.consultation-room {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden;
  z-index: 1000;
}

// Recording indicator styles
.recording-duration {
  font-size: 0.8rem;
  font-weight: bold;
  color: #fff;
}

.btn.btn-danger .recording-duration {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

// Recording status indicator
.recording-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(220, 53, 69, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1000;

  .recording-dot {
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 50%;
    animation: pulse 1s infinite;
  }
}

// Loading and Error Overlays
.connecting-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.connecting-content,
.error-content {
  text-align: center;
  color: white;
  
  .spinner-border {
    width: 3rem;
    height: 3rem;
  }
  
  i.fa-exclamation-triangle {
    font-size: 3rem;
  }
}

// Video Container
.video-container {
  position: relative;
  width: 100%;
  height: 100%;
}

// Remote Video (Main)
.remote-video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #1a1a1a;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// Local Video (Picture-in-Picture)
.local-video-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 240px;
  height: 180px;
  background: #333;
  border-radius: 12px;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
  z-index: 100;
  transition: all 0.3s ease;
  
  &.screen-sharing {
    width: 180px;
    height: 135px;
  }
}

.local-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// Video Placeholders
.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #2a2a2a;
  color: #888;
  
  &.remote-placeholder {
    .placeholder-content {
      text-align: center;
      
      i {
        font-size: 4rem;
        margin-bottom: 1rem;
      }
      
      p {
        font-size: 1.2rem;
        margin: 0;
      }
    }
  }
  
  &.local-placeholder {
    i {
      font-size: 2rem;
    }
  }
}

// Video Status Indicators
.video-status {
  position: absolute;
  bottom: 8px;
  left: 8px;
  display: flex;
  gap: 4px;
}

.status-indicator {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  
  &.video-off,
  &.audio-off {
    background: rgba(220, 53, 69, 0.9);
  }
}

// Top Bar
.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  z-index: 200;
  
  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

.consultation-info {
  h6 {
    margin: 0;
    font-weight: 600;
  }
  
  small {
    opacity: 0.8;
  }
}

.top-actions {
  display: flex;
  gap: 8px;
  
  .btn {
    border-radius: 8px;
    
    &.active {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

// Bottom Controls
.bottom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 200;
  
  &.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

.control-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex: 1;
}

.control-btn {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.1);
  }
  
  &.btn-success {
    background: #198754;
    border-color: #198754;
  }
  
  &.btn-danger {
    background: #dc3545;
    border-color: #dc3545;
  }
  
  &.btn-primary {
    background: #0d6efd;
    border-color: #0d6efd;
  }
  
  &.btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    
    &.active {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 0.9rem;
  
  .status-text {
    opacity: 0.8;
  }
  
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6c757d;
    
    &.connected {
      background: #198754;
    }
    
    &.connecting {
      background: #ffc107;
      animation: pulse 1.5s ease-in-out infinite;
    }
    
    &.error {
      background: #dc3545;
    }
  }
}

// Side Panels
.participants-panel,
.chat-panel {
  position: absolute;
  top: 0;
  right: -350px;
  width: 350px;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  transition: right 0.3s ease;
  z-index: 300;
  
  &.open {
    right: 0;
  }
}

.panel-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  
  h6 {
    margin: 0;
    font-weight: 600;
  }
}

.panel-content {
  padding: 20px;
  height: calc(100% - 80px);
  overflow-y: auto;
}

// Participants
.participant-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  
  &:last-child {
    border-bottom: none;
  }
}

.participant-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.participant-info {
  flex: 1;
}

.participant-name {
  font-weight: 600;
  margin-bottom: 2px;
}

.participant-role {
  font-size: 0.85rem;
  opacity: 0.7;
}

.participant-status {
  i {
    font-size: 0.7rem;
  }
}

// Chat
.messages-container {
  height: calc(100% - 60px);
  overflow-y: auto;
  margin-bottom: 20px;
}

.message-item {
  margin-bottom: 16px;
  
  &.own-message {
    text-align: right;
    
    .message-content {
      background: #0d6efd;
      margin-left: 40px;
    }
  }
  
  &:not(.own-message) {
    .message-content {
      background: rgba(255, 255, 255, 0.1);
      margin-right: 40px;
    }
  }
}

.message-content {
  display: inline-block;
  padding: 12px 16px;
  border-radius: 12px;
  color: white;
  max-width: 80%;
}

.message-text {
  margin-bottom: 4px;
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
}

.no-messages {
  text-align: center;
  padding: 40px 20px;
  color: #888;
  
  i {
    font-size: 2rem;
    margin-bottom: 12px;
  }
}

.message-input {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  
  .input-group {
    .form-control {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
      
      &:focus {
        background: rgba(255, 255, 255, 0.15);
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        color: white;
      }
    }
  }
}

// Animations
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// Responsive Design
@media (max-width: 768px) {
  .local-video-container {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }
  
  .participants-panel,
  .chat-panel {
    width: 100%;
    right: -100%;
  }
  
  .control-buttons {
    gap: 8px;
  }
  
  .control-btn {
    width: 45px;
    height: 45px;
    font-size: 1rem;
  }
  
  .top-bar,
  .bottom-controls {
    padding: 15px;
  }
}
