(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[572],{5538:(v,E,t)=>{"use strict";t.d(E,{m:()=>x});var u=t(1626),h=t(4412),i=t(3794),a=t(8810),l=t(8141),o=t(9437),r=t(2869),m=t(25),f=t.n(m),g=t(5312),d=t(6276),p=t(8010),b=t(5567);let x=(()=>{class k{constructor(C,S,e){this.http=C,this.authService=S,this.notificationService=e,this.apiUrl=`${g.c.apiUrl}/chats`,this.wsUrl=`${g.c.apiUrl}/ws`,this.stompClient=null,this.connectionStatusSubject=new h.t(!1),this.messageSubject=new i.B,this.typingSubject=new i.B,this.chatsSubject=new h.t([]),this.messageStatusSubject=new i.B,this.messageReactionSubject=new i.B,this.connectionStatus$=this.connectionStatusSubject.asObservable(),this.messages$=this.messageSubject.asObservable(),this.typing$=this.typingSubject.asObservable(),this.chats$=this.chatsSubject.asObservable(),this.messageStatus$=this.messageStatusSubject.asObservable(),this.messageReactions$=this.messageReactionSubject.asObservable(),this.reconnectAttempts=0,this.maxReconnectAttempts=g.c.websocket?.maxReconnectAttempts||5,this.initializeWebSocketConnection()}initializeWebSocketConnection(){this.authService.isAuthenticated()&&this.connect(),this.authService.currentUser$.subscribe(C=>{C?this.connect():this.disconnect()})}connect(){if(this.stompClient?.connected)return void console.log("WebSocket already connected");const C=this.authService.getToken();if(C){console.log("Connecting to WebSocket at:",this.wsUrl),console.log("Authentication token present:",!!C),this.stompClient=new r.K({webSocketFactory:()=>(console.log("Creating SockJS connection to:",this.wsUrl),new(f())(this.wsUrl)),connectHeaders:{Authorization:`Bearer ${C}`},debug:S=>{console.log("STOMP Debug:",S)},heartbeatIncoming:g.c.websocket?.heartbeatIncoming||25e3,heartbeatOutgoing:g.c.websocket?.heartbeatOutgoing||25e3,onConnect:S=>{this.connectionStatusSubject.next(!0),this.reconnectAttempts=0,console.log("\u2705 WebSocket connected successfully"),console.log("Connection frame:",S),console.log("Session ID:",S.headers.session),this.subscribeToUserChannels()},onWebSocketClose:S=>{this.connectionStatusSubject.next(!1),console.log("\u274c WebSocket connection closed:",S),this.handleReconnection("WebSocket closed")},onStompError:S=>{console.error("\u274c STOMP error:",S),console.error("STOMP error headers:",S.headers),console.error("STOMP error body:",S.body),this.connectionStatusSubject.next(!1),this.handleReconnection("STOMP error")},onWebSocketError:S=>{console.error("\u274c WebSocket error:",S),this.connectionStatusSubject.next(!1),this.handleReconnection("WebSocket error")}});try{this.stompClient.activate(),console.log("WebSocket activation initiated...")}catch(S){console.error("Failed to activate WebSocket client:",S),this.connectionStatusSubject.next(!1)}}else console.warn("No authentication token available for WebSocket connection")}handleReconnection(C){if(this.reconnectAttempts>=this.maxReconnectAttempts)return void console.error(`\u274c Max reconnection attempts (${this.maxReconnectAttempts}) reached. Giving up.`);if(!this.authService.isAuthenticated())return void console.log("User not authenticated, skipping reconnection");this.reconnectAttempts++;const S=g.c.websocket?.reconnectInterval||3e3;console.log(`\u{1f504} Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts}) after ${C} in ${S}ms...`),setTimeout(()=>{this.authService.isAuthenticated()&&this.connect()},S)}subscribeToUserChannels(){!this.stompClient?.connected||!this.authService.getCurrentUser()||this.stompClient.subscribe("/user/queue/errors",S=>{console.error("WebSocket error:",S.body)})}subscribeToChatMessages(C){if(this.stompClient?.connected)this.performChatSubscription(C);else{console.log("WebSocket not connected, waiting for connection...");const S=this.connectionStatus$.subscribe(e=>{e&&this.stompClient?.connected&&(console.log("WebSocket connected, subscribing to chat:",C),this.performChatSubscription(C),S.unsubscribe())})}}performChatSubscription(C){this.stompClient?.connected?(console.log("Subscribing to chat messages for chat:",C),this.stompClient.subscribe(`/topic/chat/${C}`,S=>{console.log("Received message:",S.body);const e=JSON.parse(S.body);this.messageSubject.next(e);const c=this.authService.getCurrentUser();e.sender.id!==c?.id&&this.notificationService.addMessageNotification(e.sender,e.content,C)}),this.stompClient.subscribe(`/topic/chat/${C}/typing`,S=>{const e=JSON.parse(S.body);this.typingSubject.next(e)}),this.stompClient.subscribe(`/topic/chat/${C}/status`,S=>{const e=JSON.parse(S.body);this.messageStatusSubject.next(e)}),this.stompClient.subscribe(`/topic/chat/${C}/reactions`,S=>{const e=JSON.parse(S.body);this.messageReactionSubject.next(e)}),console.log("Successfully subscribed to chat:",C)):console.error("Cannot subscribe: WebSocket not connected")}sendMessage(C,S){if(console.log("Attempting to send message:",{chatId:C,content:S,connected:this.stompClient?.connected}),!this.stompClient?.connected)throw console.error("WebSocket not connected, cannot send message"),new Error("WebSocket not connected");const e=this.authService.getToken();if(!e)throw console.error("No authentication token available"),new Error("No authentication token");const c={chatId:C,content:S};console.log("Publishing message:",c);try{this.stompClient.publish({destination:`/app/chat/${C}/send`,body:JSON.stringify(c),headers:{Authorization:`Bearer ${e}`}}),console.log("Message published successfully")}catch(n){throw console.error("Failed to publish message:",n),n}}sendTypingNotification(C,S){if(!this.stompClient?.connected)return;const e=this.authService.getToken();e&&this.stompClient.publish({destination:`/app/chat/${C}/typing`,body:S?"typing":"stopped",headers:{Authorization:`Bearer ${e}`}})}forceConnect(){this.connect()}disconnect(){this.stompClient&&(this.stompClient.deactivate(),this.connectionStatusSubject.next(!1))}createOrGetChat(C){const S={participantId:C};return console.log("Creating chat with:",{apiUrl:this.apiUrl,request:S,participantId:C,participantIdType:typeof C,token:this.authService.getToken(),currentUser:this.authService.getCurrentUser(),httpOptions:this.getHttpOptions()}),C&&"number"==typeof C?this.http.post(this.apiUrl,S,this.getHttpOptions()).pipe((0,l.M)(e=>{console.log("Chat created/retrieved successfully:",e)}),(0,o.W)(e=>(console.error("Error creating/getting chat:",e),console.error("Error details:",{status:e.status,statusText:e.statusText,message:e.message,error:e.error,url:e.url}),(0,a.$)(()=>e)))):(console.error("Invalid participantId:",C),(0,a.$)(()=>new Error("Invalid participant ID")))}getUserChats(){return this.http.get(this.apiUrl,this.getHttpOptions())}getChatMessages(C,S=0,e=50){const c={page:S.toString(),size:e.toString()};return this.http.get(`${this.apiUrl}/${C}/messages`,{...this.getHttpOptions(),params:c})}markMessagesAsRead(C){return this.http.put(`${this.apiUrl}/${C}/read`,{},this.getHttpOptions())}markMessageAsRead(C){if(!this.stompClient?.connected)return;const S=this.authService.getToken();S&&this.stompClient.publish({destination:`/app/message/${C}/read`,headers:{Authorization:`Bearer ${S}`}})}addMessageReaction(C,S){if(!this.stompClient?.connected)return;const e=this.authService.getToken();e&&this.stompClient.publish({destination:`/app/message/${C}/react`,body:JSON.stringify({reaction:S}),headers:{Authorization:`Bearer ${e}`}})}sendMessageWithAttachment(C,S,e){const c=new FormData;return c.append("content",S),c.append("file",e),this.http.post(`${this.apiUrl}/${C}/messages/attachment`,c,{headers:new u.Lr({Authorization:`Bearer ${this.authService.getToken()}`})})}replyToMessage(C,S,e){if(!this.stompClient?.connected)throw new Error("WebSocket not connected");const c=this.authService.getToken();if(!c)throw new Error("No authentication token");this.stompClient.publish({destination:`/app/chat/${C}/reply`,body:JSON.stringify({chatId:C,content:S,replyToMessageId:e}),headers:{Authorization:`Bearer ${c}`}})}loadUserChats(){this.getUserChats().subscribe({next:C=>{this.chatsSubject.next(C)},error:C=>{console.error("Failed to load chats:",C)}})}getHttpOptions(){const C=this.authService.getToken();return{headers:new u.Lr({"Content-Type":"application/json",Authorization:`Bearer ${C}`})}}static{this.\u0275fac=function(S){return new(S||k)(d.KVO(u.Qq),d.KVO(p.u),d.KVO(b.J))}}static{this.\u0275prov=d.jDH({token:k,factory:k.\u0275fac,providedIn:"root"})}}return k})()},1993:v=>{v.exports="function"==typeof Object.create?function(t,u){u&&(t.super_=u,t.prototype=Object.create(u.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:function(t,u){if(u){t.super_=u;var h=function(){};h.prototype=u.prototype,t.prototype=new h,t.prototype.constructor=t}}},8629:(v,E)=>{"use strict";var t=Object.prototype.hasOwnProperty;function h(o){try{return decodeURIComponent(o.replace(/\+/g," "))}catch{return null}}function i(o){try{return encodeURIComponent(o)}catch{return null}}E.stringify=function l(o,r){r=r||"";var f,g,m=[];for(g in"string"!=typeof r&&(r="?"),o)if(t.call(o,g)){if(!(f=o[g])&&(null==f||isNaN(f))&&(f=""),g=i(g),f=i(f),null===g||null===f)continue;m.push(g+"="+f)}return m.length?r+m.join("&"):""},E.parse=function a(o){for(var f,r=/([^=?#&]+)=?([^&]*)/g,m={};f=r.exec(o);){var g=h(f[1]),d=h(f[2]);null===g||null===d||g in m||(m[g]=d)}return m}},5852:v=>{"use strict";v.exports=function(t,u){if(u=u.split(":")[0],!(t=+t))return!1;switch(u){case"http":case"ws":return 80!==t;case"https":case"wss":return 443!==t;case"ftp":return 21!==t;case"gopher":return 70!==t;case"file":return!1}return 0!==t}},5877:(v,E,t)=>{"use strict";t.d(E,{Y:()=>o});var u=t(3888),h=t(1985),i=t(9470);function o(r=0,m=u.E){return r<0&&(r=0),function l(r=0,m,f=u.b){let g=-1;return null!=m&&((0,i.m)(m)?f=m:g=m),new h.c(d=>{let p=function a(r){return r instanceof Date&&!isNaN(r)}(r)?+r-f.now():r;p<0&&(p=0);let b=0;return f.schedule(function(){d.closed||(d.next(b++),0<=g?this.schedule(void 0,g):d.complete())},p)})}(r,r,m)}},3888:(v,E,t)=>{"use strict";t.d(E,{b:()=>g,E:()=>f});var u=t(8359);class h extends u.yU{constructor(p,b){super()}schedule(p,b=0){return this}}const i={setInterval(d,p,...b){const{delegate:x}=i;return x?.setInterval?x.setInterval(d,p,...b):setInterval(d,p,...b)},clearInterval(d){const{delegate:p}=i;return(p?.clearInterval||clearInterval)(d)},delegate:void 0};var a=t(7908);const o={now:()=>(o.delegate||Date).now(),delegate:void 0};class r{constructor(p,b=r.now){this.schedulerActionCtor=p,this.now=b}schedule(p,b=0,x){return new this.schedulerActionCtor(this,p).schedule(x,b)}}r.now=o.now;const f=new class m extends r{constructor(p,b=r.now){super(p,b),this.actions=[],this._active=!1}flush(p){const{actions:b}=this;if(this._active)return void b.push(p);let x;this._active=!0;do{if(x=p.execute(p.state,p.delay))break}while(p=b.shift());if(this._active=!1,x){for(;p=b.shift();)p.unsubscribe();throw x}}}(class l extends h{constructor(p,b){super(p,b),this.scheduler=p,this.work=b,this.pending=!1}schedule(p,b=0){var x;if(this.closed)return this;this.state=p;const k=this.id,A=this.scheduler;return null!=k&&(this.id=this.recycleAsyncId(A,k,b)),this.pending=!0,this.delay=b,this.id=null!==(x=this.id)&&void 0!==x?x:this.requestAsyncId(A,this.id,b),this}requestAsyncId(p,b,x=0){return i.setInterval(p.flush.bind(p,this),x)}recycleAsyncId(p,b,x=0){if(null!=x&&this.delay===x&&!1===this.pending)return b;null!=b&&i.clearInterval(b)}execute(p,b){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;const x=this._execute(p,b);if(x)return x;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(p,b){let k,x=!1;try{this.work(p)}catch(A){x=!0,k=A||new Error("Scheduled action threw falsy error")}if(x)return this.unsubscribe(),k}unsubscribe(){if(!this.closed){const{id:p,scheduler:b}=this,{actions:x}=b;this.work=this.state=this.scheduler=null,this.pending=!1,(0,a.o)(x,this),null!=p&&(this.id=this.recycleAsyncId(b,p,null)),this.delay=null,super.unsubscribe()}}}),g=f},25:(v,E,t)=>{"use strict";var u=t(1049);v.exports=t(9084)(u),"_sockjs_onload"in global&&setTimeout(global._sockjs_onload,1)},4542:(v,E,t)=>{"use strict";var u=t(1993),h=t(6886);function i(){h.call(this),this.initEvent("close",!1,!1),this.wasClean=!1,this.code=0,this.reason=""}u(i,h),v.exports=i},6964:(v,E,t)=>{"use strict";var u=t(1993),h=t(8551);function i(){h.call(this)}u(i,h),i.prototype.removeAllListeners=function(a){a?delete this._listeners[a]:this._listeners={}},i.prototype.once=function(a,l){var o=this,r=!1;this.on(a,function m(){o.removeListener(a,m),r||(r=!0,l.apply(this,arguments))})},i.prototype.emit=function(){var l=this._listeners[arguments[0]];if(l){for(var o=arguments.length,r=new Array(o-1),m=1;m<o;m++)r[m-1]=arguments[m];for(var f=0;f<l.length;f++)l[f].apply(this,r)}},i.prototype.on=i.prototype.addListener=h.prototype.addEventListener,i.prototype.removeListener=h.prototype.removeEventListener,v.exports.b=i},6886:v=>{"use strict";function E(t){this.type=t}E.prototype.initEvent=function(t,u,h){return this.type=t,this.bubbles=u,this.cancelable=h,this.timeStamp=+new Date,this},E.prototype.stopPropagation=function(){},E.prototype.preventDefault=function(){},E.CAPTURING_PHASE=1,E.AT_TARGET=2,E.BUBBLING_PHASE=3,v.exports=E},8551:v=>{"use strict";function E(){this._listeners={}}E.prototype.addEventListener=function(t,u){t in this._listeners||(this._listeners[t]=[]);var h=this._listeners[t];-1===h.indexOf(u)&&(h=h.concat([u])),this._listeners[t]=h},E.prototype.removeEventListener=function(t,u){var h=this._listeners[t];if(h){var i=h.indexOf(u);if(-1!==i)return void(h.length>1?this._listeners[t]=h.slice(0,i).concat(h.slice(i+1)):delete this._listeners[t])}},E.prototype.dispatchEvent=function(){var t=arguments[0],u=t.type,h=1===arguments.length?[t]:Array.apply(null,arguments);if(this["on"+u]&&this["on"+u].apply(this,h),u in this._listeners)for(var i=this._listeners[u],a=0;a<i.length;a++)i[a].apply(this,h)},v.exports=E},5714:(v,E,t)=>{"use strict";var u=t(1993),h=t(6886);function i(a){h.call(this),this.initEvent("message",!1,!1),this.data=a}u(i,h),v.exports=i},183:(v,E,t)=>{"use strict";var u=t(9915);function h(i){this._transport=i,i.on("message",this._transportMessage.bind(this)),i.on("close",this._transportClose.bind(this))}h.prototype._transportClose=function(i,a){u.postMessage("c",JSON.stringify([i,a]))},h.prototype._transportMessage=function(i){u.postMessage("t",i)},h.prototype._send=function(i){this._transport.send(i)},h.prototype._close=function(){this._transport.close(),this._transport.removeAllListeners()},v.exports=h},7958:(v,E,t)=>{"use strict";var u=t(2630),h=t(1059),i=t(183),a=t(7094),l=t(9915),o=t(6);v.exports=function(m,f){var d,g={};f.forEach(function(p){p.facadeTransport&&(g[p.facadeTransport.transportName]=p.facadeTransport)}),g[a.transportName]=a,m.bootstrap_iframe=function(){var p;l.currentWindowId=o.hash.slice(1),h.attachEvent("message",function(x){if(x.source===parent&&(typeof d>"u"&&(d=x.origin),x.origin===d)){var k;try{k=JSON.parse(x.data)}catch{return}if(k.windowId===l.currentWindowId)switch(k.type){case"s":var A;try{A=JSON.parse(k.data)}catch{break}var C=A[0],S=A[1],e=A[2],c=A[3];if(C!==m.version)throw new Error('Incompatible SockJS! Main site uses: "'+C+'", the iframe: "'+m.version+'".');if(!u.isOriginEqual(e,o.href)||!u.isOriginEqual(c,o.href))throw new Error("Can't connect to different domain from within an iframe. ("+o.href+", "+e+", "+c+")");p=new i(new g[S](e,c));break;case"m":p._send(k.data);break;case"c":p&&p._close(),p=null}}}),l.postMessage("s")}}},4036:(v,E,t)=>{"use strict";var u=t(6964).b,h=t(1993),i=t(7824);function l(o,r){u.call(this);var m=this,f=+new Date;this.xo=new r("GET",o),this.xo.once("finish",function(g,d){var p,b;if(200===g){if(b=+new Date-f,d)try{p=JSON.parse(d)}catch{}i.isObject(p)||(p={})}m.emit("finish",p,b),m.removeAllListeners()})}h(l,u),l.prototype.close=function(){this.removeAllListeners(),this.xo.close()},v.exports=l},7094:(v,E,t)=>{"use strict";var u=t(1993),h=t(6964).b,i=t(9781),a=t(4036);function l(o){var r=this;h.call(this),this.ir=new a(o,i),this.ir.once("finish",function(m,f){r.ir=null,r.emit("message",JSON.stringify([m,f]))})}u(l,h),l.transportName="iframe-info-receiver",l.prototype.close=function(){this.ir&&(this.ir.close(),this.ir=null),this.removeAllListeners()},v.exports=l},4934:(v,E,t)=>{"use strict";var u=t(6964).b,h=t(1993),i=t(1059),a=t(6531),l=t(7094);function r(m,f){var g=this;u.call(this);var d=function(){var p=g.ifr=new a(l.transportName,f,m);p.once("message",function(b){if(b){var x;try{x=JSON.parse(b)}catch{return g.emit("finish"),void g.close()}g.emit("finish",x[0],x[1])}g.close()}),p.once("close",function(){g.emit("finish"),g.close()})};global.document.body?d():i.attachEvent("load",d)}h(r,u),r.enabled=function(){return a.enabled()},r.prototype.close=function(){this.ifr&&this.ifr.close(),this.removeAllListeners(),this.ifr=null},v.exports=r},4779:(v,E,t)=>{"use strict";var u=t(6964).b,h=t(1993),i=t(2630),a=t(3103),l=t(53),o=t(9781),r=t(1749),m=t(4934),f=t(4036);function d(p,b){var x=this;u.call(this),setTimeout(function(){x.doXhr(p,b)},0)}h(d,u),d._getReceiver=function(p,b,x){return x.sameOrigin?new f(b,o):l.enabled?new f(b,l):a.enabled&&x.sameScheme?new f(b,a):m.enabled()?new m(p,b):new f(b,r)},d.prototype.doXhr=function(p,b){var x=this,k=i.addPath(p,"/info");this.xo=d._getReceiver(p,k,b),this.timeoutRef=setTimeout(function(){x._cleanup(!1),x.emit("finish")},d.timeout),this.xo.once("finish",function(A,C){x._cleanup(!0),x.emit("finish",A,C)})},d.prototype._cleanup=function(p){clearTimeout(this.timeoutRef),this.timeoutRef=null,!p&&this.xo&&this.xo.close(),this.xo=null},d.prototype.close=function(){this.removeAllListeners(),this._cleanup(!1)},d.timeout=8e3,v.exports=d},6:v=>{"use strict";v.exports=global.location||{origin:"http://localhost:80",protocol:"http:",host:"localhost",port:80,href:"http://localhost/",hash:""}},9084:(v,E,t)=>{"use strict";t(7015);var S,u=t(3711),h=t(1993),i=t(6944),a=t(196),l=t(2630),o=t(1059),r=t(2850),m=t(7824),f=t(9427),g=t(9697),d=t(6886),p=t(8551),b=t(6),x=t(4542),k=t(5714),A=t(4779);function e(n,s,y){if(!(this instanceof e))return new e(n,s,y);if(arguments.length<1)throw new TypeError("Failed to construct 'SockJS: 1 argument required, but only 0 present");p.call(this),this.readyState=e.CONNECTING,this.extensions="",this.protocol="",(y=y||{}).protocols_whitelist&&g.warn("'protocols_whitelist' is DEPRECATED. Use 'transports' instead."),this._transportsWhitelist=y.transports,this._transportOptions=y.transportOptions||{},this._timeout=y.timeout||0;var O=y.sessionId||8;if("function"==typeof O)this._generateSessionId=O;else{if("number"!=typeof O)throw new TypeError("If sessionId is used in the options, it needs to be a number or a function.");this._generateSessionId=function(){return i.string(O)}}this._server=y.server||i.numberString(1e3);var w=new u(n);if(!w.host||!w.protocol)throw new SyntaxError("The URL '"+n+"' is invalid");if(w.hash)throw new SyntaxError("The URL must not contain a fragment");if("http:"!==w.protocol&&"https:"!==w.protocol)throw new SyntaxError("The URL's scheme must be either 'http:' or 'https:'. '"+w.protocol+"' is not allowed.");if("https:"===b.protocol&&"https:"!==w.protocol&&!l.isLoopbackAddr(w.hostname))throw new Error("SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS");s?Array.isArray(s)||(s=[s]):s=[];var R=s.sort();R.forEach(function(N,T){if(!N)throw new SyntaxError("The protocols entry '"+N+"' is invalid.");if(T<R.length-1&&N===R[T+1])throw new SyntaxError("The protocols entry '"+N+"' is duplicated.")});var U=l.getOrigin(b.href);this._origin=U?U.toLowerCase():null,w.set("pathname",w.pathname.replace(/\/+$/,"")),this.url=w.href,this._urlInfo={nullOrigin:!f.hasDomain(),sameOrigin:l.isOriginEqual(this.url,b.href),sameScheme:l.isSchemeEqual(this.url,b.href)},this._ir=new A(this.url,this._urlInfo),this._ir.once("finish",this._receiveInfo.bind(this))}function c(n){return 1e3===n||n>=3e3&&n<=4999}h(e,p),e.prototype.close=function(n,s){if(n&&!c(n))throw new Error("InvalidAccessError: Invalid code");if(s&&s.length>123)throw new SyntaxError("reason argument has an invalid length");this.readyState!==e.CLOSING&&this.readyState!==e.CLOSED&&this._close(n||1e3,s||"Normal closure",!0)},e.prototype.send=function(n){if("string"!=typeof n&&(n=""+n),this.readyState===e.CONNECTING)throw new Error("InvalidStateError: The connection has not been established yet");this.readyState===e.OPEN&&this._transport.send(a.quote(n))},e.version=t(8469),e.CONNECTING=0,e.OPEN=1,e.CLOSING=2,e.CLOSED=3,e.prototype._receiveInfo=function(n,s){if(this._ir=null,n){this._rto=this.countRTO(s),this._transUrl=n.base_url?n.base_url:this.url,n=m.extend(n,this._urlInfo);var y=S.filterToEnabled(this._transportsWhitelist,n);this._transports=y.main,this._connect()}else this._close(1002,"Cannot connect to server")},e.prototype._connect=function(){for(var n=this._transports.shift();n;n=this._transports.shift()){if(n.needBody&&(!global.document.body||typeof global.document.readyState<"u"&&"complete"!==global.document.readyState&&"interactive"!==global.document.readyState))return this._transports.unshift(n),void o.attachEvent("load",this._connect.bind(this));var s=Math.max(this._timeout,this._rto*n.roundTrips||5e3);this._transportTimeoutId=setTimeout(this._transportTimeout.bind(this),s);var w=new n(l.addPath(this._transUrl,"/"+this._server+"/"+this._generateSessionId()),this._transUrl,this._transportOptions[n.transportName]);return w.on("message",this._transportMessage.bind(this)),w.once("close",this._transportClose.bind(this)),w.transportName=n.transportName,void(this._transport=w)}this._close(2e3,"All transports failed",!1)},e.prototype._transportTimeout=function(){this.readyState===e.CONNECTING&&(this._transport&&this._transport.close(),this._transportClose(2007,"Transport timed out"))},e.prototype._transportMessage=function(n){var w,s=this,y=n.slice(0,1),O=n.slice(1);switch(y){case"o":return void this._open();case"h":return void this.dispatchEvent(new d("heartbeat"))}if(O)try{w=JSON.parse(O)}catch{}if(!(typeof w>"u"))switch(y){case"a":Array.isArray(w)&&w.forEach(function(I){s.dispatchEvent(new k(I))});break;case"m":this.dispatchEvent(new k(w));break;case"c":Array.isArray(w)&&2===w.length&&this._close(w[0],w[1],!0)}},e.prototype._transportClose=function(n,s){this._transport&&(this._transport.removeAllListeners(),this._transport=null,this.transport=null),c(n)||2e3===n||this.readyState!==e.CONNECTING?this._close(n,s):this._connect()},e.prototype._open=function(){this.readyState===e.CONNECTING?(this._transportTimeoutId&&(clearTimeout(this._transportTimeoutId),this._transportTimeoutId=null),this.readyState=e.OPEN,this.transport=this._transport.transportName,this.dispatchEvent(new d("open"))):this._close(1006,"Server lost session")},e.prototype._close=function(n,s,y){var O=!1;if(this._ir&&(O=!0,this._ir.close(),this._ir=null),this._transport&&(this._transport.close(),this._transport=null,this.transport=null),this.readyState===e.CLOSED)throw new Error("InvalidStateError: SockJS has already been closed");this.readyState=e.CLOSING,setTimeout(function(){this.readyState=e.CLOSED,O&&this.dispatchEvent(new d("error"));var w=new x("close");w.wasClean=y||!1,w.code=n||1e3,w.reason=s,this.dispatchEvent(w),this.onmessage=this.onclose=this.onerror=null}.bind(this),0)},e.prototype.countRTO=function(n){return n>100?4*n:300+n},v.exports=function(n){return S=r(n),t(7958)(e,n),e}},7015:()=>{"use strict";var m,v=Array.prototype,E=Object.prototype,t=Function.prototype,u=String.prototype,h=v.slice,i=E.toString,a=function(n){return"[object Function]"===E.toString.call(n)},o=function(s){return"[object String]"===i.call(s)},r=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{}),!0}catch{return!1}}();m=r?function(n,s,y,O){!O&&s in n||Object.defineProperty(n,s,{configurable:!0,enumerable:!1,writable:!0,value:y})}:function(n,s,y,O){!O&&s in n||(n[s]=y)};var f=function(n,s,y){for(var O in s)E.hasOwnProperty.call(s,O)&&m(n,O,s[O],y)},g=function(n){if(null==n)throw new TypeError("can't convert "+n+" to object");return Object(n)};function b(){}f(t,{bind:function(s){var y=this;if(!a(y))throw new TypeError("Function.prototype.bind called on incompatible "+y);for(var O=h.call(arguments,1),I=Math.max(0,y.length-O.length),R=[],U=0;U<I;U++)R.push("$"+U);var N=Function("binder","return function ("+R.join(",")+"){ return binder.apply(this, arguments); }")(function(){if(this instanceof N){var T=y.apply(this,O.concat(h.call(arguments)));return Object(T)===T?T:this}return y.apply(s,O.concat(h.call(arguments)))});return y.prototype&&(b.prototype=y.prototype,N.prototype=new b,b.prototype=null),N}}),f(Array,{isArray:function(s){return"[object Array]"===i.call(s)}});var s,y,O,x=Object("a"),k="a"!==x[0]||!(0 in x);f(v,{forEach:function(s){var y=g(this),O=k&&o(this)?this.split(""):y,w=arguments[1],I=-1,R=O.length>>>0;if(!a(s))throw new TypeError;for(;++I<R;)I in O&&s.call(w,O[I],I,y)}},(y=!0,O=!0,(s=v.forEach)&&(s.call("foo",function(w,I,R){"object"!=typeof R&&(y=!1)}),s.call([1],function(){O="string"==typeof this},"x")),!(s&&y&&O)));var C=Array.prototype.indexOf&&-1!==[0,1].indexOf(1,2);f(v,{indexOf:function(s){var y=k&&o(this)?this.split(""):g(this),O=y.length>>>0;if(!O)return-1;var w=0;for(arguments.length>1&&(w=function d(n){var s=+n;return s!=s?s=0:0!==s&&s!==1/0&&s!==-1/0&&(s=(s>0||-1)*Math.floor(Math.abs(s))),s}(arguments[1])),w=w>=0?w:Math.max(0,O+w);w<O;w++)if(w in y&&y[w]===s)return w;return-1}},C);var n,S=u.split;2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||".".split(/()()/).length>1?(n=void 0===/()??/.exec("")[1],u.split=function(s,y){var O=this;if(void 0===s&&0===y)return[];if("[object RegExp]"!==i.call(s))return S.call(this,s,y);var U,N,T,j,w=[],I=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.extended?"x":"")+(s.sticky?"y":""),R=0;for(s=new RegExp(s.source,I+"g"),O+="",n||(U=new RegExp("^"+s.source+"$(?!\\s)",I)),y=void 0===y?-1>>>0:function p(n){return n>>>0}(y);(N=s.exec(O))&&!((T=N.index+N[0].length)>R&&(w.push(O.slice(R,N.index)),!n&&N.length>1&&N[0].replace(U,function(){for(var L=1;L<arguments.length-2;L++)void 0===arguments[L]&&(N[L]=void 0)}),N.length>1&&N.index<O.length&&v.push.apply(w,N.slice(1)),j=N[0].length,R=T,w.length>=y));)s.lastIndex===N.index&&s.lastIndex++;return R===O.length?(j||!s.test(""))&&w.push(""):w.push(O.slice(R)),w.length>y?w.slice(0,y):w}):"0".split(void 0,0).length&&(u.split=function(s,y){return void 0===s&&0===y?[]:S.call(this,s,y)});var e=u.substr;f(u,{substr:function(s,y){return e.call(this,s<0&&(s=this.length+s)<0?0:s,y)}},"".substr&&"b"!=="0b".substr(-1))},1049:(v,E,t)=>{"use strict";v.exports=[t(1240),t(9720),t(3356),t(6178),t(5568)(t(6178)),t(8940),t(5568)(t(8940)),t(5543),t(5483),t(5568)(t(5543)),t(9143)]},5067:(v,E,t)=>{"use strict";var u=t(6964).b,h=t(1993),i=t(1059),a=t(2630),l=global.XMLHttpRequest;function r(g,d,p,b){var x=this;u.call(this),setTimeout(function(){x._start(g,d,p,b)},0)}h(r,u),r.prototype._start=function(g,d,p,b){var x=this;try{this.xhr=new l}catch{}if(!this.xhr)return this.emit("finish",0,"no xhr support"),void this._cleanup();d=a.addQuery(d,"t="+ +new Date),this.unloadRef=i.unloadAdd(function(){x._cleanup(!0)});try{this.xhr.open(g,d,!0),this.timeout&&"timeout"in this.xhr&&(this.xhr.timeout=this.timeout,this.xhr.ontimeout=function(){x.emit("finish",0,""),x._cleanup(!1)})}catch(A){return this.emit("finish",0,""),void this._cleanup(!1)}if((!b||!b.noCredentials)&&r.supportsCORS&&(this.xhr.withCredentials=!0),b&&b.headers)for(var k in b.headers)this.xhr.setRequestHeader(k,b.headers[k]);this.xhr.onreadystatechange=function(){if(x.xhr){var C,S,A=x.xhr;switch(A.readyState){case 3:try{S=A.status,C=A.responseText}catch{}1223===S&&(S=204),200===S&&C&&C.length>0&&x.emit("chunk",S,C);break;case 4:1223===(S=A.status)&&(S=204),(12005===S||12029===S)&&(S=0),x.emit("finish",S,A.responseText),x._cleanup(!1)}}};try{x.xhr.send(p)}catch{x.emit("finish",0,""),x._cleanup(!1)}},r.prototype._cleanup=function(g){if(this.xhr){if(this.removeAllListeners(),i.unloadDel(this.unloadRef),this.xhr.onreadystatechange=function(){},this.xhr.ontimeout&&(this.xhr.ontimeout=null),g)try{this.xhr.abort()}catch{}this.unloadRef=this.xhr=null}},r.prototype.close=function(){this._cleanup(!0)},r.enabled=!!l;var m=["Active"].concat("Object").join("X");!r.enabled&&m in global&&(r.enabled=!!new(l=function(){try{return new global[m]("Microsoft.XMLHTTP")}catch{return null}}));var f=!1;try{f="withCredentials"in new l}catch{}r.supportsCORS=f,v.exports=r},3521:v=>{v.exports=global.EventSource},6419:v=>{"use strict";var E=global.WebSocket||global.MozWebSocket;v.exports=E?function(u){return new E(u)}:void 0},6178:(v,E,t)=>{"use strict";var u=t(1993),h=t(6599),i=t(3024),a=t(53),l=t(3521);function o(r){if(!o.enabled())throw new Error("Transport created when disabled");h.call(this,r,"/eventsource",i,a)}u(o,h),o.enabled=function(){return!!l},o.transportName="eventsource",o.roundTrips=2,v.exports=o},8940:(v,E,t)=>{"use strict";var u=t(1993),h=t(90),i=t(9781),a=t(6599);function l(o){if(!h.enabled)throw new Error("Transport created when disabled");a.call(this,o,"/htmlfile",h,i)}u(l,a),l.enabled=function(o){return h.enabled&&o.sameOrigin},l.transportName="htmlfile",l.roundTrips=2,v.exports=l},6531:(v,E,t)=>{"use strict";var u=t(1993),h=t(6964).b,i=t(8469),a=t(2630),l=t(9915),o=t(1059),r=t(6944);function f(g,d,p){if(!f.enabled())throw new Error("Transport created when disabled");h.call(this);var b=this;this.origin=a.getOrigin(p),this.baseUrl=p,this.transUrl=d,this.transport=g,this.windowId=r.string(8);var x=a.addPath(p,"/iframe.html")+"#"+this.windowId;this.iframeObj=l.createIframe(x,function(k){b.emit("close",1006,"Unable to load an iframe ("+k+")"),b.close()}),this.onmessageCallback=this._message.bind(this),o.attachEvent("message",this.onmessageCallback)}u(f,h),f.prototype.close=function(){if(this.removeAllListeners(),this.iframeObj){o.detachEvent("message",this.onmessageCallback);try{this.postMessage("c")}catch{}this.iframeObj.cleanup(),this.iframeObj=null,this.onmessageCallback=this.iframeObj=null}},f.prototype._message=function(g){if(a.isOriginEqual(g.origin,this.origin)){var d;try{d=JSON.parse(g.data)}catch{return}if(d.windowId===this.windowId)switch(d.type){case"s":this.iframeObj.loaded(),this.postMessage("s",JSON.stringify([i,this.transport,this.transUrl,this.baseUrl]));break;case"t":this.emit("message",d.data);break;case"c":var p;try{p=JSON.parse(d.data)}catch{return}this.emit("close",p[0],p[1]),this.close()}}},f.prototype.postMessage=function(g,d){this.iframeObj.post(JSON.stringify({windowId:this.windowId,type:g,data:d||""}),this.origin)},f.prototype.send=function(g){this.postMessage("m",g)},f.enabled=function(){return l.iframeEnabled},f.transportName="iframe",f.roundTrips=2,v.exports=f},9143:(v,E,t)=>{"use strict";var u=t(1993),h=t(5222),i=t(979),a=t(1759);function l(o){if(!l.enabled())throw new Error("Transport created when disabled");h.call(this,o,"/jsonp",a,i)}u(l,h),l.enabled=function(){return!!global.document},l.transportName="jsonp-polling",l.roundTrips=1,l.needBody=!0,v.exports=l},6599:(v,E,t)=>{"use strict";var u=t(1993),h=t(2630),i=t(5222);function o(r,m,f,g){i.call(this,r,m,function l(r){return function(m,f,g){var d={};"string"==typeof f&&(d.headers={"Content-type":"text/plain"});var p=h.addPath(m,"/xhr_send"),b=new r("POST",p,f,d);return b.once("finish",function(x){if(b=null,200!==x&&204!==x)return g(new Error("http status "+x));g()}),function(){b.close(),b=null;var x=new Error("Aborted");x.code=1e3,g(x)}}}(g),f,g)}u(o,i),v.exports=o},5624:(v,E,t)=>{"use strict";var u=t(1993),h=t(6964).b;function a(l,o){h.call(this),this.sendBuffer=[],this.sender=o,this.url=l}u(a,h),a.prototype.send=function(l){this.sendBuffer.push(l),this.sendStop||this.sendSchedule()},a.prototype.sendScheduleWait=function(){var o,l=this;this.sendStop=function(){l.sendStop=null,clearTimeout(o)},o=setTimeout(function(){l.sendStop=null,l.sendSchedule()},25)},a.prototype.sendSchedule=function(){var l=this;if(this.sendBuffer.length>0){var o="["+this.sendBuffer.join(",")+"]";this.sendStop=this.sender(this.url,o,function(r){l.sendStop=null,r?(l.emit("close",r.code||1006,"Sending error: "+r),l.close()):l.sendScheduleWait()}),this.sendBuffer=[]}},a.prototype._cleanup=function(){this.removeAllListeners()},a.prototype.close=function(){this._cleanup(),this.sendStop&&(this.sendStop(),this.sendStop=null)},v.exports=a},5568:(v,E,t)=>{"use strict";var u=t(1993),h=t(6531),i=t(7824);v.exports=function(a){function l(o,r){h.call(this,a.transportName,o,r)}return u(l,h),l.enabled=function(o,r){if(!global.document)return!1;var m=i.extend({},r);return m.sameOrigin=!0,a.enabled(m)&&h.enabled()},l.transportName="iframe-"+a.transportName,l.needBody=!0,l.roundTrips=h.roundTrips+a.roundTrips-1,l.facadeTransport=a,l}},3500:(v,E,t)=>{"use strict";var u=t(1993),h=t(6964).b;function a(l,o,r){h.call(this),this.Receiver=l,this.receiveUrl=o,this.AjaxObject=r,this._scheduleReceiver()}u(a,h),a.prototype._scheduleReceiver=function(){var l=this,o=this.poll=new this.Receiver(this.receiveUrl,this.AjaxObject);o.on("message",function(r){l.emit("message",r)}),o.once("close",function(r,m){l.poll=o=null,l.pollIsClosing||("network"===m?l._scheduleReceiver():(l.emit("close",r||1006,m),l.removeAllListeners()))})},a.prototype.abort=function(){this.removeAllListeners(),this.pollIsClosing=!0,this.poll&&this.poll.abort()},v.exports=a},5222:(v,E,t)=>{"use strict";var u=t(1993),h=t(2630),i=t(5624),a=t(3500);function o(r,m,f,g,d){var p=h.addPath(r,m),b=this;i.call(this,r,f),this.poll=new a(g,p,d),this.poll.on("message",function(x){b.emit("message",x)}),this.poll.once("close",function(x,k){b.poll=null,b.emit("close",x,k),b.close()})}u(o,i),o.prototype.close=function(){i.prototype.close.call(this),this.removeAllListeners(),this.poll&&(this.poll.abort(),this.poll=null)},v.exports=o},3024:(v,E,t)=>{"use strict";var u=t(1993),h=t(6964).b,i=t(3521);function l(o){h.call(this);var r=this,m=this.es=new i(o);m.onmessage=function(f){r.emit("message",decodeURI(f.data))},m.onerror=function(f){var g=2!==m.readyState?"network":"permanent";r._cleanup(),r._close(g)}}u(l,h),l.prototype.abort=function(){this._cleanup(),this._close("user")},l.prototype._cleanup=function(){var o=this.es;o&&(o.onmessage=o.onerror=null,o.close(),this.es=null)},l.prototype._close=function(o){var r=this;setTimeout(function(){r.emit("close",null,o),r.removeAllListeners()},200)},v.exports=l},90:(v,E,t)=>{"use strict";var u=t(1993),h=t(9915),i=t(2630),a=t(6964).b,l=t(6944);function r(f){a.call(this);var g=this;h.polluteGlobalNamespace(),this.id="a"+l.string(6),f=i.addQuery(f,"c="+decodeURIComponent(h.WPrefix+"."+this.id));var d=r.htmlfileEnabled?h.createHtmlfile:h.createIframe;global[h.WPrefix][this.id]={start:function(){g.iframeObj.loaded()},message:function(p){g.emit("message",p)},stop:function(){g._cleanup(),g._close("network")}},this.iframeObj=d(f,function(){g._cleanup(),g._close("permanent")})}u(r,a),r.prototype.abort=function(){this._cleanup(),this._close("user")},r.prototype._cleanup=function(){this.iframeObj&&(this.iframeObj.cleanup(),this.iframeObj=null),delete global[h.WPrefix][this.id]},r.prototype._close=function(f){this.emit("close",null,f),this.removeAllListeners()},r.htmlfileEnabled=!1;var m=["Active"].concat("Object").join("X");if(m in global)try{r.htmlfileEnabled=!!new global[m]("htmlfile")}catch{}r.enabled=r.htmlfileEnabled||h.iframeEnabled,v.exports=r},979:(v,E,t)=>{"use strict";var u=t(9915),h=t(6944),i=t(9427),a=t(2630),l=t(1993),o=t(6964).b;function m(f){var g=this;o.call(this),u.polluteGlobalNamespace(),this.id="a"+h.string(6);var d=a.addQuery(f,"c="+encodeURIComponent(u.WPrefix+"."+this.id));global[u.WPrefix][this.id]=this._callback.bind(this),this._createScript(d),this.timeoutId=setTimeout(function(){g._abort(new Error("JSONP script loaded abnormally (timeout)"))},m.timeout)}l(m,o),m.prototype.abort=function(){if(global[u.WPrefix][this.id]){var f=new Error("JSONP user aborted read");f.code=1e3,this._abort(f)}},m.timeout=35e3,m.scriptErrorTimeout=1e3,m.prototype._callback=function(f){this._cleanup(),!this.aborting&&(f&&this.emit("message",f),this.emit("close",null,"network"),this.removeAllListeners())},m.prototype._abort=function(f){this._cleanup(),this.aborting=!0,this.emit("close",f.code,f.message),this.removeAllListeners()},m.prototype._cleanup=function(){if(clearTimeout(this.timeoutId),this.script2&&(this.script2.parentNode.removeChild(this.script2),this.script2=null),this.script){var f=this.script;f.parentNode.removeChild(f),f.onreadystatechange=f.onerror=f.onload=f.onclick=null,this.script=null}delete global[u.WPrefix][this.id]},m.prototype._scriptError=function(){var f=this;this.errorTimer||(this.errorTimer=setTimeout(function(){f.loadedOkay||f._abort(new Error("JSONP script loaded abnormally (onerror)"))},m.scriptErrorTimeout))},m.prototype._createScript=function(f){var p,g=this,d=this.script=global.document.createElement("script");if(d.id="a"+h.string(8),d.src=f,d.type="text/javascript",d.charset="UTF-8",d.onerror=this._scriptError.bind(this),d.onload=function(){g._abort(new Error("JSONP script loaded abnormally (onload)"))},d.onreadystatechange=function(){if(/loaded|closed/.test(d.readyState)){if(d&&d.htmlFor&&d.onclick){g.loadedOkay=!0;try{d.onclick()}catch{}}d&&g._abort(new Error("JSONP script loaded abnormally (onreadystatechange)"))}},typeof d.async>"u"&&global.document.attachEvent)if(i.isOpera())(p=this.script2=global.document.createElement("script")).text="try{var a = document.getElementById('"+d.id+"'); if(a)a.onerror();}catch(x){};",d.async=p.async=!1;else{try{d.htmlFor=d.id,d.event="onclick"}catch{}d.async=!0}typeof d.async<"u"&&(d.async=!0);var b=global.document.getElementsByTagName("head")[0];b.insertBefore(d,b.firstChild),p&&b.insertBefore(p,b.firstChild)},v.exports=m},5847:(v,E,t)=>{"use strict";var u=t(1993),h=t(6964).b;function a(l,o){h.call(this);var r=this;this.bufferPosition=0,this.xo=new o("POST",l,null),this.xo.on("chunk",this._chunkHandler.bind(this)),this.xo.once("finish",function(m,f){r._chunkHandler(m,f),r.xo=null,r.emit("close",null,200===m?"network":"permanent"),r._cleanup()})}u(a,h),a.prototype._chunkHandler=function(l,o){if(200===l&&o)for(var r=-1;;this.bufferPosition+=r+1){var m=o.slice(this.bufferPosition);if(-1===(r=m.indexOf("\n")))break;var f=m.slice(0,r);f&&this.emit("message",f)}},a.prototype._cleanup=function(){this.removeAllListeners()},a.prototype.abort=function(){this.xo&&(this.xo.close(),this.emit("close",null,"user"),this.xo=null),this._cleanup()},v.exports=a},1759:(v,E,t)=>{"use strict";var a,l,u=t(6944),h=t(2630);v.exports=function(m,f,g){a||function r(){(a=global.document.createElement("form")).style.display="none",a.style.position="absolute",a.method="POST",a.enctype="application/x-www-form-urlencoded",a.acceptCharset="UTF-8",(l=global.document.createElement("textarea")).name="d",a.appendChild(l),global.document.body.appendChild(a)}();var d="a"+u.string(8);a.target=d,a.action=h.addQuery(h.addPath(m,"/jsonp_send"),"i="+d);var p=function o(m){try{return global.document.createElement('<iframe name="'+m+'">')}catch{var f=global.document.createElement("iframe");return f.name=m,f}}(d);p.id=d,p.style.display="none",a.appendChild(p);try{l.value=f}catch{}a.submit();var b=function(x){p.onerror&&(p.onreadystatechange=p.onerror=p.onload=null,setTimeout(function(){p.parentNode.removeChild(p),p=null},500),l.value="",g(x))};return p.onerror=function(){b()},p.onload=function(){b()},p.onreadystatechange=function(x){"complete"===p.readyState&&b()},function(){b(new Error("Aborted"))}}},3103:(v,E,t)=>{"use strict";var u=t(6964).b,h=t(1993),i=t(1059),a=t(9427),l=t(2630);function r(m,f,g){var d=this;u.call(this),setTimeout(function(){d._start(m,f,g)},0)}h(r,u),r.prototype._start=function(m,f,g){var d=this,p=new global.XDomainRequest;f=l.addQuery(f,"t="+ +new Date),p.onerror=function(){d._error()},p.ontimeout=function(){d._error()},p.onprogress=function(){d.emit("chunk",200,p.responseText)},p.onload=function(){d.emit("finish",200,p.responseText),d._cleanup(!1)},this.xdr=p,this.unloadRef=i.unloadAdd(function(){d._cleanup(!0)});try{this.xdr.open(m,f),this.timeout&&(this.xdr.timeout=this.timeout),this.xdr.send(g)}catch{this._error()}},r.prototype._error=function(){this.emit("finish",0,""),this._cleanup(!1)},r.prototype._cleanup=function(m){if(this.xdr){if(this.removeAllListeners(),i.unloadDel(this.unloadRef),this.xdr.ontimeout=this.xdr.onerror=this.xdr.onprogress=this.xdr.onload=null,m)try{this.xdr.abort()}catch{}this.unloadRef=this.xdr=null}},r.prototype.close=function(){this._cleanup(!0)},r.enabled=!(!global.XDomainRequest||!a.hasDomain()),v.exports=r},53:(v,E,t)=>{"use strict";var u=t(1993),h=t(5067);function i(a,l,o,r){h.call(this,a,l,o,r)}u(i,h),i.enabled=h.enabled&&h.supportsCORS,v.exports=i},1749:(v,E,t)=>{"use strict";var u=t(6964).b;function i(){var a=this;u.call(this),this.to=setTimeout(function(){a.emit("finish",200,"{}")},i.timeout)}t(1993)(i,u),i.prototype.close=function(){clearTimeout(this.to)},i.timeout=2e3,v.exports=i},9781:(v,E,t)=>{"use strict";var u=t(1993),h=t(5067);function i(a,l,o){h.call(this,a,l,o,{noCredentials:!0})}u(i,h),i.enabled=h.enabled,v.exports=i},1240:(v,E,t)=>{"use strict";var u=t(1059),h=t(2630),i=t(1993),a=t(6964).b,l=t(6419);function r(m,f,g){if(!r.enabled())throw new Error("Transport created when disabled");a.call(this);var d=this,p=h.addPath(m,"/websocket");p="https"===p.slice(0,5)?"wss"+p.slice(5):"ws"+p.slice(4),this.url=p,this.ws=new l(this.url,[],g),this.ws.onmessage=function(b){d.emit("message",b.data)},this.unloadRef=u.unloadAdd(function(){d.ws.close()}),this.ws.onclose=function(b){d.emit("close",b.code,b.reason),d._cleanup()},this.ws.onerror=function(b){d.emit("close",1006,"WebSocket connection broken"),d._cleanup()}}i(r,a),r.prototype.send=function(m){this.ws.send("["+m+"]")},r.prototype.close=function(){var m=this.ws;this._cleanup(),m&&m.close()},r.prototype._cleanup=function(){var m=this.ws;m&&(m.onmessage=m.onclose=m.onerror=null),u.unloadDel(this.unloadRef),this.unloadRef=this.ws=null,this.removeAllListeners()},r.enabled=function(){return!!l},r.transportName="websocket",r.roundTrips=2,v.exports=r},5483:(v,E,t)=>{"use strict";var u=t(1993),h=t(6599),i=t(3356),a=t(5847),l=t(3103);function o(r){if(!l.enabled)throw new Error("Transport created when disabled");h.call(this,r,"/xhr",a,l)}u(o,h),o.enabled=i.enabled,o.transportName="xdr-polling",o.roundTrips=2,v.exports=o},3356:(v,E,t)=>{"use strict";var u=t(1993),h=t(6599),i=t(5847),a=t(3103);function l(o){if(!a.enabled)throw new Error("Transport created when disabled");h.call(this,o,"/xhr_streaming",i,a)}u(l,h),l.enabled=function(o){return!o.cookie_needed&&!o.nullOrigin&&a.enabled&&o.sameScheme},l.transportName="xdr-streaming",l.roundTrips=2,v.exports=l},5543:(v,E,t)=>{"use strict";var u=t(1993),h=t(6599),i=t(5847),a=t(53),l=t(9781);function o(r){if(!l.enabled&&!a.enabled)throw new Error("Transport created when disabled");h.call(this,r,"/xhr",i,a)}u(o,h),o.enabled=function(r){return!r.nullOrigin&&(!(!l.enabled||!r.sameOrigin)||a.enabled)},o.transportName="xhr-polling",o.roundTrips=2,v.exports=o},9720:(v,E,t)=>{"use strict";var u=t(1993),h=t(6599),i=t(5847),a=t(53),l=t(9781),o=t(9427);function r(m){if(!l.enabled&&!a.enabled)throw new Error("Transport created when disabled");h.call(this,m,"/xhr_streaming",i,a)}u(r,h),r.enabled=function(m){return!m.nullOrigin&&!o.isOpera()&&a.enabled},r.transportName="xhr-streaming",r.roundTrips=2,r.needBody=!!global.document,v.exports=r},5893:v=>{"use strict";v.exports.randomBytes=global.crypto&&global.crypto.getRandomValues?function(E){var t=new Uint8Array(E);return global.crypto.getRandomValues(t),t}:function(E){for(var t=new Array(E),u=0;u<E;u++)t[u]=Math.floor(256*Math.random());return t}},9427:v=>{"use strict";v.exports={isOpera:function(){return global.navigator&&/opera/i.test(global.navigator.userAgent)},isKonqueror:function(){return global.navigator&&/konqueror/i.test(global.navigator.userAgent)},hasDomain:function(){if(!global.document)return!0;try{return!!global.document.domain}catch{return!1}}}},196:v=>{"use strict";var t,E=/[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g;v.exports={quote:function(h){var i=JSON.stringify(h);return E.lastIndex=0,E.test(i)?(t||(t=function(h){var i,a={},l=[];for(i=0;i<65536;i++)l.push(String.fromCharCode(i));return h.lastIndex=0,l.join("").replace(h,function(o){return a[o]="\\u"+("0000"+o.charCodeAt(0).toString(16)).slice(-4),""}),h.lastIndex=0,a}(E)),i.replace(E,function(a){return t[a]})):i}}},1059:(v,E,t)=>{"use strict";var u=t(6944),h={},i=!1,a=global.chrome&&global.chrome.app&&global.chrome.app.runtime;v.exports={attachEvent:function(o,r){typeof global.addEventListener<"u"?global.addEventListener(o,r,!1):global.document&&global.attachEvent&&(global.document.attachEvent("on"+o,r),global.attachEvent("on"+o,r))},detachEvent:function(o,r){typeof global.addEventListener<"u"?global.removeEventListener(o,r,!1):global.document&&global.detachEvent&&(global.document.detachEvent("on"+o,r),global.detachEvent("on"+o,r))},unloadAdd:function(o){if(a)return null;var r=u.string(8);return h[r]=o,i&&setTimeout(this.triggerUnloadCallbacks,0),r},unloadDel:function(o){o in h&&delete h[o]},triggerUnloadCallbacks:function(){for(var o in h)h[o](),delete h[o]}},a||v.exports.attachEvent("unload",function(){i||(i=!0,v.exports.triggerUnloadCallbacks())})},9915:(v,E,t)=>{"use strict";var u=t(1059),h=t(9427);v.exports={WPrefix:"_jp",currentWindowId:null,polluteGlobalNamespace:function(){v.exports.WPrefix in global||(global[v.exports.WPrefix]={})},postMessage:function(a,l){global.parent!==global&&global.parent.postMessage(JSON.stringify({windowId:v.exports.currentWindowId,type:a,data:l||""}),"*")},createIframe:function(a,l){var r,m,o=global.document.createElement("iframe"),f=function(){clearTimeout(r);try{o.onload=null}catch{}o.onerror=null},g=function(){o&&(f(),setTimeout(function(){o&&o.parentNode.removeChild(o),o=null},0),u.unloadDel(m))},d=function(b){o&&(g(),l(b))};return o.src=a,o.style.display="none",o.style.position="absolute",o.onerror=function(){d("onerror")},o.onload=function(){clearTimeout(r),r=setTimeout(function(){d("onload timeout")},2e3)},global.document.body.appendChild(o),r=setTimeout(function(){d("timeout")},15e3),m=u.unloadAdd(g),{post:function(b,x){setTimeout(function(){try{o&&o.contentWindow&&o.contentWindow.postMessage(b,x)}catch{}},0)},cleanup:g,loaded:f}},createHtmlfile:function(a,l){var m,f,g,o=["Active"].concat("Object").join("X"),r=new global[o]("htmlfile"),d=function(){clearTimeout(m),g.onerror=null},p=function(){r&&(d(),u.unloadDel(f),g.parentNode.removeChild(g),g=r=null,CollectGarbage())},b=function(A){r&&(p(),l(A))};r.open(),r.write('<html><script>document.domain="'+global.document.domain+'";<\/script></html>'),r.close(),r.parentWindow[v.exports.WPrefix]=global[v.exports.WPrefix];var k=r.createElement("div");return r.body.appendChild(k),g=r.createElement("iframe"),k.appendChild(g),g.src=a,g.onerror=function(){b("onerror")},m=setTimeout(function(){b("timeout")},15e3),f=u.unloadAdd(p),{post:function(A,C){try{setTimeout(function(){g&&g.contentWindow&&g.contentWindow.postMessage(A,C)},0)}catch{}},cleanup:p,loaded:d}}},v.exports.iframeEnabled=!1,global.document&&(v.exports.iframeEnabled=("function"==typeof global.postMessage||"object"==typeof global.postMessage)&&!h.isKonqueror())},9697:v=>{"use strict";var E={};["log","debug","warn"].forEach(function(t){var u;try{u=global.console&&global.console[t]&&global.console[t].apply}catch{}E[t]=u?function(){return global.console[t].apply(global.console,arguments)}:"log"===t?function(){}:E.log}),v.exports=E},7824:v=>{"use strict";v.exports={isObject:function(E){var t=typeof E;return"function"===t||"object"===t&&!!E},extend:function(E){if(!this.isObject(E))return E;for(var t,u,h=1,i=arguments.length;h<i;h++)for(u in t=arguments[h])Object.prototype.hasOwnProperty.call(t,u)&&(E[u]=t[u]);return E}}},6944:(v,E,t)=>{"use strict";var u=t(5893);v.exports={string:function(i){for(var l=u.randomBytes(i),o=[],r=0;r<i;r++)o.push("abcdefghijklmnopqrstuvwxyz012345".substr(l[r]%32,1));return o.join("")},number:function(i){return Math.floor(Math.random()*i)},numberString:function(i){var a=(""+(i-1)).length;return(new Array(a+1).join("0")+this.number(i)).slice(-a)}}},2850:v=>{"use strict";v.exports=function(t){return{filterToEnabled:function(u,h){var i={main:[],facade:[]};return u?"string"==typeof u&&(u=[u]):u=[],t.forEach(function(a){if(a){if("websocket"===a.transportName&&!1===h.websocket)return;if(u.length&&-1===u.indexOf(a.transportName))return;a.enabled(h)&&(i.main.push(a),a.facadeTransport&&i.facade.push(a.facadeTransport))}}),i}}}},2630:(v,E,t)=>{"use strict";var u=t(3711);v.exports={getOrigin:function(i){if(!i)return null;var a=new u(i);if("file:"===a.protocol)return null;var l=a.port;return l||(l="https:"===a.protocol?"443":"80"),a.protocol+"//"+a.hostname+":"+l},isOriginEqual:function(i,a){return this.getOrigin(i)===this.getOrigin(a)},isSchemeEqual:function(i,a){return i.split(":")[0]===a.split(":")[0]},addPath:function(i,a){var l=i.split("?");return l[0]+a+(l[1]?"?"+l[1]:"")},addQuery:function(i,a){return i+(-1===i.indexOf("?")?"?"+a:"&"+a)},isLoopbackAddr:function(i){return/^127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(i)||/^\[::1\]$/.test(i)}}},8469:v=>{v.exports="1.6.1"},3711:(v,E,t)=>{"use strict";var u=t(5852),h=t(8629),i=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,a=/[\n\r\t]/g,l=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,o=/:\d+$/,r=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,m=/^[a-zA-Z]:/;function f(e){return(e||"").toString().replace(i,"")}var g=[["#","hash"],["?","query"],function(c,n){return b(n.protocol)?c.replace(/\\/g,"/"):c},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],d={hash:1,query:1};function p(e){var c;c=typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};var O,s={},y=typeof(e=e||c.location||{});if("blob:"===e.protocol)s=new A(unescape(e.pathname),{});else if("string"===y)for(O in s=new A(e,{}),d)delete s[O];else if("object"===y){for(O in e)O in d||(s[O]=e[O]);void 0===s.slashes&&(s.slashes=l.test(e.href))}return s}function b(e){return"file:"===e||"ftp:"===e||"http:"===e||"https:"===e||"ws:"===e||"wss:"===e}function x(e,c){e=(e=f(e)).replace(a,""),c=c||{};var I,n=r.exec(e),s=n[1]?n[1].toLowerCase():"",y=!!n[2],O=!!n[3],w=0;return y?O?(I=n[2]+n[3]+n[4],w=n[2].length+n[3].length):(I=n[2]+n[4],w=n[2].length):O?(I=n[3]+n[4],w=n[3].length):I=n[4],"file:"===s?w>=2&&(I=I.slice(2)):b(s)?I=n[4]:s?y&&(I=I.slice(2)):w>=2&&b(c.protocol)&&(I=n[4]),{protocol:s,slashes:y||b(s),slashesCount:w,rest:I}}function A(e,c,n){if(e=(e=f(e)).replace(a,""),!(this instanceof A))return new A(e,c,n);var s,y,O,w,I,R,U=g.slice(),N=typeof c,T=this,j=0;for("object"!==N&&"string"!==N&&(n=c,c=null),n&&"function"!=typeof n&&(n=h.parse),s=!(y=x(e||"",c=p(c))).protocol&&!y.slashes,T.slashes=y.slashes||s&&c.slashes,T.protocol=y.protocol||c.protocol||"",e=y.rest,("file:"===y.protocol&&(2!==y.slashesCount||m.test(e))||!y.slashes&&(y.protocol||y.slashesCount<2||!b(T.protocol)))&&(U[3]=[/(.*)/,"pathname"]);j<U.length;j++)"function"!=typeof(w=U[j])?(R=w[1],(O=w[0])!=O?T[R]=e:"string"==typeof O?~(I="@"===O?e.lastIndexOf(O):e.indexOf(O))&&("number"==typeof w[2]?(T[R]=e.slice(0,I),e=e.slice(I+w[2])):(T[R]=e.slice(I),e=e.slice(0,I))):(I=O.exec(e))&&(T[R]=I[1],e=e.slice(0,I.index)),T[R]=T[R]||s&&w[3]&&c[R]||"",w[4]&&(T[R]=T[R].toLowerCase())):e=w(e,T);n&&(T.query=n(T.query)),s&&c.slashes&&"/"!==T.pathname.charAt(0)&&(""!==T.pathname||""!==c.pathname)&&(T.pathname=function k(e,c){if(""===e)return c;for(var n=(c||"/").split("/").slice(0,-1).concat(e.split("/")),s=n.length,y=n[s-1],O=!1,w=0;s--;)"."===n[s]?n.splice(s,1):".."===n[s]?(n.splice(s,1),w++):w&&(0===s&&(O=!0),n.splice(s,1),w--);return O&&n.unshift(""),("."===y||".."===y)&&n.push(""),n.join("/")}(T.pathname,c.pathname)),"/"!==T.pathname.charAt(0)&&b(T.protocol)&&(T.pathname="/"+T.pathname),u(T.port,T.protocol)||(T.host=T.hostname,T.port=""),T.username=T.password="",T.auth&&(~(I=T.auth.indexOf(":"))?(T.username=T.auth.slice(0,I),T.username=encodeURIComponent(decodeURIComponent(T.username)),T.password=T.auth.slice(I+1),T.password=encodeURIComponent(decodeURIComponent(T.password))):T.username=encodeURIComponent(decodeURIComponent(T.auth)),T.auth=T.password?T.username+":"+T.password:T.username),T.origin="file:"!==T.protocol&&b(T.protocol)&&T.host?T.protocol+"//"+T.host:"null",T.href=T.toString()}A.prototype={set:function C(e,c,n){var s=this;switch(e){case"query":"string"==typeof c&&c.length&&(c=(n||h.parse)(c)),s[e]=c;break;case"port":s[e]=c,u(c,s.protocol)?c&&(s.host=s.hostname+":"+c):(s.host=s.hostname,s[e]="");break;case"hostname":s[e]=c,s.port&&(c+=":"+s.port),s.host=c;break;case"host":s[e]=c,o.test(c)?(c=c.split(":"),s.port=c.pop(),s.hostname=c.join(":")):(s.hostname=c,s.port="");break;case"protocol":s.protocol=c.toLowerCase(),s.slashes=!n;break;case"pathname":case"hash":if(c){var y="pathname"===e?"/":"#";s[e]=c.charAt(0)!==y?y+c:c}else s[e]=c;break;case"username":case"password":s[e]=encodeURIComponent(c);break;case"auth":var O=c.indexOf(":");~O?(s.username=c.slice(0,O),s.username=encodeURIComponent(decodeURIComponent(s.username)),s.password=c.slice(O+1),s.password=encodeURIComponent(decodeURIComponent(s.password))):s.username=encodeURIComponent(decodeURIComponent(c))}for(var w=0;w<g.length;w++){var I=g[w];I[4]&&(s[I[1]]=s[I[1]].toLowerCase())}return s.auth=s.password?s.username+":"+s.password:s.username,s.origin="file:"!==s.protocol&&b(s.protocol)&&s.host?s.protocol+"//"+s.host:"null",s.href=s.toString(),s},toString:function S(e){(!e||"function"!=typeof e)&&(e=h.stringify);var c,n=this,s=n.host,y=n.protocol;y&&":"!==y.charAt(y.length-1)&&(y+=":");var O=y+(n.protocol&&n.slashes||b(n.protocol)?"//":"");return n.username?(O+=n.username,n.password&&(O+=":"+n.password),O+="@"):n.password?(O+=":"+n.password,O+="@"):"file:"!==n.protocol&&b(n.protocol)&&!s&&"/"!==n.pathname&&(O+="@"),(":"===s[s.length-1]||o.test(n.hostname)&&!n.port)&&(s+=":"),O+=s+n.pathname,(c="object"==typeof n.query?e(n.query):n.query)&&(O+="?"!==c.charAt(0)?"?"+c:c),n.hash&&(O+=n.hash),O}},A.extractProtocol=x,A.location=p,A.trimLeft=f,A.qs=h,v.exports=A},2869:(v,E,t)=>{"use strict";t.d(E,{K:()=>C});var u=t(467);const i_LF="\n",i_NULL="\0";class a{get body(){return!this._body&&this.isBinaryBody&&(this._body=(new TextDecoder).decode(this._binaryBody)),this._body||""}get binaryBody(){return!this._binaryBody&&!this.isBinaryBody&&(this._binaryBody=(new TextEncoder).encode(this._body)),this._binaryBody}constructor(e){const{command:c,headers:n,body:s,binaryBody:y,escapeHeaderValues:O,skipContentLengthHeader:w}=e;this.command=c,this.headers=Object.assign({},n||{}),y?(this._binaryBody=y,this.isBinaryBody=!0):(this._body=s||"",this.isBinaryBody=!1),this.escapeHeaderValues=O||!1,this.skipContentLengthHeader=w||!1}static fromRawFrame(e,c){const n={},s=y=>y.replace(/^\s+|\s+$/g,"");for(const y of e.headers.reverse()){y.indexOf(":");const w=s(y[0]);let I=s(y[1]);c&&"CONNECT"!==e.command&&"CONNECTED"!==e.command&&(I=a.hdrValueUnEscape(I)),n[w]=I}return new a({command:e.command,headers:n,binaryBody:e.binaryBody,escapeHeaderValues:c})}toString(){return this.serializeCmdAndHeaders()}serialize(){const e=this.serializeCmdAndHeaders();return this.isBinaryBody?a.toUnit8Array(e,this._binaryBody).buffer:e+this._body+i_NULL}serializeCmdAndHeaders(){const e=[this.command];this.skipContentLengthHeader&&delete this.headers["content-length"];for(const c of Object.keys(this.headers||{})){const n=this.headers[c];e.push(this.escapeHeaderValues&&"CONNECT"!==this.command&&"CONNECTED"!==this.command?`${c}:${a.hdrValueEscape(`${n}`)}`:`${c}:${n}`)}return(this.isBinaryBody||!this.isBodyEmpty()&&!this.skipContentLengthHeader)&&e.push(`content-length:${this.bodyLength()}`),e.join(i_LF)+i_LF+i_LF}isBodyEmpty(){return 0===this.bodyLength()}bodyLength(){const e=this.binaryBody;return e?e.length:0}static sizeOfUTF8(e){return e?(new TextEncoder).encode(e).length:0}static toUnit8Array(e,c){const n=(new TextEncoder).encode(e),s=new Uint8Array([0]),y=new Uint8Array(n.length+c.length+s.length);return y.set(n),y.set(c,n.length),y.set(s,n.length+c.length),y}static marshall(e){return new a(e).serialize()}static hdrValueEscape(e){return e.replace(/\\/g,"\\\\").replace(/\r/g,"\\r").replace(/\n/g,"\\n").replace(/:/g,"\\c")}static hdrValueUnEscape(e){return e.replace(/\\r/g,"\r").replace(/\\n/g,"\n").replace(/\\c/g,":").replace(/\\\\/g,"\\")}}class f{constructor(e,c){this.onFrame=e,this.onIncomingPing=c,this._encoder=new TextEncoder,this._decoder=new TextDecoder,this._token=[],this._initState()}parseChunk(e,c=!1){let n;if(n="string"==typeof e?this._encoder.encode(e):new Uint8Array(e),c&&0!==n[n.length-1]){const s=new Uint8Array(n.length+1);s.set(n,0),s[n.length]=0,n=s}for(let s=0;s<n.length;s++)this._onByte(n[s])}_collectFrame(e){if(0!==e&&13!==e){if(10===e)return void this.onIncomingPing();this._onByte=this._collectCommand,this._reinjectByte(e)}}_collectCommand(e){if(13!==e){if(10===e)return this._results.command=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaders);this._consumeByte(e)}}_collectHeaders(e){if(13!==e){if(10===e)return void this._setupCollectBody();this._onByte=this._collectHeaderKey,this._reinjectByte(e)}}_reinjectByte(e){this._onByte(e)}_collectHeaderKey(e){if(58===e)return this._headerKey=this._consumeTokenAsUTF8(),void(this._onByte=this._collectHeaderValue);this._consumeByte(e)}_collectHeaderValue(e){if(13!==e){if(10===e)return this._results.headers.push([this._headerKey,this._consumeTokenAsUTF8()]),this._headerKey=void 0,void(this._onByte=this._collectHeaders);this._consumeByte(e)}}_setupCollectBody(){const e=this._results.headers.filter(c=>"content-length"===c[0])[0];e?(this._bodyBytesRemaining=parseInt(e[1],10),this._onByte=this._collectBodyFixedSize):this._onByte=this._collectBodyNullTerminated}_collectBodyNullTerminated(e){0!==e?this._consumeByte(e):this._retrievedBody()}_collectBodyFixedSize(e){0!=this._bodyBytesRemaining--?this._consumeByte(e):this._retrievedBody()}_retrievedBody(){this._results.binaryBody=this._consumeTokenAsRaw();try{this.onFrame(this._results)}catch(e){console.log("Ignoring an exception thrown by a frame handler. Original exception: ",e)}this._initState()}_consumeByte(e){this._token.push(e)}_consumeTokenAsUTF8(){return this._decoder.decode(this._consumeTokenAsRaw())}_consumeTokenAsRaw(){const e=new Uint8Array(this._token);return this._token=[],e}_initState(){this._results={command:void 0,headers:[],binaryBody:void 0},this._token=[],this._headerKey=void 0,this._onByte=this._collectFrame}}var g=function(S){return S[S.CONNECTING=0]="CONNECTING",S[S.OPEN=1]="OPEN",S[S.CLOSING=2]="CLOSING",S[S.CLOSED=3]="CLOSED",S}(g||{}),d=function(S){return S[S.ACTIVE=0]="ACTIVE",S[S.DEACTIVATING=1]="DEACTIVATING",S[S.INACTIVE=2]="INACTIVE",S}(d||{}),p=function(S){return S[S.LINEAR=0]="LINEAR",S[S.EXPONENTIAL=1]="EXPONENTIAL",S}(p||{}),b=function(S){return S.Interval="interval",S.Worker="worker",S}(b||{});class x{constructor(e,c=b.Interval,n){this._interval=e,this._strategy=c,this._debug=n,this._workerScript=`\n    var startTime = Date.now();\n    setInterval(function() {\n        self.postMessage(Date.now() - startTime);\n    }, ${this._interval});\n  `}start(e){this.stop(),this.shouldUseWorker()?this.runWorker(e):this.runInterval(e)}stop(){this.disposeWorker(),this.disposeInterval()}shouldUseWorker(){return typeof Worker<"u"&&this._strategy===b.Worker}runWorker(e){this._debug("Using runWorker for outgoing pings"),this._worker||(this._worker=new Worker(URL.createObjectURL(new Blob([this._workerScript],{type:"text/javascript"}))),this._worker.onmessage=c=>e(c.data))}runInterval(e){if(this._debug("Using runInterval for outgoing pings"),!this._timer){const c=Date.now();this._timer=setInterval(()=>{e(Date.now()-c)},this._interval)}}disposeWorker(){this._worker&&(this._worker.terminate(),delete this._worker,this._debug("Outgoing ping disposeWorker"))}disposeInterval(){this._timer&&(clearInterval(this._timer),delete this._timer,this._debug("Outgoing ping disposeInterval"))}}class k{constructor(e){this.versions=e}supportedVersions(){return this.versions.join(",")}protocolVersions(){return this.versions.map(e=>`v${e.replace(".","")}.stomp`)}}k.V1_0="1.0",k.V1_1="1.1",k.V1_2="1.2",k.default=new k([k.V1_2,k.V1_1,k.V1_0]);class A{get connectedVersion(){return this._connectedVersion}get connected(){return this._connected}constructor(e,c,n){this._client=e,this._webSocket=c,this._connected=!1,this._serverFrameHandlers={CONNECTED:s=>{this.debug(`connected to server ${s.headers.server}`),this._connected=!0,this._connectedVersion=s.headers.version,this._connectedVersion===k.V1_2&&(this._escapeHeaderValues=!0),this._setupHeartbeat(s.headers),this.onConnect(s)},MESSAGE:s=>{const y=s.headers.subscription,O=this._subscriptions[y]||this.onUnhandledMessage,w=s,I=this,R=this._connectedVersion===k.V1_2?w.headers.ack:w.headers["message-id"];w.ack=(U={})=>I.ack(R,y,U),w.nack=(U={})=>I.nack(R,y,U),O(w)},RECEIPT:s=>{const y=this._receiptWatchers[s.headers["receipt-id"]];y?(y(s),delete this._receiptWatchers[s.headers["receipt-id"]]):this.onUnhandledReceipt(s)},ERROR:s=>{this.onStompError(s)}},this._counter=0,this._subscriptions={},this._receiptWatchers={},this._partialData="",this._escapeHeaderValues=!1,this._lastServerActivityTS=Date.now(),this.debug=n.debug,this.stompVersions=n.stompVersions,this.connectHeaders=n.connectHeaders,this.disconnectHeaders=n.disconnectHeaders,this.heartbeatIncoming=n.heartbeatIncoming,this.heartbeatOutgoing=n.heartbeatOutgoing,this.splitLargeFrames=n.splitLargeFrames,this.maxWebSocketChunkSize=n.maxWebSocketChunkSize,this.forceBinaryWSFrames=n.forceBinaryWSFrames,this.logRawCommunication=n.logRawCommunication,this.appendMissingNULLonIncoming=n.appendMissingNULLonIncoming,this.discardWebsocketOnCommFailure=n.discardWebsocketOnCommFailure,this.onConnect=n.onConnect,this.onDisconnect=n.onDisconnect,this.onStompError=n.onStompError,this.onWebSocketClose=n.onWebSocketClose,this.onWebSocketError=n.onWebSocketError,this.onUnhandledMessage=n.onUnhandledMessage,this.onUnhandledReceipt=n.onUnhandledReceipt,this.onUnhandledFrame=n.onUnhandledFrame}start(){const e=new f(c=>{const n=a.fromRawFrame(c,this._escapeHeaderValues);this.logRawCommunication||this.debug(`<<< ${n}`),(this._serverFrameHandlers[n.command]||this.onUnhandledFrame)(n)},()=>{this.debug("<<< PONG")});this._webSocket.onmessage=c=>{if(this.debug("Received data"),this._lastServerActivityTS=Date.now(),this.logRawCommunication){const n=c.data instanceof ArrayBuffer?(new TextDecoder).decode(c.data):c.data;this.debug(`<<< ${n}`)}e.parseChunk(c.data,this.appendMissingNULLonIncoming)},this._webSocket.onclose=c=>{this.debug(`Connection closed to ${this._webSocket.url}`),this._cleanUp(),this.onWebSocketClose(c)},this._webSocket.onerror=c=>{this.onWebSocketError(c)},this._webSocket.onopen=()=>{const c=Object.assign({},this.connectHeaders);this.debug("Web Socket Opened..."),c["accept-version"]=this.stompVersions.supportedVersions(),c["heart-beat"]=[this.heartbeatOutgoing,this.heartbeatIncoming].join(","),this._transmit({command:"CONNECT",headers:c})}}_setupHeartbeat(e){if(e.version!==k.V1_1&&e.version!==k.V1_2||!e["heart-beat"])return;const[c,n]=e["heart-beat"].split(",").map(s=>parseInt(s,10));if(0!==this.heartbeatOutgoing&&0!==n){const s=Math.max(this.heartbeatOutgoing,n);this.debug(`send PING every ${s}ms`),this._pinger=new x(s,this._client.heartbeatStrategy,this.debug),this._pinger.start(()=>{this._webSocket.readyState===g.OPEN&&(this._webSocket.send(i_LF),this.debug(">>> PING"))})}if(0!==this.heartbeatIncoming&&0!==c){const s=Math.max(this.heartbeatIncoming,c);this.debug(`check PONG every ${s}ms`),this._ponger=setInterval(()=>{const y=Date.now()-this._lastServerActivityTS;y>2*s&&(this.debug(`did not receive server activity for the last ${y}ms`),this._closeOrDiscardWebsocket())},s)}}_closeOrDiscardWebsocket(){this.discardWebsocketOnCommFailure?(this.debug("Discarding websocket, the underlying socket may linger for a while"),this.discardWebsocket()):(this.debug("Issuing close on the websocket"),this._closeWebsocket())}forceDisconnect(){this._webSocket&&(this._webSocket.readyState===g.CONNECTING||this._webSocket.readyState===g.OPEN)&&this._closeOrDiscardWebsocket()}_closeWebsocket(){this._webSocket.onmessage=()=>{},this._webSocket.close()}discardWebsocket(){"function"!=typeof this._webSocket.terminate&&function h(S,e){S.terminate=function(){const c=()=>{};this.onerror=c,this.onmessage=c,this.onopen=c;const n=new Date,s=Math.random().toString().substring(2,8),y=this.onclose;this.onclose=O=>{const w=(new Date).getTime()-n.getTime();e(`Discarded socket (#${s})  closed after ${w}ms, with code/reason: ${O.code}/${O.reason}`)},this.close(),y?.call(S,{code:4001,reason:`Quick discarding socket (#${s}) without waiting for the shutdown sequence.`,wasClean:!1})}}(this._webSocket,e=>this.debug(e)),this._webSocket.terminate()}_transmit(e){const{command:c,headers:n,body:s,binaryBody:y,skipContentLengthHeader:O}=e,w=new a({command:c,headers:n,body:s,binaryBody:y,escapeHeaderValues:this._escapeHeaderValues,skipContentLengthHeader:O});let I=w.serialize();if(this.debug(this.logRawCommunication?`>>> ${I}`:`>>> ${w}`),this.forceBinaryWSFrames&&"string"==typeof I&&(I=(new TextEncoder).encode(I)),"string"==typeof I&&this.splitLargeFrames){let R=I;for(;R.length>0;){const U=R.substring(0,this.maxWebSocketChunkSize);R=R.substring(this.maxWebSocketChunkSize),this._webSocket.send(U),this.debug(`chunk sent = ${U.length}, remaining = ${R.length}`)}}else this._webSocket.send(I)}dispose(){if(this.connected)try{const e=Object.assign({},this.disconnectHeaders);e.receipt||(e.receipt="close-"+this._counter++),this.watchForReceipt(e.receipt,c=>{this._closeWebsocket(),this._cleanUp(),this.onDisconnect(c)}),this._transmit({command:"DISCONNECT",headers:e})}catch(e){this.debug(`Ignoring error during disconnect ${e}`)}else(this._webSocket.readyState===g.CONNECTING||this._webSocket.readyState===g.OPEN)&&this._closeWebsocket()}_cleanUp(){this._connected=!1,this._pinger&&(this._pinger.stop(),this._pinger=void 0),this._ponger&&(clearInterval(this._ponger),this._ponger=void 0)}publish(e){const{destination:c,headers:n,body:s,binaryBody:y,skipContentLengthHeader:O}=e,w=Object.assign({destination:c},n);this._transmit({command:"SEND",headers:w,body:s,binaryBody:y,skipContentLengthHeader:O})}watchForReceipt(e,c){this._receiptWatchers[e]=c}subscribe(e,c,n={}){(n=Object.assign({},n)).id||(n.id="sub-"+this._counter++),n.destination=e,this._subscriptions[n.id]=c,this._transmit({command:"SUBSCRIBE",headers:n});const s=this;return{id:n.id,unsubscribe:y=>s.unsubscribe(n.id,y)}}unsubscribe(e,c={}){c=Object.assign({},c),delete this._subscriptions[e],c.id=e,this._transmit({command:"UNSUBSCRIBE",headers:c})}begin(e){const c=e||"tx-"+this._counter++;this._transmit({command:"BEGIN",headers:{transaction:c}});const n=this;return{id:c,commit(){n.commit(c)},abort(){n.abort(c)}}}commit(e){this._transmit({command:"COMMIT",headers:{transaction:e}})}abort(e){this._transmit({command:"ABORT",headers:{transaction:e}})}ack(e,c,n={}){n=Object.assign({},n),this._connectedVersion===k.V1_2?n.id=e:n["message-id"]=e,n.subscription=c,this._transmit({command:"ACK",headers:n})}nack(e,c,n={}){return n=Object.assign({},n),this._connectedVersion===k.V1_2?n.id=e:n["message-id"]=e,n.subscription=c,this._transmit({command:"NACK",headers:n})}}class C{get webSocket(){return this._stompHandler?._webSocket}get disconnectHeaders(){return this._disconnectHeaders}set disconnectHeaders(e){this._disconnectHeaders=e,this._stompHandler&&(this._stompHandler.disconnectHeaders=this._disconnectHeaders)}get connected(){return!!this._stompHandler&&this._stompHandler.connected}get connectedVersion(){return this._stompHandler?this._stompHandler.connectedVersion:void 0}get active(){return this.state===d.ACTIVE}_changeState(e){this.state=e,this.onChangeState(e)}constructor(e={}){this.stompVersions=k.default,this.connectionTimeout=0,this.reconnectDelay=5e3,this._nextReconnectDelay=0,this.maxReconnectDelay=9e5,this.reconnectTimeMode=p.LINEAR,this.heartbeatIncoming=1e4,this.heartbeatOutgoing=1e4,this.heartbeatStrategy=b.Interval,this.splitLargeFrames=!1,this.maxWebSocketChunkSize=8192,this.forceBinaryWSFrames=!1,this.appendMissingNULLonIncoming=!1,this.discardWebsocketOnCommFailure=!1,this.state=d.INACTIVE;const c=()=>{};this.debug=c,this.beforeConnect=c,this.onConnect=c,this.onDisconnect=c,this.onUnhandledMessage=c,this.onUnhandledReceipt=c,this.onUnhandledFrame=c,this.onStompError=c,this.onWebSocketClose=c,this.onWebSocketError=c,this.logRawCommunication=!1,this.onChangeState=c,this.connectHeaders={},this._disconnectHeaders={},this.configure(e)}configure(e){Object.assign(this,e),this.maxReconnectDelay>0&&this.maxReconnectDelay<this.reconnectDelay&&(this.debug(`Warning: maxReconnectDelay (${this.maxReconnectDelay}ms) is less than reconnectDelay (${this.reconnectDelay}ms). Using reconnectDelay as the maxReconnectDelay delay.`),this.maxReconnectDelay=this.reconnectDelay)}activate(){const e=()=>{this.active?this.debug("Already ACTIVE, ignoring request to activate"):(this._changeState(d.ACTIVE),this._nextReconnectDelay=this.reconnectDelay,this._connect())};this.state===d.DEACTIVATING?(this.debug("Waiting for deactivation to finish before activating"),this.deactivate().then(()=>{e()})):e()}_connect(){var e=this;return(0,u.A)(function*(){if(yield e.beforeConnect(e),e._stompHandler)return void e.debug("There is already a stompHandler, skipping the call to connect");if(!e.active)return void e.debug("Client has been marked inactive, will not attempt to connect");e.connectionTimeout>0&&(e._connectionWatcher&&clearTimeout(e._connectionWatcher),e._connectionWatcher=setTimeout(()=>{e.connected||(e.debug(`Connection not established in ${e.connectionTimeout}ms, closing socket`),e.forceDisconnect())},e.connectionTimeout)),e.debug("Opening Web Socket...");const c=e._createWebSocket();e._stompHandler=new A(e,c,{debug:e.debug,stompVersions:e.stompVersions,connectHeaders:e.connectHeaders,disconnectHeaders:e._disconnectHeaders,heartbeatIncoming:e.heartbeatIncoming,heartbeatOutgoing:e.heartbeatOutgoing,heartbeatStrategy:e.heartbeatStrategy,splitLargeFrames:e.splitLargeFrames,maxWebSocketChunkSize:e.maxWebSocketChunkSize,forceBinaryWSFrames:e.forceBinaryWSFrames,logRawCommunication:e.logRawCommunication,appendMissingNULLonIncoming:e.appendMissingNULLonIncoming,discardWebsocketOnCommFailure:e.discardWebsocketOnCommFailure,onConnect:n=>{if(e._connectionWatcher&&(clearTimeout(e._connectionWatcher),e._connectionWatcher=void 0),!e.active)return e.debug("STOMP got connected while deactivate was issued, will disconnect now"),void e._disposeStompHandler();e.onConnect(n)},onDisconnect:n=>{e.onDisconnect(n)},onStompError:n=>{e.onStompError(n)},onWebSocketClose:n=>{e._stompHandler=void 0,e.state===d.DEACTIVATING&&e._changeState(d.INACTIVE),e.onWebSocketClose(n),e.active&&e._schedule_reconnect()},onWebSocketError:n=>{e.onWebSocketError(n)},onUnhandledMessage:n=>{e.onUnhandledMessage(n)},onUnhandledReceipt:n=>{e.onUnhandledReceipt(n)},onUnhandledFrame:n=>{e.onUnhandledFrame(n)}}),e._stompHandler.start()})()}_createWebSocket(){let e;if(this.webSocketFactory)e=this.webSocketFactory();else{if(!this.brokerURL)throw new Error("Either brokerURL or webSocketFactory must be provided");e=new WebSocket(this.brokerURL,this.stompVersions.protocolVersions())}return e.binaryType="arraybuffer",e}_schedule_reconnect(){this._nextReconnectDelay>0&&(this.debug(`STOMP: scheduling reconnection in ${this._nextReconnectDelay}ms`),this._reconnector=setTimeout(()=>{this.reconnectTimeMode===p.EXPONENTIAL&&(this._nextReconnectDelay=2*this._nextReconnectDelay,0!==this.maxReconnectDelay&&(this._nextReconnectDelay=Math.min(this._nextReconnectDelay,this.maxReconnectDelay))),this._connect()},this._nextReconnectDelay))}deactivate(){var e=this;return(0,u.A)(function*(c={}){const n=c.force||!1,s=e.active;let y;if(e.state===d.INACTIVE)return e.debug("Already INACTIVE, nothing more to do"),Promise.resolve();if(e._changeState(d.DEACTIVATING),e._nextReconnectDelay=0,e._reconnector&&(clearTimeout(e._reconnector),e._reconnector=void 0),!e._stompHandler||e.webSocket.readyState===g.CLOSED)return e._changeState(d.INACTIVE),Promise.resolve();{const O=e._stompHandler.onWebSocketClose;y=new Promise((w,I)=>{e._stompHandler.onWebSocketClose=R=>{O(R),w()}})}return n?e._stompHandler?.discardWebsocket():s&&e._disposeStompHandler(),y}).apply(this,arguments)}forceDisconnect(){this._stompHandler&&this._stompHandler.forceDisconnect()}_disposeStompHandler(){this._stompHandler&&this._stompHandler.dispose()}publish(e){this._checkConnection(),this._stompHandler.publish(e)}_checkConnection(){if(!this.connected)throw new TypeError("There is no underlying STOMP connection")}watchForReceipt(e,c){this._checkConnection(),this._stompHandler.watchForReceipt(e,c)}subscribe(e,c,n={}){return this._checkConnection(),this._stompHandler.subscribe(e,c,n)}unsubscribe(e,c={}){this._checkConnection(),this._stompHandler.unsubscribe(e,c)}begin(e){return this._checkConnection(),this._stompHandler.begin(e)}commit(e){this._checkConnection(),this._stompHandler.commit(e)}abort(e){this._checkConnection(),this._stompHandler.abort(e)}ack(e,c,n={}){this._checkConnection(),this._stompHandler.ack(e,c,n)}nack(e,c,n={}){this._checkConnection(),this._stompHandler.nack(e,c,n)}}}}]);