"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[860],{6860:(tt,h,g)=>{g.r(h),g.d(h,{AiHealthBotModule:()=>Z});var d=g(177),r=g(4341),m=g(2434),v=g(4978),p=g(1626),C=g(4412),_=g(5312),t=g(6276),b=g(8010),l=function(n){return n.GENERAL_HEALTH="GENERAL_HEALTH",n.SYMPTOM_ANALYSIS="SYMPTOM_ANALYSIS",n.MEDICATION_INQUIRY="MEDICATION_INQUIRY",n.WELLNESS_TIPS="WELLNESS_TIPS",n.EMERGENCY_GUIDANCE="EMERGENCY_GUIDANCE",n}(l||{});let u=(()=>{class n{constructor(e,o){this.http=e,this.authService=o,this.apiUrl=`${_.c.apiUrl}/ai-health-bot`,this.currentConversationSubject=new C.t(null),this.currentConversation$=this.currentConversationSubject.asObservable()}getHttpOptions(){const e=this.authService.getToken();return{headers:new p.Lr({"Content-Type":"application/json",Authorization:`Bearer ${e}`})}}sendMessage(e){return this.http.post(`${this.apiUrl}/chat`,e,this.getHttpOptions())}getUserConversations(){return this.http.get(`${this.apiUrl}/conversations`,this.getHttpOptions())}getUserConversationsPaginated(e=0,o=10){return this.http.get(`${this.apiUrl}/conversations/paginated?page=${e}&size=${o}`,this.getHttpOptions())}getConversationDetails(e){return this.http.get(`${this.apiUrl}/conversations/${e}`,this.getHttpOptions())}setCurrentConversation(e){this.currentConversationSubject.next(e)}getCurrentConversation(){return this.currentConversationSubject.value}getConversationTypeDisplayName(e){switch(e){case l.GENERAL_HEALTH:return"General Health";case l.SYMPTOM_ANALYSIS:return"Symptom Analysis";case l.MEDICATION_INQUIRY:return"Medication Inquiry";case l.WELLNESS_TIPS:return"Wellness Tips";case l.EMERGENCY_GUIDANCE:return"Emergency Guidance";default:return"General Health"}}getConversationTypeIcon(e){switch(e){case l.GENERAL_HEALTH:return"heart";case l.SYMPTOM_ANALYSIS:return"activity";case l.MEDICATION_INQUIRY:return"pill";case l.WELLNESS_TIPS:return"sun";case l.EMERGENCY_GUIDANCE:return"alert-triangle";default:return"heart"}}healthCheck(){return this.http.get(`${this.apiUrl}/health`,this.getHttpOptions())}static{this.\u0275fac=function(o){return new(o||n)(t.KVO(p.Qq),t.KVO(b.u))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();const y=["messagesContainer"];function M(n,i){if(1&n&&(t.j41(0,"div",28)(1,"div",29),t.nrm(2,"i"),t.j41(3,"span",30),t.EFF(4),t.k0s(),t.j41(5,"small"),t.EFF(6),t.k0s()()()),2&n){const e=t.XpG();t.R7$(2),t.ZvI("fas fa-",e.getConversationTypeIcon(e.currentConversation.conversationType)," me-2"),t.R7$(2),t.JRh(e.getConversationTypeDisplayName(e.currentConversation.conversationType)),t.R7$(2),t.JRh(e.currentConversation.title)}}function T(n,i){1&n&&(t.j41(0,"div",31)(1,"div",32)(2,"span",33),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",34),t.EFF(5,"Loading conversation..."),t.k0s()())}function O(n,i){1&n&&(t.j41(0,"div",35)(1,"div",36),t.nrm(2,"i",37),t.j41(3,"h5"),t.EFF(4,"Welcome to HealthConnect AI Assistant"),t.k0s(),t.j41(5,"p",27),t.EFF(6,"I'm here to help you with health-related questions and provide general medical guidance."),t.k0s(),t.j41(7,"div",38),t.nrm(8,"i",39),t.j41(9,"strong"),t.EFF(10,"Important:"),t.k0s(),t.EFF(11," I provide general health information only. For medical emergencies or serious concerns, please consult healthcare professionals immediately. "),t.k0s()()())}const x=function(n,i){return{"user-message":n,"ai-message":i}},F=function(n,i){return{"fa-user":n,"fa-robot":i}};function E(n,i){if(1&n&&(t.j41(0,"div",43)(1,"div",44)(2,"div",45)(3,"div",46),t.nrm(4,"i",47),t.k0s(),t.j41(5,"div",48)(6,"span",49),t.EFF(7),t.k0s(),t.j41(8,"span",50),t.EFF(9),t.k0s()()(),t.j41(10,"div",51),t.EFF(11),t.k0s()()()),2&n){const e=i.$implicit,o=t.XpG(2);t.Y8G("ngClass",t.l_i(5,x,"USER"===e.role,"ASSISTANT"===e.role)),t.R7$(4),t.Y8G("ngClass",t.l_i(8,F,"USER"===e.role,"ASSISTANT"===e.role)),t.R7$(3),t.SpI(" ","USER"===e.role?"You":"HealthConnect AI"," "),t.R7$(2),t.JRh(o.formatTimestamp(e.timestamp)),t.R7$(2),t.SpI(" ",e.content," ")}}function k(n,i){1&n&&(t.j41(0,"div",52)(1,"div",44)(2,"div",45)(3,"div",46),t.nrm(4,"i",53),t.k0s(),t.j41(5,"div",48)(6,"span",49),t.EFF(7,"HealthConnect AI"),t.k0s(),t.j41(8,"span",50),t.EFF(9,"typing..."),t.k0s()()(),t.j41(10,"div",51)(11,"div",54),t.nrm(12,"span")(13,"span")(14,"span"),t.k0s()()()())}function P(n,i){if(1&n&&(t.j41(0,"div",40),t.DNE(1,E,12,11,"div",41),t.DNE(2,k,15,0,"div",42),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.messages),t.R7$(1),t.Y8G("ngIf",e.isSending)}}function I(n,i){if(1&n&&(t.j41(0,"div",55),t.nrm(1,"i",56),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function S(n,i){if(1&n&&(t.j41(0,"option",61),t.EFF(1),t.k0s()),2&n){const e=i.$implicit,o=t.XpG(2);t.Y8G("value",e),t.R7$(1),t.SpI(" ",o.getConversationTypeDisplayName(e)," ")}}function j(n,i){if(1&n){const e=t.RV6();t.j41(0,"div",57)(1,"label",58),t.EFF(2,"Conversation Type:"),t.k0s(),t.j41(3,"select",59),t.bIt("change",function(){t.eBV(e);const s=t.XpG();return t.Njj(s.onConversationTypeChange())}),t.DNE(4,S,2,2,"option",60),t.k0s()()}if(2&n){const e=t.XpG();t.R7$(4),t.Y8G("ngForOf",e.conversationTypes)}}function R(n,i){1&n&&t.nrm(0,"i",62)}function N(n,i){1&n&&(t.j41(0,"div",63)(1,"span",33),t.EFF(2,"Sending..."),t.k0s()())}let f=(()=>{class n{constructor(e,o,s,a){this.fb=e,this.aiHealthBotService=o,this.route=s,this.router=a,this.currentConversation=null,this.messages=[],this.isLoading=!1,this.isSending=!1,this.error="",this.conversationTypes=Object.values(l),this.selectedConversationType=l.GENERAL_HEALTH,this.subscriptions=[],this.shouldScrollToBottom=!1,this.chatForm=this.fb.group({message:["",[r.k0.required,r.k0.maxLength(2e3)]],conversationType:[l.GENERAL_HEALTH]})}ngOnInit(){this.loadConversationFromRoute(),this.subscribeToCurrentConversation()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}ngAfterViewChecked(){this.shouldScrollToBottom&&(this.scrollToBottom(),this.shouldScrollToBottom=!1)}loadConversationFromRoute(){const e=this.route.snapshot.paramMap.get("conversationId");e&&this.loadConversation(+e)}subscribeToCurrentConversation(){const e=this.aiHealthBotService.currentConversation$.subscribe(o=>{this.currentConversation=o,o&&(this.messages=o.messages||[],this.selectedConversationType=o.conversationType,this.chatForm.patchValue({conversationType:o.conversationType}),this.shouldScrollToBottom=!0)});this.subscriptions.push(e)}loadConversation(e){this.isLoading=!0,this.error="";const o=this.aiHealthBotService.getConversationDetails(e).subscribe({next:s=>{this.aiHealthBotService.setCurrentConversation(s),this.isLoading=!1},error:s=>{console.error("Failed to load conversation:",s),this.error="Failed to load conversation",this.isLoading=!1}});this.subscriptions.push(o)}onSendMessage(){if(this.chatForm.invalid||this.isSending)return;const e=this.chatForm.get("message")?.value?.trim();if(!e)return;this.isSending=!0,this.error="";const o={message:e,conversationId:this.currentConversation?.id,conversationType:this.selectedConversationType,isNewConversation:!this.currentConversation},s={id:Date.now(),content:e,role:"USER",timestamp:(new Date).toISOString()};this.messages.push(s),this.shouldScrollToBottom=!0,this.chatForm.patchValue({message:""});const a=this.aiHealthBotService.sendMessage(o).subscribe({next:c=>{const q={id:Date.now()+1,content:c.aiResponse,role:"ASSISTANT",timestamp:c.timestamp};this.messages.push(q),(c.isNewConversation||!this.currentConversation)&&this.router.navigate(["/ai-health-bot/chat",c.conversationId]),this.shouldScrollToBottom=!0,this.isSending=!1},error:c=>{console.error("Failed to send message:",c),this.error="Failed to send message. Please try again.",this.messages.pop(),this.isSending=!1}});this.subscriptions.push(a)}onNewConversation(){this.aiHealthBotService.setCurrentConversation(null),this.messages=[],this.selectedConversationType=l.GENERAL_HEALTH,this.chatForm.patchValue({message:"",conversationType:l.GENERAL_HEALTH}),this.router.navigate(["/ai-health-bot/chat"])}onConversationTypeChange(){this.selectedConversationType=this.chatForm.get("conversationType")?.value}getConversationTypeDisplayName(e){return this.aiHealthBotService.getConversationTypeDisplayName(e)}getConversationTypeIcon(e){return this.aiHealthBotService.getConversationTypeIcon(e)}scrollToBottom(){try{this.messagesContainer&&(this.messagesContainer.nativeElement.scrollTop=this.messagesContainer.nativeElement.scrollHeight)}catch(e){console.error("Error scrolling to bottom:",e)}}formatTimestamp(e){return new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}onKeyPress(e){"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),this.onSendMessage())}static{this.\u0275fac=function(o){return new(o||n)(t.rXU(r.ok),t.rXU(u),t.rXU(m.nX),t.rXU(m.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-ai-chat"]],viewQuery:function(o,s){if(1&o&&t.GBs(y,5),2&o){let a;t.mGM(a=t.lsd())&&(s.messagesContainer=a.first)}},decls:32,vars:13,consts:[[1,"ai-chat-container"],[1,"chat-header"],[1,"d-flex","justify-content-between","align-items-center"],[1,"d-flex","align-items-center"],[1,"fas","fa-robot","text-primary","me-2"],[1,"mb-0"],[1,"d-flex","gap-2"],[1,"btn","btn-outline-primary","btn-sm",3,"disabled","click"],[1,"fas","fa-plus","me-1"],["routerLink","/ai-health-bot/history",1,"btn","btn-outline-secondary","btn-sm"],[1,"fas","fa-history","me-1"],["class","conversation-info mt-2",4,"ngIf"],[1,"messages-container"],["messagesContainer",""],["class","text-center py-4",4,"ngIf"],["class","welcome-message",4,"ngIf"],["class","messages-list",4,"ngIf"],["class","alert alert-danger mx-3",4,"ngIf"],[1,"chat-input"],[3,"formGroup","ngSubmit"],["class","conversation-type-selector mb-2",4,"ngIf"],[1,"input-group"],["formControlName","message","placeholder","Type your health question here...","rows","2",1,"form-control",3,"disabled","keypress"],["type","submit",1,"btn","btn-primary",3,"disabled"],["class","fas fa-paper-plane",4,"ngIf"],["class","spinner-border spinner-border-sm","role","status",4,"ngIf"],[1,"text-end","mt-1"],[1,"text-muted"],[1,"conversation-info","mt-2"],[1,"d-flex","align-items-center","text-muted"],[1,"me-3"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],[1,"welcome-message"],[1,"text-center","py-5"],[1,"fas","fa-robot","fa-3x","text-primary","mb-3"],[1,"alert","alert-info"],[1,"fas","fa-info-circle","me-2"],[1,"messages-list"],["class","message",3,"ngClass",4,"ngFor","ngForOf"],["class","message ai-message",4,"ngIf"],[1,"message",3,"ngClass"],[1,"message-content"],[1,"message-header"],[1,"message-avatar"],[1,"fas",3,"ngClass"],[1,"message-info"],[1,"message-sender"],[1,"message-time"],[1,"message-text"],[1,"message","ai-message"],[1,"fas","fa-robot"],[1,"typing-indicator"],[1,"alert","alert-danger","mx-3"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"conversation-type-selector","mb-2"],[1,"form-label","small","text-muted"],["formControlName","conversationType",1,"form-select","form-select-sm",3,"change"],[3,"value",4,"ngFor","ngForOf"],[3,"value"],[1,"fas","fa-paper-plane"],["role","status",1,"spinner-border","spinner-border-sm"]],template:function(o,s){if(1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),t.nrm(4,"i",4),t.j41(5,"h4",5),t.EFF(6,"HealthConnect AI Assistant"),t.k0s()(),t.j41(7,"div",6)(8,"button",7),t.bIt("click",function(){return s.onNewConversation()}),t.nrm(9,"i",8),t.EFF(10," New Chat "),t.k0s(),t.j41(11,"button",9),t.nrm(12,"i",10),t.EFF(13," History "),t.k0s()()(),t.DNE(14,M,7,5,"div",11),t.k0s(),t.j41(15,"div",12,13),t.DNE(17,T,6,0,"div",14),t.DNE(18,O,12,0,"div",15),t.DNE(19,P,3,2,"div",16),t.k0s(),t.DNE(20,I,3,1,"div",17),t.j41(21,"div",18)(22,"form",19),t.bIt("ngSubmit",function(){return s.onSendMessage()}),t.DNE(23,j,5,1,"div",20),t.j41(24,"div",21)(25,"textarea",22),t.bIt("keypress",function(c){return s.onKeyPress(c)}),t.k0s(),t.j41(26,"button",23),t.DNE(27,R,1,0,"i",24),t.DNE(28,N,3,0,"div",25),t.k0s()(),t.j41(29,"div",26)(30,"small",27),t.EFF(31),t.k0s()()()()()),2&o){let a;t.R7$(8),t.Y8G("disabled",s.isSending),t.R7$(6),t.Y8G("ngIf",s.currentConversation),t.R7$(3),t.Y8G("ngIf",s.isLoading),t.R7$(1),t.Y8G("ngIf",0===s.messages.length&&!s.isLoading),t.R7$(1),t.Y8G("ngIf",s.messages.length>0),t.R7$(1),t.Y8G("ngIf",s.error),t.R7$(2),t.Y8G("formGroup",s.chatForm),t.R7$(1),t.Y8G("ngIf",!s.currentConversation),t.R7$(2),t.Y8G("disabled",s.isSending),t.R7$(1),t.Y8G("disabled",s.chatForm.invalid||s.isSending),t.R7$(1),t.Y8G("ngIf",!s.isSending),t.R7$(1),t.Y8G("ngIf",s.isSending),t.R7$(3),t.SpI(" ",(null==(a=s.chatForm.get("message"))||null==a.value?null:a.value.length)||0,"/2000 ")}},dependencies:[d.YU,d.Sq,d.bT,r.qT,r.xH,r.y7,r.me,r.wz,r.BC,r.cb,r.j4,r.JD,m.Wk],styles:[".ai-chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:calc(100vh - 120px);background:#f8f9fa}.chat-header[_ngcontent-%COMP%]{background:white;padding:1rem;border-bottom:1px solid #dee2e6;box-shadow:0 2px 4px #0000001a}.conversation-info[_ngcontent-%COMP%]{font-size:.9rem}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%)}.welcome-message[_ngcontent-%COMP%]{max-width:600px;margin:0 auto}.messages-list[_ngcontent-%COMP%]{max-width:800px;margin:0 auto}.message[_ngcontent-%COMP%]{margin-bottom:1.5rem}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{margin-left:auto;margin-right:0;max-width:70%;background:#007bff;color:#fff}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%]{background:#0056b3}.message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{margin-left:0;margin-right:auto;max-width:80%;background:white;color:#333;border:1px solid #dee2e6}.message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-avatar[_ngcontent-%COMP%]{background:#28a745;color:#fff}.message-content[_ngcontent-%COMP%]{border-radius:1rem;padding:1rem;box-shadow:0 2px 8px #0000001a}.message-header[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.5rem}.message-avatar[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:flex;align-items:center;justify-content:center;margin-right:.75rem;font-size:.9rem}.message-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}.message-sender[_ngcontent-%COMP%]{font-weight:600;font-size:.9rem}.message-time[_ngcontent-%COMP%]{font-size:.75rem;opacity:.7}.message-text[_ngcontent-%COMP%]{line-height:1.5;white-space:pre-wrap;word-wrap:break-word}.typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:4px}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background:#6c757d;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:0s}@keyframes _ngcontent-%COMP%_typing{0%,80%,to{transform:scale(.8);opacity:.5}40%{transform:scale(1);opacity:1}}.chat-input[_ngcontent-%COMP%]{background:white;padding:1rem;border-top:1px solid #dee2e6;box-shadow:0 -2px 8px #0000001a}.conversation-type-selector[_ngcontent-%COMP%]{max-width:300px}.input-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{resize:none;border-radius:1rem 0 0 1rem}.input-group[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #007bff40}.input-group[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border-radius:0 1rem 1rem 0;min-width:60px}@media (max-width: 768px){.ai-chat-container[_ngcontent-%COMP%]{height:calc(100vh - 80px)}.chat-header[_ngcontent-%COMP%]{padding:.75rem}.chat-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.1rem}.chat-header[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%]{font-size:.8rem;padding:.25rem .5rem}.messages-container[_ngcontent-%COMP%]{padding:.75rem}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:90%}.message-content[_ngcontent-%COMP%], .chat-input[_ngcontent-%COMP%]{padding:.75rem}}@media (prefers-color-scheme: dark){.ai-chat-container[_ngcontent-%COMP%]{background:#1a1a1a}.chat-header[_ngcontent-%COMP%]{background:#2d2d2d;border-bottom-color:#404040;color:#e9ecef}.messages-container[_ngcontent-%COMP%]{background:linear-gradient(135deg,#1a1a1a 0%,#2d2d2d 100%)}.ai-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:#2d2d2d;border-color:#404040;color:#e9ecef}.chat-input[_ngcontent-%COMP%]{background:#2d2d2d;border-top-color:#404040}}"]})}}return n})();function w(n,i){if(1&n){const e=t.RV6();t.j41(0,"button",30),t.bIt("click",function(){t.eBV(e);const s=t.XpG();return t.Njj(s.clearSearch())}),t.nrm(1,"i",31),t.k0s()}}function A(n,i){if(1&n&&(t.j41(0,"option",32),t.EFF(1),t.k0s()),2&n){const e=i.$implicit,o=t.XpG();t.Y8G("value",e),t.R7$(1),t.SpI(" ",o.getConversationTypeDisplayName(e)," ")}}function H(n,i){1&n&&(t.j41(0,"div",33)(1,"div",34)(2,"span",35),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",36),t.EFF(5,"Loading conversation history..."),t.k0s()())}function G(n,i){if(1&n&&(t.j41(0,"div",37),t.nrm(1,"i",38),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function $(n,i){1&n&&(t.j41(0,"h5",44),t.EFF(1,"No conversations yet"),t.k0s())}function L(n,i){1&n&&(t.j41(0,"h5",44),t.EFF(1,"No conversations match your filters"),t.k0s())}function D(n,i){1&n&&(t.j41(0,"p",44),t.EFF(1," Start your first conversation with the AI Health Assistant "),t.k0s())}function Y(n,i){if(1&n){const e=t.RV6();t.j41(0,"button",45),t.bIt("click",function(){t.eBV(e);const s=t.XpG(2);return t.Njj(s.startNewConversation())}),t.nrm(1,"i",46),t.EFF(2," Start First Conversation "),t.k0s()}}function B(n,i){if(1&n){const e=t.RV6();t.j41(0,"button",47),t.bIt("click",function(){t.eBV(e);const s=t.XpG(2);return s.clearSearch(),s.selectedType="ALL",t.Njj(s.onTypeFilterChange())}),t.nrm(1,"i",48),t.EFF(2," Clear Filters "),t.k0s()}}function U(n,i){if(1&n&&(t.j41(0,"div",39),t.nrm(1,"i",40),t.DNE(2,$,2,0,"h5",41),t.DNE(3,L,2,0,"h5",41),t.DNE(4,D,2,0,"p",41),t.DNE(5,Y,3,0,"button",42),t.DNE(6,B,3,0,"button",43),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.Y8G("ngIf",0===e.conversations.length),t.R7$(1),t.Y8G("ngIf",e.conversations.length>0),t.R7$(1),t.Y8G("ngIf",0===e.conversations.length),t.R7$(1),t.Y8G("ngIf",0===e.conversations.length),t.R7$(1),t.Y8G("ngIf",e.conversations.length>0)}}function X(n,i){1&n&&(t.j41(0,"span",68),t.nrm(1,"i",69),t.EFF(2," Shared "),t.k0s())}function V(n,i){if(1&n){const e=t.RV6();t.j41(0,"div",51),t.bIt("click",function(){const a=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.openConversation(a))}),t.j41(1,"div",52)(2,"div",3)(3,"div",53),t.nrm(4,"i"),t.k0s(),t.j41(5,"div",54)(6,"h6",5),t.EFF(7),t.k0s(),t.j41(8,"small",44),t.EFF(9),t.k0s()()(),t.j41(10,"div",55)(11,"div",56)(12,"small",44),t.EFF(13),t.k0s(),t.j41(14,"small",57),t.EFF(15),t.k0s()()()(),t.j41(16,"div",58)(17,"p",59),t.EFF(18),t.k0s()(),t.j41(19,"div",60)(20,"div",61)(21,"div",62)(22,"span",63),t.nrm(23,"i",64),t.EFF(24),t.k0s(),t.DNE(25,X,3,0,"span",65),t.k0s(),t.j41(26,"div",66),t.nrm(27,"i",67),t.k0s()()()()}if(2&n){const e=i.$implicit,o=t.XpG(2);t.R7$(4),t.ZvI("fas fa-",o.getConversationTypeIcon(e.conversationType),""),t.R7$(3),t.JRh(e.title),t.R7$(2),t.SpI(" ",o.getConversationTypeDisplayName(e.conversationType)," "),t.R7$(4),t.JRh(o.formatDate(e.updatedAt)),t.R7$(2),t.JRh(o.formatTime(e.updatedAt)),t.R7$(3),t.JRh(o.getConversationPreview(e)),t.R7$(6),t.Lme(" ",e.messageCount," message",1!==e.messageCount?"s":""," "),t.R7$(1),t.Y8G("ngIf",e.isSharedWithDoctor)}}function z(n,i){if(1&n&&(t.j41(0,"div",49),t.DNE(1,V,28,11,"div",50),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.filteredConversations)}}const W=[{path:"",canActivate:[v.q],children:[{path:"",redirectTo:"chat",pathMatch:"full"},{path:"chat",component:f,data:{title:"AI Health Assistant"}},{path:"chat/:conversationId",component:f,data:{title:"AI Health Assistant"}},{path:"history",component:(()=>{class n{constructor(e,o){this.aiHealthBotService=e,this.router=o,this.conversations=[],this.filteredConversations=[],this.isLoading=!0,this.error="",this.searchTerm="",this.selectedType="ALL",this.conversationTypes=Object.values(l),this.subscriptions=[]}ngOnInit(){this.loadConversations()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}loadConversations(){this.isLoading=!0,this.error="";const e=this.aiHealthBotService.getUserConversations().subscribe({next:o=>{this.conversations=o,this.applyFilters(),this.isLoading=!1},error:o=>{console.error("Failed to load conversations:",o),this.error="Failed to load conversation history",this.isLoading=!1}});this.subscriptions.push(e)}onSearchChange(){this.applyFilters()}onTypeFilterChange(){this.applyFilters()}applyFilters(){let e=[...this.conversations];if(this.searchTerm.trim()){const o=this.searchTerm.toLowerCase();e=e.filter(s=>s.title.toLowerCase().includes(o)||s.summary?.toLowerCase().includes(o)||s.lastMessage?.toLowerCase().includes(o))}"ALL"!==this.selectedType&&(e=e.filter(o=>o.conversationType===this.selectedType)),this.filteredConversations=e}openConversation(e){this.aiHealthBotService.setCurrentConversation(e),this.router.navigate(["/ai-health-bot/chat",e.id])}startNewConversation(){this.router.navigate(["/ai-health-bot/chat"])}getConversationTypeDisplayName(e){return this.aiHealthBotService.getConversationTypeDisplayName(e)}getConversationTypeIcon(e){return this.aiHealthBotService.getConversationTypeIcon(e)}formatDate(e){const o=new Date(e),a=Math.abs((new Date).getTime()-o.getTime()),c=Math.ceil(a/864e5);return 1===c?"Today":2===c?"Yesterday":c<=7?c-1+" days ago":o.toLocaleDateString()}formatTime(e){return new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}getConversationPreview(e){return e.lastMessage?e.lastMessage.length>100?e.lastMessage.substring(0,97)+"...":e.lastMessage:"No messages yet"}refreshConversations(){this.loadConversations()}clearSearch(){this.searchTerm="",this.applyFilters()}static{this.\u0275fac=function(o){return new(o||n)(t.rXU(u),t.rXU(m.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-conversation-history"]],decls:35,vars:13,consts:[[1,"conversation-history-container"],[1,"history-header"],[1,"d-flex","justify-content-between","align-items-center","mb-3"],[1,"d-flex","align-items-center"],[1,"fas","fa-history","text-primary","me-2"],[1,"mb-0"],[1,"d-flex","gap-2"],[1,"btn","btn-primary","btn-sm",3,"click"],[1,"fas","fa-plus","me-1"],[1,"btn","btn-outline-secondary","btn-sm",3,"disabled","click"],[1,"fas","fa-sync-alt","me-1"],[1,"filters-section"],[1,"row","g-3"],[1,"col-md-6"],[1,"input-group"],[1,"input-group-text"],[1,"fas","fa-search"],["type","text","placeholder","Search conversations...",1,"form-control",3,"ngModel","ngModelChange","input"],["class","btn btn-outline-secondary","type","button",3,"click",4,"ngIf"],[1,"col-md-4"],[1,"form-select",3,"ngModel","ngModelChange","change"],["value","ALL"],[3,"value",4,"ngFor","ngForOf"],[1,"col-md-2"],[1,"text-muted","small"],[1,"history-content"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger",4,"ngIf"],["class","empty-state text-center py-5",4,"ngIf"],["class","conversations-list",4,"ngIf"],["type","button",1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-times"],[3,"value"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],[1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"empty-state","text-center","py-5"],[1,"fas","fa-comments","fa-3x","text-muted","mb-3"],["class","text-muted",4,"ngIf"],["class","btn btn-primary",3,"click",4,"ngIf"],["class","btn btn-outline-secondary",3,"click",4,"ngIf"],[1,"text-muted"],[1,"btn","btn-primary",3,"click"],[1,"fas","fa-plus","me-2"],[1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-filter","me-2"],[1,"conversations-list"],["class","conversation-card",3,"click",4,"ngFor","ngForOf"],[1,"conversation-card",3,"click"],[1,"conversation-header"],[1,"conversation-type-icon"],[1,"conversation-title"],[1,"conversation-meta"],[1,"conversation-date"],[1,"text-muted","d-block"],[1,"conversation-preview"],[1,"mb-2"],[1,"conversation-footer"],[1,"d-flex","justify-content-between","align-items-center"],[1,"conversation-stats"],[1,"badge","bg-light","text-dark","me-2"],[1,"fas","fa-comments","me-1"],["class","badge bg-success",4,"ngIf"],[1,"conversation-actions"],[1,"fas","fa-chevron-right","text-muted"],[1,"badge","bg-success"],[1,"fas","fa-share","me-1"]],template:function(o,s){1&o&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3),t.nrm(4,"i",4),t.j41(5,"h4",5),t.EFF(6,"Conversation History"),t.k0s()(),t.j41(7,"div",6)(8,"button",7),t.bIt("click",function(){return s.startNewConversation()}),t.nrm(9,"i",8),t.EFF(10," New Chat "),t.k0s(),t.j41(11,"button",9),t.bIt("click",function(){return s.refreshConversations()}),t.nrm(12,"i",10),t.EFF(13," Refresh "),t.k0s()()(),t.j41(14,"div",11)(15,"div",12)(16,"div",13)(17,"div",14)(18,"span",15),t.nrm(19,"i",16),t.k0s(),t.j41(20,"input",17),t.bIt("ngModelChange",function(c){return s.searchTerm=c})("input",function(){return s.onSearchChange()}),t.k0s(),t.DNE(21,w,2,0,"button",18),t.k0s()(),t.j41(22,"div",19)(23,"select",20),t.bIt("ngModelChange",function(c){return s.selectedType=c})("change",function(){return s.onTypeFilterChange()}),t.j41(24,"option",21),t.EFF(25,"All Types"),t.k0s(),t.DNE(26,A,2,2,"option",22),t.k0s()(),t.j41(27,"div",23)(28,"div",24),t.EFF(29),t.k0s()()()()(),t.j41(30,"div",25),t.DNE(31,H,6,0,"div",26),t.DNE(32,G,3,1,"div",27),t.DNE(33,U,7,5,"div",28),t.DNE(34,z,2,1,"div",29),t.k0s()()),2&o&&(t.R7$(11),t.Y8G("disabled",s.isLoading),t.R7$(1),t.AVh("fa-spin",s.isLoading),t.R7$(8),t.Y8G("ngModel",s.searchTerm),t.R7$(1),t.Y8G("ngIf",s.searchTerm),t.R7$(2),t.Y8G("ngModel",s.selectedType),t.R7$(3),t.Y8G("ngForOf",s.conversationTypes),t.R7$(3),t.Lme(" ",s.filteredConversations.length," conversation",1!==s.filteredConversations.length?"s":""," "),t.R7$(2),t.Y8G("ngIf",s.isLoading),t.R7$(1),t.Y8G("ngIf",s.error),t.R7$(1),t.Y8G("ngIf",!s.isLoading&&0===s.filteredConversations.length&&!s.error),t.R7$(1),t.Y8G("ngIf",!s.isLoading&&s.filteredConversations.length>0))},dependencies:[d.Sq,d.bT,r.xH,r.y7,r.me,r.wz,r.BC,r.vS],styles:[".conversation-history-container[_ngcontent-%COMP%]{padding:1.5rem;background:#f8f9fa;min-height:calc(100vh - 120px)}.history-header[_ngcontent-%COMP%]{background:white;padding:1.5rem;border-radius:.5rem;box-shadow:0 2px 8px #0000001a;margin-bottom:1.5rem}.filters-section[_ngcontent-%COMP%]{border-top:1px solid #dee2e6;padding-top:1rem;margin-top:1rem}.history-content[_ngcontent-%COMP%]{background:white;border-radius:.5rem;box-shadow:0 2px 8px #0000001a;min-height:400px}.conversations-list[_ngcontent-%COMP%]{padding:1rem}.conversation-card[_ngcontent-%COMP%]{border:1px solid #dee2e6;border-radius:.5rem;padding:1rem;margin-bottom:1rem;cursor:pointer;transition:all .2s ease;background:white}.conversation-card[_ngcontent-%COMP%]:hover{border-color:#007bff;box-shadow:0 4px 12px #007bff26;transform:translateY(-2px)}.conversation-card[_ngcontent-%COMP%]:last-child{margin-bottom:0}.conversation-header[_ngcontent-%COMP%]{display:flex;justify-content:between;align-items:flex-start;margin-bottom:.75rem}.conversation-type-icon[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;background:linear-gradient(135deg,#007bff,#0056b3);color:#fff;display:flex;align-items:center;justify-content:center;margin-right:.75rem;flex-shrink:0}.conversation-title[_ngcontent-%COMP%]{flex:1}.conversation-title[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#333;font-weight:600;line-height:1.2}.conversation-meta[_ngcontent-%COMP%]{text-align:right;flex-shrink:0}.conversation-date[_ngcontent-%COMP%]{line-height:1.2}.conversation-preview[_ngcontent-%COMP%]{color:#6c757d;font-size:.9rem;line-height:1.4;margin-bottom:.75rem}.conversation-preview[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:0}.conversation-footer[_ngcontent-%COMP%]{border-top:1px solid #f8f9fa;padding-top:.75rem}.conversation-stats[_ngcontent-%COMP%]{display:flex;align-items:center;flex-wrap:wrap;gap:.5rem}.conversation-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.empty-state[_ngcontent-%COMP%]{padding:3rem 1rem}@media (max-width: 768px){.conversation-history-container[_ngcontent-%COMP%], .history-header[_ngcontent-%COMP%]{padding:1rem}.filters-section[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{--bs-gutter-x: .75rem}.conversation-card[_ngcontent-%COMP%]{padding:.75rem}.conversation-header[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.5rem}.conversation-meta[_ngcontent-%COMP%]{text-align:left;width:100%}.conversation-type-icon[_ngcontent-%COMP%]{width:32px;height:32px;margin-right:.5rem}.conversation-stats[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;gap:.25rem}}.fa-spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fa-spin 1s infinite linear}@keyframes _ngcontent-%COMP%_fa-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.badge[_ngcontent-%COMP%]{font-size:.75rem}.badge.bg-light[_ngcontent-%COMP%]{color:#495057!important}.input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}@media (prefers-color-scheme: dark){.conversation-history-container[_ngcontent-%COMP%]{background:#1a1a1a}.history-header[_ngcontent-%COMP%], .history-content[_ngcontent-%COMP%], .conversation-card[_ngcontent-%COMP%]{background:#2d2d2d;border-color:#404040;color:#e9ecef}.conversation-card[_ngcontent-%COMP%]:hover{border-color:#0d6efd;background:#343a40}.conversation-title[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#e9ecef}.conversation-preview[_ngcontent-%COMP%]{color:#adb5bd}.conversation-footer[_ngcontent-%COMP%]{border-top-color:#404040}.badge.bg-light[_ngcontent-%COMP%]{background-color:#495057!important;color:#e9ecef!important}}"]})}}return n})(),data:{title:"Conversation History"}}]}];let Q=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[m.iI.forChild(W),m.iI]})}}return n})();var K=g(3887);let Z=(()=>{class n{static{this.\u0275fac=function(o){return new(o||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[d.MD,r.YN,r.X1,Q,K.G]})}}return n})()}}]);