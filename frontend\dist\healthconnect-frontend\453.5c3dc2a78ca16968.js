"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[453],{3453:(S,b,a)=>{a.r(b),a.d(b,{DebugModule:()=>P});var g=a(177),i=a(2434),r=a(4341),t=a(6276),f=a(271),C=a(8359),d=a(2869),T=a(25),c=a.n(T),m=a(5312),u=a(5538),v=a(8010);function k(o,p){if(1&o&&(t.j41(0,"div",19)(1,"small",20),t.EFF(2),t.nI1(3,"date"),t.k0s(),t.j41(4,"span",21),t.EFF(5),t.k0s()()),2&o){const e=p.$implicit;t.HbH(e.type),t.R7$(2),t.JRh(t.i5U(3,4,e.timestamp,"HH:mm:ss.SSS")),t.R7$(3),t.JRh(e.message)}}function F(o,p){if(1&o&&(t.j41(0,"div",6)(1,"label",7),t.EFF(2,"Test Results:"),t.k0s(),t.j41(3,"div",17),t.DNE(4,k,6,7,"div",18),t.k0s()()),2&o){const e=t.XpG();t.R7$(4),t.Y8G("ngForOf",e.testResults)}}function M(o,p){if(1&o){const e=t.RV6();t.j41(0,"button",22),t.bIt("click",function(){t.eBV(e);const s=t.XpG();return t.Njj(s.clearResults())}),t.EFF(1," Clear Results "),t.k0s()}}let _=(()=>{class o{constructor(e,n){this.chatService=e,this.authService=n,this.isConnected=!1,this.connectionStatusText="Checking...",this.connectionStatusClass="connection-status status-connecting",this.connectionIconClass="fas fa-circle-notch fa-spin",this.testMessage="Hello WebSocket!",this.isTestingConnection=!1,this.testResults=[],this.testClient=null,this.subscription=new C.yU}ngOnInit(){this.subscription.add(this.chatService.connectionStatus$.subscribe(e=>{this.isConnected=e,this.updateConnectionStatus(e)}))}ngOnDestroy(){this.subscription.unsubscribe(),this.testClient&&this.testClient.deactivate()}updateConnectionStatus(e){e?(this.connectionStatusText="Connected",this.connectionStatusClass="connection-status status-connected",this.connectionIconClass="fas fa-check-circle"):(this.connectionStatusText="Disconnected",this.connectionStatusClass="connection-status status-disconnected",this.connectionIconClass="fas fa-times-circle")}testConnection(){this.isTestingConnection=!0,this.addTestResult("Testing WebSocket connection...","sent");const e=this.authService.getToken();if(!e)return this.addTestResult("Error: No authentication token","error"),void(this.isTestingConnection=!1);this.testClient=new d.K({webSocketFactory:()=>new(c())(m.c.wsUrl),connectHeaders:{Authorization:`Bearer ${e}`},onConnect:n=>{this.addTestResult("\u2705 Test connection successful","received"),this.isTestingConnection=!1,this.testClient?.subscribe("/topic/test",s=>{this.addTestResult(`\u{1f4e8} Received: ${s.body}`,"received")})},onStompError:n=>{this.addTestResult(`\u274c STOMP Error: ${n.body}`,"error"),this.isTestingConnection=!1},onWebSocketError:n=>{this.addTestResult(`\u274c WebSocket Error: ${n}`,"error"),this.isTestingConnection=!1}}),this.testClient.activate()}sendTestMessage(){if(!this.testClient?.connected)return void this.addTestResult("Error: Test client not connected","error");const e=this.authService.getToken();if(e)try{this.testClient.publish({destination:"/app/test",body:this.testMessage,headers:{Authorization:`Bearer ${e}`}}),this.addTestResult(`\u{1f4e4} Sent: ${this.testMessage}`,"sent"),this.testMessage="Hello WebSocket!"}catch(n){this.addTestResult(`\u274c Send Error: ${n}`,"error")}else this.addTestResult("Error: No authentication token","error")}onKeyPress(e){"Enter"===e.key&&this.sendTestMessage()}addTestResult(e,n){this.testResults.push({timestamp:new Date,message:e,type:n}),this.testResults.length>20&&(this.testResults=this.testResults.slice(-20))}clearResults(){this.testResults=[]}static{this.\u0275fac=function(n){return new(n||o)(t.rXU(u.m),t.rXU(v.u))}}static{this.\u0275cmp=t.VBU({type:o,selectors:[["app-websocket-test"]],decls:26,vars:12,consts:[[1,"websocket-test-panel"],[1,"card"],[1,"card-header"],[1,"mb-0"],[1,"fas","fa-network-wired","me-2"],[1,"card-body"],[1,"mb-3"],[1,"form-label"],[1,"d-flex","align-items-center"],[1,"btn","btn-sm","btn-outline-primary","ms-2",3,"disabled","click"],[1,"fas","fa-sync"],["for","testMessage",1,"form-label"],[1,"input-group"],["type","text","id","testMessage","placeholder","Enter test message...",1,"form-control",3,"ngModel","ngModelChange","keypress"],[1,"btn","btn-primary",3,"disabled","click"],["class","mb-3",4,"ngIf"],["class","btn btn-sm btn-outline-secondary",3,"click",4,"ngIf"],[1,"test-results"],["class","test-result-item",3,"class",4,"ngFor","ngForOf"],[1,"test-result-item"],[1,"timestamp"],[1,"message"],[1,"btn","btn-sm","btn-outline-secondary",3,"click"]],template:function(n,s){1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h5",3),t.nrm(4,"i",4),t.EFF(5," WebSocket Connection Test "),t.k0s()(),t.j41(6,"div",5)(7,"div",6)(8,"label",7),t.EFF(9,"Connection Status:"),t.k0s(),t.j41(10,"div",8)(11,"span"),t.nrm(12,"i"),t.EFF(13),t.k0s(),t.j41(14,"button",9),t.bIt("click",function(){return s.testConnection()}),t.nrm(15,"i",10),t.EFF(16," Test Connection "),t.k0s()()(),t.j41(17,"div",6)(18,"label",11),t.EFF(19,"Test Message:"),t.k0s(),t.j41(20,"div",12)(21,"input",13),t.bIt("ngModelChange",function(h){return s.testMessage=h})("keypress",function(h){return s.onKeyPress(h)}),t.k0s(),t.j41(22,"button",14),t.bIt("click",function(){return s.sendTestMessage()}),t.EFF(23," Send Test "),t.k0s()()(),t.DNE(24,F,5,1,"div",15),t.DNE(25,M,2,0,"button",16),t.k0s()()()),2&n&&(t.R7$(11),t.HbH(s.connectionStatusClass),t.R7$(1),t.HbH(s.connectionIconClass),t.R7$(1),t.SpI(" ",s.connectionStatusText," "),t.R7$(1),t.Y8G("disabled",s.isTestingConnection),t.R7$(1),t.AVh("fa-spin",s.isTestingConnection),t.R7$(6),t.Y8G("ngModel",s.testMessage),t.R7$(1),t.Y8G("disabled",!s.isConnected||!s.testMessage.trim()),t.R7$(2),t.Y8G("ngIf",s.testResults.length>0),t.R7$(1),t.Y8G("ngIf",s.testResults.length>0))},dependencies:[g.Sq,g.bT,r.me,r.BC,r.vS,g.vh],styles:[".websocket-test-panel[_ngcontent-%COMP%]{margin:1rem 0}.connection-status[_ngcontent-%COMP%]{font-weight:500}.status-connected[_ngcontent-%COMP%]{color:#28a745}.status-disconnected[_ngcontent-%COMP%]{color:#dc3545}.status-connecting[_ngcontent-%COMP%]{color:#ffc107}.test-results[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto;border:1px solid #dee2e6;border-radius:.375rem;padding:.5rem;background-color:#f8f9fa}.test-result-item[_ngcontent-%COMP%]{display:flex;margin-bottom:.25rem;font-family:monospace;font-size:.875rem}.test-result-item.sent[_ngcontent-%COMP%]{color:#06c}.test-result-item.received[_ngcontent-%COMP%]{color:#28a745}.test-result-item.error[_ngcontent-%COMP%]{color:#dc3545}.timestamp[_ngcontent-%COMP%]{margin-right:.5rem;color:#6c757d;min-width:80px}.message[_ngcontent-%COMP%]{flex:1}"]})}}return o})();var R=a(5877);function E(o,p){if(1&o&&(t.j41(0,"option",25),t.EFF(1),t.k0s()),2&o){const e=p.$implicit,n=t.XpG();let s;t.Y8G("value",e.id),t.R7$(1),t.SpI(" Chat with ",null==(s=n.getOtherParticipant(e))?null:s.fullName," ")}}function y(o,p){if(1&o&&(t.j41(0,"div",26)(1,"small",27),t.EFF(2),t.nI1(3,"date"),t.k0s(),t.j41(4,"span",28),t.EFF(5),t.k0s()()),2&o){const e=p.$implicit;t.HbH(e.type),t.R7$(2),t.JRh(t.i5U(3,4,e.timestamp,"HH:mm:ss.SSS")),t.R7$(3),t.JRh(e.message)}}let I=(()=>{class o{constructor(e,n){this.chatService=e,this.authService=n,this.messageCount=10,this.autoInterval=2e3,this.selectedChatId=null,this.messagesSent=0,this.messagesReceived=0,this.connectionStatus=!1,this.testDuration=0,this.isTesting=!1,this.autoTesting=!1,this.availableChats=[],this.testLogs=[],this.subscriptions=[]}ngOnInit(){this.subscribeToServices(),this.loadAvailableChats()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe()),this.stopAutoTest(),this.durationInterval&&clearInterval(this.durationInterval)}subscribeToServices(){this.subscriptions.push(this.chatService.connectionStatus$.subscribe(e=>{this.connectionStatus=e,this.addLog("Connection "+(e?"established":"lost"),e?"success":"error")})),this.subscriptions.push(this.chatService.messages$.subscribe(e=>{this.selectedChatId&&e.chatId===this.selectedChatId&&(this.messagesReceived++,this.addLog(`Message received: ${e.content.substring(0,30)}...`,"info"))}))}loadAvailableChats(){this.chatService.getUserChats().subscribe({next:e=>{this.availableChats=e},error:e=>{this.addLog(`Failed to load chats: ${e.message}`,"error")}})}sendMultipleMessages(){if(this.selectedChatId&&!this.isTesting){this.isTesting=!0,this.startTestTimer(),this.addLog(`Starting to send ${this.messageCount} messages`,"info");for(let e=1;e<=this.messageCount;e++)setTimeout(()=>{try{const n=`Test message ${e}/${this.messageCount} - ${(new Date).toISOString()}`;this.chatService.sendMessage(this.selectedChatId,n),this.messagesSent++,e===this.messageCount&&(this.isTesting=!1,this.addLog(`Completed sending ${this.messageCount} messages`,"success"))}catch(n){this.addLog(`Failed to send message ${e}: ${n}`,"error")}},100*e)}}toggleAutoTest(){this.autoTesting?this.stopAutoTest():this.startAutoTest()}startAutoTest(){this.selectedChatId&&(this.autoTesting=!0,this.startTestTimer(),this.addLog(`Starting auto test with ${this.autoInterval}ms interval`,"info"),this.autoTestSubscription=(0,R.Y)(this.autoInterval).subscribe(()=>{try{const e=`Auto test message - ${(new Date).toLocaleTimeString()}`;this.chatService.sendMessage(this.selectedChatId,e),this.messagesSent++}catch(e){this.addLog(`Auto test error: ${e}`,"error"),this.stopAutoTest()}}))}stopAutoTest(){this.autoTesting=!1,this.autoTestSubscription&&(this.autoTestSubscription.unsubscribe(),this.autoTestSubscription=void 0),this.addLog("Auto test stopped","info")}sendLongMessage(){if(!this.selectedChatId||this.isTesting)return;const e="This is a very long message that tests how the chat handles large amounts of text. ".repeat(20)+"It should wrap properly and not break the UI layout. The input field should remain functional after sending this message.";try{this.chatService.sendMessage(this.selectedChatId,e),this.messagesSent++,this.addLog("Long message sent successfully","success")}catch(n){this.addLog(`Failed to send long message: ${n}`,"error")}}startTestTimer(){this.testStartTime||(this.testStartTime=new Date,this.durationInterval=setInterval(()=>{this.testStartTime&&(this.testDuration=Math.floor((Date.now()-this.testStartTime.getTime())/1e3))},1e3))}clearResults(){this.messagesSent=0,this.messagesReceived=0,this.testDuration=0,this.testLogs=[],this.testStartTime=void 0,this.durationInterval&&(clearInterval(this.durationInterval),this.durationInterval=void 0)}getOtherParticipant(e){return"PATIENT"===this.authService.getCurrentUser()?.role?e.doctor:e.patient}addLog(e,n){this.testLogs.push({timestamp:new Date,message:e,type:n}),this.testLogs.length>50&&(this.testLogs=this.testLogs.slice(-50))}static{this.\u0275fac=function(n){return new(n||o)(t.rXU(u.m),t.rXU(v.u))}}static{this.\u0275cmp=t.VBU({type:o,selectors:[["app-chat-stress-test"]],decls:63,vars:20,consts:[[1,"chat-stress-test"],[1,"card"],[1,"card-header"],[1,"mb-0"],[1,"fas","fa-vial","me-2"],[1,"card-body"],[1,"row"],[1,"col-md-6"],[1,"mb-3"],[1,"form-label"],[1,"input-group"],["type","number","min","1","max","100","placeholder","Number of messages",1,"form-control",3,"ngModel","ngModelChange"],[1,"btn","btn-primary",3,"disabled","click"],["type","number","min","500","max","10000","placeholder","Interval (ms)",1,"form-control",3,"ngModel","ngModelChange"],[1,"btn",3,"disabled","click"],[1,"btn","btn-warning",3,"disabled","click"],[1,"form-select",3,"ngModel","ngModelChange"],["value",""],[3,"value",4,"ngFor","ngForOf"],[1,"test-stats"],[1,"stat-item"],[1,"test-log","mt-3"],[1,"log-container"],["class","log-entry",3,"class",4,"ngFor","ngForOf"],[1,"btn","btn-sm","btn-outline-secondary","mt-2",3,"click"],[3,"value"],[1,"log-entry"],[1,"timestamp"],[1,"message"]],template:function(n,s){1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h5",3),t.nrm(4,"i",4),t.EFF(5," Chat Stress Test "),t.k0s()(),t.j41(6,"div",5)(7,"div",6)(8,"div",7)(9,"h6"),t.EFF(10,"Test Controls"),t.k0s(),t.j41(11,"div",8)(12,"label",9),t.EFF(13,"Send Multiple Messages:"),t.k0s(),t.j41(14,"div",10)(15,"input",11),t.bIt("ngModelChange",function(h){return s.messageCount=h}),t.k0s(),t.j41(16,"button",12),t.bIt("click",function(){return s.sendMultipleMessages()}),t.EFF(17),t.k0s()()(),t.j41(18,"div",8)(19,"label",9),t.EFF(20,"Auto Message Test:"),t.k0s(),t.j41(21,"div",10)(22,"input",13),t.bIt("ngModelChange",function(h){return s.autoInterval=h}),t.k0s(),t.j41(23,"button",14),t.bIt("click",function(){return s.toggleAutoTest()}),t.EFF(24),t.k0s()()(),t.j41(25,"div",8)(26,"button",15),t.bIt("click",function(){return s.sendLongMessage()}),t.EFF(27," Send Long Message "),t.k0s()(),t.j41(28,"div",8)(29,"label",9),t.EFF(30,"Select Chat for Testing:"),t.k0s(),t.j41(31,"select",16),t.bIt("ngModelChange",function(h){return s.selectedChatId=h}),t.j41(32,"option",17),t.EFF(33,"Select a chat..."),t.k0s(),t.DNE(34,E,2,2,"option",18),t.k0s()()(),t.j41(35,"div",7)(36,"h6"),t.EFF(37,"Test Results"),t.k0s(),t.j41(38,"div",19)(39,"div",20)(40,"strong"),t.EFF(41,"Messages Sent:"),t.k0s(),t.EFF(42),t.k0s(),t.j41(43,"div",20)(44,"strong"),t.EFF(45,"Messages Received:"),t.k0s(),t.EFF(46),t.k0s(),t.j41(47,"div",20)(48,"strong"),t.EFF(49,"Connection Status:"),t.k0s(),t.j41(50,"span"),t.EFF(51),t.k0s()(),t.j41(52,"div",20)(53,"strong"),t.EFF(54,"Test Duration:"),t.k0s(),t.EFF(55),t.k0s()(),t.j41(56,"div",21)(57,"h6"),t.EFF(58,"Test Log:"),t.k0s(),t.j41(59,"div",22),t.DNE(60,y,6,7,"div",23),t.k0s()(),t.j41(61,"button",24),t.bIt("click",function(){return s.clearResults()}),t.EFF(62," Clear Results "),t.k0s()()()()()()),2&n&&(t.R7$(15),t.Y8G("ngModel",s.messageCount),t.R7$(1),t.Y8G("disabled",!s.selectedChatId||s.isTesting),t.R7$(1),t.SpI(" Send ",s.messageCount," Messages "),t.R7$(5),t.Y8G("ngModel",s.autoInterval),t.R7$(1),t.AVh("btn-success",!s.autoTesting)("btn-danger",s.autoTesting),t.Y8G("disabled",!s.selectedChatId),t.R7$(1),t.SpI(" ",s.autoTesting?"Stop":"Start"," Auto Test "),t.R7$(2),t.Y8G("disabled",!s.selectedChatId||s.isTesting),t.R7$(5),t.Y8G("ngModel",s.selectedChatId),t.R7$(3),t.Y8G("ngForOf",s.availableChats),t.R7$(8),t.SpI(" ",s.messagesSent," "),t.R7$(4),t.SpI(" ",s.messagesReceived," "),t.R7$(4),t.HbH(s.connectionStatus?"text-success":"text-danger"),t.R7$(1),t.SpI(" ",s.connectionStatus?"Connected":"Disconnected"," "),t.R7$(4),t.SpI(" ",s.testDuration,"s "),t.R7$(5),t.Y8G("ngForOf",s.testLogs))},dependencies:[g.Sq,r.xH,r.y7,r.me,r.Q0,r.wz,r.BC,r.VZ,r.zX,r.vS,g.vh],styles:[".chat-stress-test[_ngcontent-%COMP%]{margin:1rem 0}.test-stats[_ngcontent-%COMP%]{background:#f8f9fa;padding:1rem;border-radius:.375rem;border:1px solid #dee2e6}.stat-item[_ngcontent-%COMP%]{margin-bottom:.5rem}.stat-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.test-log[_ngcontent-%COMP%]   .log-container[_ngcontent-%COMP%]{max-height:200px;overflow-y:auto;border:1px solid #dee2e6;border-radius:.375rem;padding:.5rem;background:#f8f9fa;font-family:monospace;font-size:.875rem}.test-log[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%]{display:flex;margin-bottom:.25rem}.test-log[_ngcontent-%COMP%]   .log-entry.success[_ngcontent-%COMP%]{color:#28a745}.test-log[_ngcontent-%COMP%]   .log-entry.error[_ngcontent-%COMP%]{color:#dc3545}.test-log[_ngcontent-%COMP%]   .log-entry.info[_ngcontent-%COMP%]{color:#17a2b8}.test-log[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%]   .timestamp[_ngcontent-%COMP%]{margin-right:.5rem;color:#6c757d;min-width:80px}.test-log[_ngcontent-%COMP%]   .log-entry[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{flex:1}"]})}}return o})(),O=(()=>{class o{constructor(){}static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275cmp=t.VBU({type:o,selectors:[["app-debug"]],decls:53,vars:0,consts:[[1,"container","mt-4"],[1,"row"],[1,"col-12"],[1,"fas","fa-bug","me-2"],[1,"text-muted"],[1,"row","mt-4"],[1,"col-lg-8"],[1,"col-lg-4"],[1,"card"],[1,"card-header"],[1,"mb-0"],[1,"card-body"],[1,"card","mt-3"],[1,"list-unstyled"],[1,"small"]],template:function(n,s){1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h2"),t.nrm(4,"i",3),t.EFF(5," Debug & Testing Tools "),t.k0s(),t.j41(6,"p",4),t.EFF(7," Use these tools to test and debug the Angular-Spring Boot integration. "),t.k0s()()(),t.j41(8,"div",1)(9,"div",2),t.nrm(10,"app-websocket-test"),t.k0s()(),t.j41(11,"div",5)(12,"div",2),t.nrm(13,"app-chat-stress-test"),t.k0s()(),t.j41(14,"div",5)(15,"div",6)(16,"div",7)(17,"div",8)(18,"div",9)(19,"h5",10),t.EFF(20,"Connection Status"),t.k0s()(),t.j41(21,"div",11),t.nrm(22,"app-websocket-status"),t.k0s()(),t.j41(23,"div",12)(24,"div",9)(25,"h5",10),t.EFF(26,"Quick Info"),t.k0s()(),t.j41(27,"div",11)(28,"ul",13)(29,"li")(30,"strong"),t.EFF(31,"Backend:"),t.k0s(),t.EFF(32," http://localhost:8080"),t.k0s(),t.j41(33,"li")(34,"strong"),t.EFF(35,"WebSocket:"),t.k0s(),t.EFF(36," http://localhost:8080/api/ws"),t.k0s(),t.j41(37,"li")(38,"strong"),t.EFF(39,"Frontend:"),t.k0s(),t.EFF(40," http://localhost:4200"),t.k0s()(),t.nrm(41,"hr"),t.j41(42,"h6"),t.EFF(43,"Test Steps:"),t.k0s(),t.j41(44,"ol",14)(45,"li"),t.EFF(46,"Check connection status is green"),t.k0s(),t.j41(47,"li"),t.EFF(48,'Click "Test Connection"'),t.k0s(),t.j41(49,"li"),t.EFF(50,"Send a test message"),t.k0s(),t.j41(51,"li"),t.EFF(52,"Verify echo response"),t.k0s()()()()()()()())},dependencies:[f.B,_,I],styles:[".container[_ngcontent-%COMP%]{max-width:1200px}"]})}}return o})();var $=a(3887);const j=[{path:"",component:O}];let P=(()=>{class o{static{this.\u0275fac=function(n){return new(n||o)}}static{this.\u0275mod=t.$C({type:o})}static{this.\u0275inj=t.G2t({imports:[g.MD,r.YN,$.G,i.iI.forChild(j)]})}}return o})()},271:(S,b,a)=>{a.d(b,{B:()=>C});var g=a(8359),i=a(6276),r=a(5538),t=a(177);function f(d,T){if(1&d){const c=i.RV6();i.j41(0,"button",3),i.bIt("click",function(){i.eBV(c);const u=i.XpG();return i.Njj(u.reconnect())}),i.nrm(1,"i",4),i.EFF(2),i.k0s()}if(2&d){const c=i.XpG();i.Y8G("disabled",c.isReconnecting),i.R7$(1),i.AVh("fa-spin",c.isReconnecting),i.R7$(1),i.SpI(" ",c.isReconnecting?"Connecting...":"Reconnect"," ")}}let C=(()=>{class d{constructor(c){this.chatService=c,this.isConnected=!1,this.isReconnecting=!1,this.statusText="Connecting...",this.statusClass="status-connecting",this.iconClass="fas fa-circle-notch fa-spin",this.subscription=new g.yU}ngOnInit(){this.subscription.add(this.chatService.connectionStatus$.subscribe(c=>{this.isConnected=c,this.updateStatus(c)}))}ngOnDestroy(){this.subscription.unsubscribe()}updateStatus(c){c?(this.statusText="Real-time chat connected",this.statusClass="status-connected",this.iconClass="fas fa-check-circle",this.isReconnecting=!1):(this.statusText="Chat disconnected - some features may not work",this.statusClass="status-disconnected",this.iconClass="fas fa-exclamation-circle")}reconnect(){this.isReconnecting=!0,this.statusText="Reconnecting...",this.statusClass="status-connecting",this.iconClass="fas fa-circle-notch fa-spin",this.chatService.disconnect(),setTimeout(()=>{this.chatService.forceConnect()},1e3)}static{this.\u0275fac=function(m){return new(m||d)(i.rXU(r.m))}}static{this.\u0275cmp=i.VBU({type:d,selectors:[["app-websocket-status"]],decls:5,vars:5,consts:[[1,"websocket-status",3,"ngClass"],[1,"status-text"],["class","btn btn-sm btn-outline-primary ms-2",3,"disabled","click",4,"ngIf"],[1,"btn","btn-sm","btn-outline-primary","ms-2",3,"disabled","click"],[1,"fas","fa-sync"]],template:function(m,u){1&m&&(i.j41(0,"div",0),i.nrm(1,"i"),i.j41(2,"span",1),i.EFF(3),i.k0s(),i.DNE(4,f,3,4,"button",2),i.k0s()),2&m&&(i.Y8G("ngClass",u.statusClass),i.R7$(1),i.HbH(u.iconClass),i.R7$(2),i.JRh(u.statusText),i.R7$(1),i.Y8G("ngIf",!u.isConnected))},dependencies:[t.YU,t.bT],styles:[".websocket-status[_ngcontent-%COMP%]{display:flex;align-items:center;padding:8px 12px;border-radius:6px;font-size:.875rem;margin-bottom:1rem;transition:all .3s ease}.status-connected[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724;border:1px solid #c3e6cb}.status-disconnected[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb}.status-connecting[_ngcontent-%COMP%]{background-color:#fff3cd;color:#856404;border:1px solid #ffeaa7}.status-text[_ngcontent-%COMP%]{margin-left:8px;font-weight:500}.fa-spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fa-spin 1s infinite linear}@keyframes _ngcontent-%COMP%_fa-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}"]})}}return d})()}}]);