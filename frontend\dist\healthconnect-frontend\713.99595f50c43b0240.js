"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[713],{628:(G,E,m)=>{m.d(E,{E:()=>ot});var e=m(6276),i=m(3888),P=m(9974),w=m(4360),R=m(3294),h=m(5538),y=m(8010),C=m(177),x=m(4341),b=m(453);function A(r,O){if(1&r&&(e.j41(0,"span",7),e.nrm(1,"i",8),e.k0s()),2&r){const s=e.XpG();e.HbH(s.getStatusClass()),e.R7$(1),e.HbH(s.getStatusIcon())}}let u=(()=>{class r{constructor(){this.isOwn=!1}formatTime(s){return new Date(s).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}getStatusIcon(){switch(this.message.status){case"SENT":return"bi-check";case"DELIVERED":return"bi-check2";case"READ":return"bi-check2-all";default:return"bi-clock"}}getStatusClass(){return"READ"===this.message.status?"text-primary":"text-muted"}static{this.\u0275fac=function(d){return new(d||r)}}static{this.\u0275cmp=e.VBU({type:r,selectors:[["app-message-item"]],inputs:{message:"message",isOwn:"isOwn"},decls:9,vars:7,consts:[[1,"message-item"],[1,"message-content"],[1,"message-bubble"],[1,"message-text","mb-1"],[1,"message-meta"],[1,"message-time"],["class","message-status ms-1",3,"class",4,"ngIf"],[1,"message-status","ms-1"],[1,"bi"]],template:function(d,g){1&d&&(e.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"p",3),e.EFF(4),e.k0s(),e.j41(5,"div",4)(6,"small",5),e.EFF(7),e.k0s(),e.DNE(8,A,2,4,"span",6),e.k0s()()()()),2&d&&(e.AVh("own-message",g.isOwn)("other-message",!g.isOwn),e.R7$(4),e.JRh(g.message.content),e.R7$(3),e.JRh(g.formatTime(g.message.createdAt)),e.R7$(1),e.Y8G("ngIf",g.isOwn))},dependencies:[C.bT],styles:[".message-item[_ngcontent-%COMP%]{margin-bottom:.75rem;display:flex;width:100%;min-width:0}.message-item.own-message[_ngcontent-%COMP%]{justify-content:flex-end}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{background-color:#007bff;color:#fff;border-bottom-right-radius:.25rem}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{color:#fffc}.message-item.other-message[_ngcontent-%COMP%]{justify-content:flex-start}.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{background-color:#fff;color:#333;border:1px solid #e9ecef;border-bottom-left-radius:.25rem}.message-item.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{color:#6c757d}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:70%;min-width:0}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{padding:.75rem 1rem;border-radius:1rem;word-wrap:break-word;word-break:break-word;overflow-wrap:break-word;-webkit-hyphens:auto;hyphens:auto;box-shadow:0 1px 2px #0000001a}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{line-height:1.4;margin:0;white-space:pre-wrap;overflow-wrap:break-word}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;margin-top:.25rem}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%], .message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]   .message-meta[_ngcontent-%COMP%]   .message-status[_ngcontent-%COMP%]{font-size:.75rem}@media (max-width: 768px){.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:85%}.message-item[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-bubble[_ngcontent-%COMP%]{padding:.5rem .75rem}}"]})}}return r})();var l=m(9545),M=m(2434);function f(r,O){1&r&&(e.j41(0,"div",5)(1,"div",6)(2,"span",7),e.EFF(3,"Loading..."),e.k0s()(),e.j41(4,"span",8),e.EFF(5,"Loading appointment details..."),e.k0s()())}function D(r,O){if(1&r&&(e.j41(0,"div",9),e.nrm(1,"i",10),e.EFF(2),e.k0s()),2&r){const s=e.XpG(2);e.R7$(2),e.SpI(" ",s.error," ")}}function k(r,O){if(1&r&&(e.j41(0,"div",27),e.nrm(1,"i",38),e.j41(2,"span",39),e.EFF(3),e.k0s()()),2&r){const s=e.XpG(3);e.R7$(3),e.JRh(s.appointment.reasonForVisit)}}function $(r,O){if(1&r&&(e.j41(0,"div",40),e.nrm(1,"i",41),e.j41(2,"strong"),e.EFF(3,"Upcoming:"),e.k0s(),e.EFF(4),e.k0s()),2&r){const s=e.XpG(3);e.R7$(4),e.SpI(" ",s.getTimeUntilAppointment()," ")}}function j(r,O){1&r&&(e.j41(0,"div",42),e.nrm(1,"i",43),e.j41(2,"strong"),e.EFF(3,"Completed:"),e.k0s(),e.EFF(4," Follow-up discussion "),e.k0s())}function U(r,O){if(1&r){const s=e.RV6();e.j41(0,"button",44),e.bIt("click",function(){e.eBV(s);const g=e.XpG(3);return e.Njj(g.openMeetingLink(g.appointment.meetingLink))}),e.nrm(1,"i",45),e.EFF(2," Join Call "),e.k0s()}}function B(r,O){if(1&r){const s=e.RV6();e.j41(0,"div",11)(1,"div",12)(2,"div",13),e.nrm(3,"i"),e.j41(4,"h6",14),e.EFF(5),e.k0s()(),e.j41(6,"button",15),e.bIt("click",function(){e.eBV(s);const g=e.XpG(2);return e.Njj(g.navigateToAppointment())}),e.nrm(7,"i",16),e.k0s()(),e.j41(8,"div",17)(9,"div",18)(10,"div",19)(11,"div",20),e.nrm(12,"i",21),e.j41(13,"span",22),e.EFF(14),e.nI1(15,"date"),e.k0s()()(),e.j41(16,"div",19)(17,"div",20),e.nrm(18,"i",23),e.j41(19,"span",22),e.EFF(20),e.k0s()()(),e.j41(21,"div",19)(22,"div",20),e.nrm(23,"i",24),e.j41(24,"span",22),e.EFF(25),e.k0s()()(),e.j41(26,"div",19)(27,"div",20),e.nrm(28,"i",25),e.j41(29,"span",26),e.EFF(30),e.k0s()()()(),e.j41(31,"div",27),e.nrm(32,"i",28),e.j41(33,"span",22),e.EFF(34),e.k0s()(),e.DNE(35,k,4,1,"div",29),e.j41(36,"div",30),e.DNE(37,$,5,1,"div",31),e.DNE(38,j,5,0,"div",32),e.k0s()(),e.j41(39,"div",33)(40,"div",34),e.DNE(41,U,3,0,"button",35),e.j41(42,"button",36),e.bIt("click",function(){e.eBV(s);const g=e.XpG(2);return e.Njj(g.navigateToAppointment())}),e.nrm(43,"i",37),e.EFF(44," View Details "),e.k0s()()()()}if(2&r){const s=e.XpG(2);e.R7$(3),e.HbH(s.getContextIcon()+" me-2"),e.R7$(2),e.JRh(s.getContextTitle()),e.R7$(9),e.JRh(e.i5U(15,17,s.appointment.date,"mediumDate")),e.R7$(6),e.Lme("",s.appointment.startTime," - ",s.appointment.endTime,""),e.R7$(5),e.JRh(s.appointment.doctor.fullName),e.R7$(4),e.HbH("badge-"+s.appointment.status.toLowerCase()),e.R7$(1),e.SpI(" ",s.appointment.status," "),e.R7$(2),e.HbH("VIDEO_CALL"===s.appointment.type?"fa-video":"fa-user-friends"),e.R7$(2),e.SpI(" ","VIDEO_CALL"===s.appointment.type?"Video Call":"In-Person"," "),e.R7$(1),e.Y8G("ngIf",s.appointment.reasonForVisit),e.R7$(2),e.Y8G("ngIf",s.isBeforeAppointment()),e.R7$(1),e.Y8G("ngIf",s.isAfterAppointment()),e.R7$(3),e.Y8G("ngIf","VIDEO_CALL"===s.appointment.type&&s.appointment.meetingLink&&s.isBeforeAppointment())}}function Y(r,O){if(1&r&&(e.j41(0,"div",1),e.DNE(1,f,6,0,"div",2),e.DNE(2,D,3,1,"div",3),e.DNE(3,B,45,20,"div",4),e.k0s()),2&r){const s=e.XpG();e.R7$(1),e.Y8G("ngIf",s.loading),e.R7$(1),e.Y8G("ngIf",s.error),e.R7$(1),e.Y8G("ngIf",s.appointment&&!s.loading&&!s.error)}}let z=(()=>{class r{constructor(s,d){this.appointmentService=s,this.router=d,this.appointment=null,this.loading=!1,this.error=null}ngOnInit(){this.appointmentId&&this.loadAppointment()}loadAppointment(){this.appointmentId&&(this.loading=!0,this.error=null,this.appointmentService.getAppointment(this.appointmentId).subscribe({next:s=>{this.appointment=s,this.loading=!1},error:s=>{this.error="Failed to load appointment details",this.loading=!1,console.error("Error loading appointment:",s)}}))}navigateToAppointment(){this.appointment&&this.router.navigate(["/appointments",this.appointment.id])}getContextTitle(){switch(this.chatType){case"PRE_APPOINTMENT":return"Pre-Appointment Discussion";case"POST_APPOINTMENT":return"Post-Appointment Follow-up";case"URGENT":return"Urgent Medical Consultation";case"PRESCRIPTION_INQUIRY":return"Prescription Questions";case"FOLLOW_UP":return"Follow-up Care";default:return"Appointment Discussion"}}getContextIcon(){switch(this.chatType){case"PRE_APPOINTMENT":return"fas fa-clock text-info";case"POST_APPOINTMENT":return"fas fa-check-circle text-success";case"URGENT":return"fas fa-exclamation-triangle text-danger";case"PRESCRIPTION_INQUIRY":return"fas fa-pills text-primary";case"FOLLOW_UP":return"fas fa-stethoscope text-secondary";default:return"fas fa-calendar text-primary"}}isBeforeAppointment(){return!!this.appointment&&new Date(`${this.appointment.date}T${this.appointment.startTime}`)>new Date}isAfterAppointment(){return!!this.appointment&&new Date(`${this.appointment.date}T${this.appointment.endTime}`)<new Date}getTimeUntilAppointment(){if(!this.appointment)return"";const s=new Date(`${this.appointment.date}T${this.appointment.startTime}`),d=new Date,g=s.getTime()-d.getTime();if(g<=0)return"Appointment has passed";const _=Math.floor(g/864e5),T=Math.floor(g%864e5/36e5),F=Math.floor(g%36e5/6e4);return _>0?`${_} day${_>1?"s":""} remaining`:T>0?`${T} hour${T>1?"s":""} remaining`:`${F} minute${F>1?"s":""} remaining`}openMeetingLink(s){window.open(s,"_blank")}static{this.\u0275fac=function(d){return new(d||r)(e.rXU(l.h),e.rXU(M.Ix))}}static{this.\u0275cmp=e.VBU({type:r,selectors:[["app-appointment-context"]],inputs:{appointmentId:"appointmentId",chatType:"chatType"},decls:1,vars:1,consts:[["class","appointment-context-card",4,"ngIf"],[1,"appointment-context-card"],["class","text-center p-3",4,"ngIf"],["class","alert alert-warning mb-0",4,"ngIf"],["class","appointment-context",4,"ngIf"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"ms-2"],[1,"alert","alert-warning","mb-0"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"appointment-context"],[1,"context-header"],[1,"d-flex","align-items-center"],[1,"mb-0","fw-bold"],["type","button","title","View full appointment details",1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"fas","fa-external-link-alt"],[1,"appointment-summary"],[1,"row","g-2"],[1,"col-md-6"],[1,"summary-item"],[1,"fas","fa-calendar-alt","text-muted","me-2"],[1,"fw-medium"],[1,"fas","fa-clock","text-muted","me-2"],[1,"fas","fa-user-md","text-muted","me-2"],[1,"fas","fa-tag","text-muted","me-2"],[1,"badge"],[1,"summary-item","mt-2"],[1,"text-muted","me-2"],["class","summary-item mt-2",4,"ngIf"],[1,"time-context","mt-3"],["class","alert alert-info py-2 mb-0",4,"ngIf"],["class","alert alert-success py-2 mb-0",4,"ngIf"],[1,"quick-actions","mt-3"],["role","group",1,"btn-group","w-100"],["type","button","class","btn btn-sm btn-primary",3,"click",4,"ngIf"],["type","button",1,"btn","btn-sm","btn-outline-secondary",3,"click"],[1,"fas","fa-eye","me-1"],[1,"fas","fa-notes-medical","text-muted","me-2"],[1,"text-muted"],[1,"alert","alert-info","py-2","mb-0"],[1,"fas","fa-info-circle","me-2"],[1,"alert","alert-success","py-2","mb-0"],[1,"fas","fa-check-circle","me-2"],["type","button",1,"btn","btn-sm","btn-primary",3,"click"],[1,"fas","fa-video","me-1"]],template:function(d,g){1&d&&e.DNE(0,Y,4,3,"div",0),2&d&&e.Y8G("ngIf",g.appointmentId)},dependencies:[C.bT,C.vh],styles:[".appointment-context-card[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8f9fa 0%,#e9ecef 100%);border:1px solid #dee2e6;border-radius:12px;margin-bottom:1rem;overflow:hidden;box-shadow:0 2px 4px #0000000d}.context-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;background:white;border-bottom:1px solid #dee2e6}.context-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057}.appointment-summary[_ngcontent-%COMP%]{padding:1rem}.summary-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.5rem}.summary-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.summary-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;text-align:center}.badge[_ngcontent-%COMP%]{font-size:.75rem}.badge.badge-pending[_ngcontent-%COMP%]{background-color:#ffc107;color:#000}.badge.badge-scheduled[_ngcontent-%COMP%]{background-color:#17a2b8;color:#fff}.badge.badge-confirmed[_ngcontent-%COMP%]{background-color:#28a745;color:#fff}.badge.badge-completed[_ngcontent-%COMP%]{background-color:#6c757d;color:#fff}.badge.badge-cancelled[_ngcontent-%COMP%]{background-color:#dc3545;color:#fff}.time-context[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{border-radius:8px;font-size:.875rem}.quick-actions[_ngcontent-%COMP%]{padding:0 1rem 1rem}.quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:6px;font-size:.875rem}@media (max-width: 768px){.context-header[_ngcontent-%COMP%], .appointment-summary[_ngcontent-%COMP%]{padding:.75rem}.quick-actions[_ngcontent-%COMP%]{padding:0 .75rem .75rem}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]{flex-direction:column}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:6px!important;margin-bottom:.25rem}.quick-actions[_ngcontent-%COMP%]   .btn-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}}.appointment-context-card[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideIn .3s ease-out}@keyframes _ngcontent-%COMP%_slideIn{0%{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}"]})}}return r})();const X=["messagesContainer"],V=["messageInput"];function W(r,O){if(1&r&&e.nrm(0,"app-doctor-availability",27),2&r){const s=e.XpG(2);let d;e.Y8G("doctorId",null==(d=s.getOtherParticipant())?null:d.id)("showDetails",!1)}}function H(r,O){if(1&r&&e.nrm(0,"app-appointment-context",28),2&r){const s=e.XpG(2);e.Y8G("appointmentId",s.chat.relatedAppointment.id)("chatType",s.chat.type)}}function J(r,O){1&r&&(e.j41(0,"div",29)(1,"div",30)(2,"span",31),e.EFF(3,"Loading messages..."),e.k0s()()())}function K(r,O){1&r&&(e.j41(0,"div",32),e.nrm(1,"i",33),e.j41(2,"p"),e.EFF(3,"No messages yet"),e.k0s(),e.j41(4,"small"),e.EFF(5,"Start the conversation!"),e.k0s()())}function Q(r,O){if(1&r&&(e.j41(0,"div",39)(1,"span",40),e.EFF(2),e.k0s()()),2&r){const s=e.XpG().$implicit,d=e.XpG(3);e.R7$(2),e.JRh(d.formatMessageDate(s.createdAt))}}function Z(r,O){if(1&r&&(e.qex(0),e.DNE(1,Q,3,1,"div",37),e.nrm(2,"app-message-item",38),e.bVm()),2&r){const s=O.$implicit,d=O.index,g=e.XpG(3);e.R7$(1),e.Y8G("ngIf",g.shouldShowDateSeparator(d)),e.R7$(1),e.Y8G("message",s)("isOwn",s.sender.id===(null==g.currentUser?null:g.currentUser.id))}}function q(r,O){if(1&r&&(e.j41(0,"div",41)(1,"div",42),e.nrm(2,"span")(3,"span")(4,"span"),e.k0s(),e.j41(5,"small",43),e.EFF(6),e.k0s()()),2&r){const s=e.XpG(3);let d;e.R7$(6),e.SpI("",null==(d=s.getOtherParticipant())?null:d.fullName," is typing...")}}function tt(r,O){if(1&r&&(e.j41(0,"div",34),e.DNE(1,Z,3,3,"ng-container",35),e.DNE(2,q,7,1,"div",36),e.k0s()),2&r){const s=e.XpG(2);e.R7$(1),e.Y8G("ngForOf",s.messages),e.R7$(1),e.Y8G("ngIf",s.isTyping())}}function et(r,O){1&r&&e.nrm(0,"i",44)}function nt(r,O){1&r&&(e.j41(0,"div",30)(1,"span",31),e.EFF(2,"Sending..."),e.k0s()())}function it(r,O){1&r&&(e.j41(0,"div",45)(1,"small",46),e.nrm(2,"i",47),e.EFF(3," Connection lost. Trying to reconnect... "),e.k0s()())}function st(r,O){if(1&r){const s=e.RV6();e.j41(0,"div",2)(1,"div",3)(2,"div",4),e.nrm(3,"img",5),e.j41(4,"div",6)(5,"h6",7),e.EFF(6),e.k0s(),e.j41(7,"small",8),e.EFF(8),e.k0s(),e.DNE(9,W,1,2,"app-doctor-availability",9),e.k0s()(),e.j41(10,"div",10)(11,"span",11),e.nrm(12,"i",12),e.k0s()()(),e.DNE(13,H,1,2,"app-appointment-context",13),e.j41(14,"div",14,15),e.DNE(16,J,4,0,"div",16),e.DNE(17,K,6,0,"div",17),e.DNE(18,tt,3,2,"div",18),e.k0s(),e.j41(19,"div",19)(20,"div",20)(21,"input",21,22),e.bIt("ngModelChange",function(g){e.eBV(s);const _=e.XpG();return e.Njj(_.messageContent=g)})("keypress",function(g){e.eBV(s);const _=e.XpG();return e.Njj(_.onKeyPress(g))})("input",function(){e.eBV(s);const g=e.XpG();return e.Njj(g.onInputChange())})("blur",function(){e.eBV(s);const g=e.XpG();return e.Njj(g.onInputBlur())}),e.k0s(),e.j41(23,"button",23),e.bIt("click",function(){e.eBV(s);const g=e.XpG();return e.Njj(g.sendMessage())}),e.DNE(24,et,1,0,"i",24),e.DNE(25,nt,3,0,"div",25),e.k0s()(),e.DNE(26,it,4,0,"div",26),e.k0s()()}if(2&r){const s=e.XpG();let d,g,_,T,F;e.R7$(3),e.Y8G("src",(null==(d=s.getOtherParticipant())?null:d.avatar)||"/assets/images/default-avatar.png",e.B4B)("alt",null==(g=s.getOtherParticipant())?null:g.fullName),e.R7$(3),e.JRh(null==(_=s.getOtherParticipant())?null:_.fullName),e.R7$(2),e.SpI(" ","DOCTOR"===(null==(T=s.getOtherParticipant())?null:T.role)?"Dr. "+(null==(T=s.getOtherParticipant())?null:T.specialization):"Patient"," "),e.R7$(1),e.Y8G("ngIf","DOCTOR"===(null==(F=s.getOtherParticipant())?null:F.role)),e.R7$(2),e.AVh("connected",s.connectionStatus)("disconnected",!s.connectionStatus),e.R7$(1),e.AVh("bi-wifi",s.connectionStatus)("bi-wifi-off",!s.connectionStatus),e.R7$(1),e.Y8G("ngIf",null==s.chat?null:s.chat.relatedAppointment),e.R7$(3),e.Y8G("ngIf",s.loading),e.R7$(1),e.Y8G("ngIf",!s.loading&&0===s.messages.length),e.R7$(1),e.Y8G("ngIf",!s.loading&&s.messages.length>0),e.R7$(3),e.Y8G("ngModel",s.messageContent)("disabled",s.sending||!s.connectionStatus),e.R7$(2),e.Y8G("disabled",!s.messageContent.trim()||!s.chat||!s.connectionStatus||s.sending),e.R7$(1),e.Y8G("ngIf",!s.sending),e.R7$(1),e.Y8G("ngIf",s.sending),e.R7$(1),e.Y8G("ngIf",!s.connectionStatus)}}function at(r,O){1&r&&(e.j41(0,"div",48)(1,"div",49),e.nrm(2,"i",33),e.j41(3,"h5"),e.EFF(4,"Select a conversation"),e.k0s(),e.j41(5,"p"),e.EFF(6,"Choose a conversation from the list to start messaging"),e.k0s()()())}let ot=(()=>{class r{constructor(s,d,g,_){this.chatService=s,this.authService=d,this.cdr=g,this.ngZone=_,this.chat=null,this.messagesSent=new e.bkB,this.messages=[],this.messageContent="",this.loading=!1,this.sending=!1,this.typingUsers=new Set,this.connectionStatus=!1,this.subscriptions=[],this.isScrolledToBottom=!0,this.lastMessageCount=0,this.maxMessages=1e3}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.subscribeToServices()}ngAfterViewInit(){this.setupScrollListener()}ngOnChanges(s){s.chat&&s.chat.currentValue&&this.loadChat(s.chat.currentValue)}ngOnDestroy(){this.subscriptions.forEach(s=>{s&&!s.closed&&s.unsubscribe()}),this.subscriptions=[],this.clearTimeouts()}clearTimeouts(){this.typingTimeout&&(clearTimeout(this.typingTimeout),this.typingTimeout=null),this.scrollTimeout&&(clearTimeout(this.scrollTimeout),this.scrollTimeout=null)}subscribeToServices(){const s=this.chatService.connectionStatus$.subscribe({next:_=>{this.connectionStatus=_,this.cdr.detectChanges()},error:_=>console.error("Connection status error:",_)});this.subscriptions.push(s);const d=this.chatService.messages$.subscribe({next:_=>{this.chat&&_.chatId===this.chat.id&&this.addNewMessage(_)},error:_=>console.error("Messages subscription error:",_)});this.subscriptions.push(d);const g=this.chatService.typing$.pipe(function t(r,O=i.E){return(0,P.N)((s,d)=>{let g=null,_=null,T=null;const F=()=>{if(g){g.unsubscribe(),g=null;const N=_;_=null,d.next(N)}};function ct(){const N=T+r,L=O.now();if(L<N)return g=this.schedule(void 0,N-L),void d.add(g);F()}s.subscribe((0,w._)(d,N=>{_=N,T=O.now(),g||(g=O.schedule(ct,r),d.add(g))},()=>{F(),d.complete()},void 0,()=>{_=g=null}))})}(100),(0,R.F)()).subscribe({next:_=>this.handleTypingNotification(_),error:_=>console.error("Typing notification error:",_)});this.subscriptions.push(g)}addNewMessage(s){this.messages.find(g=>g.id===s.id)||(this.messages.push(s),this.messages.length>this.maxMessages&&(this.messages=this.messages.slice(-this.maxMessages)),s.sender.id!==this.currentUser?.id&&this.chatService.markMessagesAsRead(this.chat.id).subscribe({error:g=>console.error("Mark as read error:",g)}),this.scheduleScrollToBottom(),this.cdr.detectChanges())}loadChat(s){this.clearTimeouts(),this.messages=[],this.typingUsers.clear(),this.messageContent="",this.isScrolledToBottom=!0,this.chat=s,s&&(this.loadMessages(),this.chatService.subscribeToChatMessages(s.id)),this.cdr.detectChanges()}loadMessages(){this.chat&&(this.loading=!0,this.chatService.getChatMessages(this.chat.id).subscribe({next:s=>{this.messages=s.reverse(),this.loading=!1,this.lastMessageCount=this.messages.length,this.scheduleScrollToBottom(),this.cdr.detectChanges()},error:s=>{console.error("Failed to load messages:",s),this.loading=!1,this.cdr.detectChanges()}}))}onTyping(){this.chat&&(this.chatService.sendTypingNotification(this.chat.id,!0),this.typingTimeout&&clearTimeout(this.typingTimeout),this.typingTimeout=setTimeout(()=>{this.stopTyping()},3e3))}stopTyping(){this.chat&&(this.chatService.sendTypingNotification(this.chat.id,!1),this.typingTimeout&&(clearTimeout(this.typingTimeout),this.typingTimeout=null))}handleTypingNotification(s){s.userId!==this.currentUser?.id&&("typing"===s.status?this.typingUsers.add(s.userId):this.typingUsers.delete(s.userId))}setupScrollListener(){if(!this.messagesContainer)return;const s=this.messagesContainer.nativeElement;s.addEventListener("scroll",()=>{this.ngZone.runOutsideAngular(()=>{const{scrollTop:d,scrollHeight:g,clientHeight:_}=s;this.isScrolledToBottom=d+_>=g-50})})}scheduleScrollToBottom(){this.isScrolledToBottom&&(this.scrollTimeout&&cancelAnimationFrame(this.scrollTimeout),this.scrollTimeout=requestAnimationFrame(()=>{this.scrollToBottom()}))}scrollToBottom(){try{if(this.messagesContainer?.nativeElement){const s=this.messagesContainer.nativeElement;s.scrollTo({top:s.scrollHeight,behavior:"smooth"}),this.isScrolledToBottom=!0}}catch(s){console.error("Error scrolling to bottom:",s)}}getOtherParticipant(){return this.chat?"PATIENT"===this.currentUser?.role?this.chat.doctor:this.chat.patient:null}isTyping(){return this.typingUsers.size>0}formatMessageTime(s){return new Date(s).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}formatMessageDate(s){const d=new Date(s),g=new Date,_=new Date(g);return _.setDate(_.getDate()-1),d.toDateString()===g.toDateString()?"Today":d.toDateString()===_.toDateString()?"Yesterday":d.toLocaleDateString()}shouldShowDateSeparator(s){if(0===s)return!0;const g=this.messages[s-1];return new Date(this.messages[s].createdAt).toDateString()!==new Date(g.createdAt).toDateString()}sendMessage(){const s=this.messageContent.trim();if(s&&this.chat&&this.connectionStatus&&!this.sending){this.sending=!0;try{this.chatService.sendMessage(this.chat.id,s),this.messageContent="",this.stopTyping(),this.messagesSent.emit(),this.focusInput()}catch(d){console.error("Failed to send message:",d)}finally{this.sending=!1,this.cdr.detectChanges()}}}focusInput(){setTimeout(()=>{this.messageInput?.nativeElement&&this.messageInput.nativeElement.focus()},100)}onKeyPress(s){"Enter"===s.key&&!s.shiftKey&&(s.preventDefault(),this.sendMessage())}onInputChange(){this.onTyping()}onInputBlur(){setTimeout(()=>{this.messageInput?.nativeElement&&document.activeElement!==this.messageInput.nativeElement&&this.stopTyping()},100)}static{this.\u0275fac=function(d){return new(d||r)(e.rXU(h.m),e.rXU(y.u),e.rXU(e.gRc),e.rXU(e.SKi))}}static{this.\u0275cmp=e.VBU({type:r,selectors:[["app-chat-window"]],viewQuery:function(d,g){if(1&d&&(e.GBs(X,5),e.GBs(V,5)),2&d){let _;e.mGM(_=e.lsd())&&(g.messagesContainer=_.first),e.mGM(_=e.lsd())&&(g.messageInput=_.first)}},inputs:{chat:"chat"},outputs:{messagesSent:"messagesSent"},features:[e.OA$],decls:2,vars:2,consts:[["class","chat-window",4,"ngIf"],["class","chat-placeholder",4,"ngIf"],[1,"chat-window"],[1,"chat-header"],[1,"participant-info"],[1,"participant-avatar",3,"src","alt"],[1,"participant-details"],[1,"mb-0"],[1,"text-muted"],["size","sm",3,"doctorId","showDetails",4,"ngIf"],[1,"connection-status"],[1,"status-indicator"],[1,"bi"],[3,"appointmentId","chatType",4,"ngIf"],[1,"messages-container"],["messagesContainer",""],["class","text-center p-3",4,"ngIf"],["class","text-center p-4 text-muted",4,"ngIf"],["class","messages-list",4,"ngIf"],[1,"message-input-container"],[1,"input-group"],["type","text","placeholder","Type a message...","autocomplete","off","maxlength","1000",1,"form-control","message-input",3,"ngModel","disabled","ngModelChange","keypress","input","blur"],["messageInput",""],["type","button",1,"btn","btn-primary","send-button",3,"disabled","click"],["class","bi bi-send",4,"ngIf"],["class","spinner-border spinner-border-sm","role","status",4,"ngIf"],["class","connection-warning",4,"ngIf"],["size","sm",3,"doctorId","showDetails"],[3,"appointmentId","chatType"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"text-center","p-4","text-muted"],[1,"bi","bi-chat-square-text","fs-1","mb-3","d-block"],[1,"messages-list"],[4,"ngFor","ngForOf"],["class","typing-indicator",4,"ngIf"],["class","date-separator",4,"ngIf"],[3,"message","isOwn"],[1,"date-separator"],[1,"date-label"],[1,"typing-indicator"],[1,"typing-dots"],[1,"text-muted","ms-2"],[1,"bi","bi-send"],[1,"connection-warning"],[1,"text-warning"],[1,"bi","bi-exclamation-triangle","me-1"],[1,"chat-placeholder"],[1,"text-center","text-muted"]],template:function(d,g){1&d&&(e.DNE(0,st,27,23,"div",0),e.DNE(1,at,7,0,"div",1)),2&d&&(e.Y8G("ngIf",g.chat),e.R7$(1),e.Y8G("ngIf",!g.chat))},dependencies:[C.Sq,C.bT,x.me,x.BC,x.tU,x.vS,b.U,u,z],styles:[".chat-window[_ngcontent-%COMP%]{height:100%;display:flex;flex-direction:column;min-height:0;position:relative;overflow:hidden}.chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;border-bottom:1px solid #e9ecef;background-color:#f8f9fa;flex-shrink:0;min-height:70px}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]{display:flex;align-items:center;min-width:0}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;object-fit:cover;margin-right:.75rem;border:2px solid #e9ecef;flex-shrink:0}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-details[_ngcontent-%COMP%]{min-width:0}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-details[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-weight:600;color:#333;margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]{flex-shrink:0}.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.connected[_ngcontent-%COMP%]{color:#28a745}.chat-header[_ngcontent-%COMP%]   .connection-status[_ngcontent-%COMP%]   .status-indicator.disconnected[_ngcontent-%COMP%]{color:#dc3545}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;overflow-x:hidden;padding:1rem;background-color:#f8f9fa;min-height:0;position:relative;scroll-behavior:smooth}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#a8a8a8}.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]{text-align:center;margin:1rem 0}.messages-list[_ngcontent-%COMP%]   .date-separator[_ngcontent-%COMP%]   .date-label[_ngcontent-%COMP%]{background-color:#e9ecef;padding:.25rem .75rem;border-radius:1rem;font-size:.75rem;color:#6c757d}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;margin:.5rem 0}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]{display:flex;align-items:center;background-color:#e9ecef;padding:.5rem .75rem;border-radius:1rem}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:6px;height:6px;background-color:#6c757d;border-radius:50%;margin:0 2px;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.messages-list[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-dots[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.message-input-container[_ngcontent-%COMP%]{padding:1rem;border-top:1px solid #e9ecef;background-color:#fff;flex-shrink:0;min-height:80px}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{border-right:none;border-radius:25px 0 0 25px;padding:.75rem 1rem;font-size:.95rem}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #007bff40;border-color:#80bdff;outline:none}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]:disabled{background-color:#f8f9fa;opacity:.7}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]::placeholder{color:#6c757d;opacity:.8}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]{border-left:none;border-radius:0 25px 25px 0;padding:.75rem 1.25rem;min-width:60px}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]:not(:disabled):hover{background-color:#0056b3;border-color:#0056b3}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]   .spinner-border-sm[_ngcontent-%COMP%]{width:1rem;height:1rem}.message-input-container[_ngcontent-%COMP%]   .connection-warning[_ngcontent-%COMP%]{margin-top:.5rem;text-align:center;padding:.25rem;border-radius:4px;background-color:#fff3cd}.message-input-container[_ngcontent-%COMP%]   .character-count[_ngcontent-%COMP%]{margin-top:.25rem;text-align:right}.message-input-container[_ngcontent-%COMP%]   .character-count[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.75rem}.chat-placeholder[_ngcontent-%COMP%]{height:100%;display:flex;align-items:center;justify-content:center;background-color:#f8f9fa}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]{max-width:300px}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#6c757d}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#495057}.chat-placeholder[_ngcontent-%COMP%]   .text-center[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{transform:translateY(0)}30%{transform:translateY(-10px)}}@media (max-width: 768px){.chat-window[_ngcontent-%COMP%]{height:calc(100vh - 60px)}.chat-header[_ngcontent-%COMP%]{padding:.75rem;min-height:60px}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%]{width:35px;height:35px}.messages-container[_ngcontent-%COMP%]{padding:.75rem .5rem}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.message-input-container[_ngcontent-%COMP%]{padding:.75rem;min-height:70px}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{font-size:16px;padding:.6rem .8rem}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .send-button[_ngcontent-%COMP%]{padding:.6rem 1rem;min-width:50px}}@media (max-width: 480px){.chat-header[_ngcontent-%COMP%]{padding:.5rem}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-avatar[_ngcontent-%COMP%]{width:30px;height:30px;margin-right:.5rem}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .participant-details[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:.9rem}.messages-container[_ngcontent-%COMP%]{padding:.5rem .25rem}.message-input-container[_ngcontent-%COMP%]{padding:.5rem}.message-input-container[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .message-input[_ngcontent-%COMP%]{padding:.5rem .75rem}}"]})}}return r})()},1713:(G,E,m)=>{m.r(E),m.d(E,{ChatModule:()=>vt});var e=m(177),i=m(4341),P=m(2434),w=m(4978),t=m(6276),R=m(5538),h=m(3443),y=m(8010),C=m(271),x=m(628);function b(o,p){1&o&&(t.j41(0,"div",15)(1,"div",16)(2,"span",17),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"div",18),t.EFF(5,"Loading chats..."),t.k0s()())}function A(o,p){1&o&&(t.j41(0,"div",19),t.nrm(1,"i",20),t.j41(2,"p"),t.EFF(3,"No conversations yet"),t.k0s(),t.j41(4,"small"),t.EFF(5,"Click the + button to start a conversation"),t.k0s()())}function u(o,p){1&o&&t.nrm(0,"span",35)}function l(o,p){if(1&o&&(t.j41(0,"span",38),t.nrm(1,"i",39),t.k0s()),2&o){const n=t.XpG(2).$implicit;t.R7$(1),t.AVh("text-primary","READ"===n.lastMessage.status)("text-muted","READ"!==n.lastMessage.status)}}function M(o,p){if(1&o&&(t.j41(0,"p",36),t.DNE(1,l,2,4,"span",37),t.EFF(2),t.nI1(3,"slice"),t.k0s()),2&o){const n=t.XpG().$implicit,a=t.XpG(2);t.R7$(1),t.Y8G("ngIf",n.lastMessage.sender.id===(null==a.currentUser?null:a.currentUser.id)),t.R7$(1),t.Lme(" ",t.brH(3,3,n.lastMessage.content,0,50),"",n.lastMessage.content.length>50?"...":""," ")}}function f(o,p){1&o&&(t.j41(0,"p",36)(1,"em"),t.EFF(2,"No messages yet"),t.k0s()())}function D(o,p){if(1&o&&(t.j41(0,"span",40),t.EFF(1),t.k0s()),2&o){const n=t.XpG().$implicit;t.R7$(1),t.SpI(" ",n.unreadCount," ")}}function k(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",23),t.bIt("click",function(){const v=t.eBV(n).$implicit,I=t.XpG(2);return t.Njj(I.selectChat(v))}),t.j41(1,"div",24),t.nrm(2,"img",25),t.DNE(3,u,1,0,"span",26),t.k0s(),t.j41(4,"div",27)(5,"div",28)(6,"h6",29),t.EFF(7),t.k0s(),t.j41(8,"small",30),t.EFF(9),t.k0s()(),t.j41(10,"div",31),t.DNE(11,M,4,7,"p",32),t.DNE(12,f,3,0,"p",32),t.k0s()(),t.j41(13,"div",33),t.DNE(14,D,2,1,"span",34),t.k0s()()}if(2&o){const n=p.$implicit,a=t.XpG(2);t.AVh("active",(null==a.selectedChat?null:a.selectedChat.id)===n.id),t.R7$(2),t.Y8G("src",a.getOtherParticipant(n).avatar||"/assets/images/default-avatar.png",t.B4B)("alt",a.getOtherParticipant(n).fullName),t.R7$(1),t.Y8G("ngIf",!1),t.R7$(4),t.SpI(" ",a.getOtherParticipant(n).fullName," "),t.R7$(2),t.SpI(" ",a.formatLastMessageTime(n.lastMessage?n.lastMessage.createdAt:n.createdAt)," "),t.R7$(2),t.Y8G("ngIf",n.lastMessage),t.R7$(1),t.Y8G("ngIf",!n.lastMessage),t.R7$(2),t.Y8G("ngIf",n.unreadCount>0)}}function $(o,p){if(1&o&&(t.j41(0,"div",21),t.DNE(1,k,15,10,"div",22),t.k0s()),2&o){const n=t.XpG();t.R7$(1),t.Y8G("ngForOf",n.chats)}}function j(o,p){1&o&&(t.j41(0,"div",15)(1,"div",16)(2,"span",17),t.EFF(3,"Loading doctors..."),t.k0s()(),t.j41(4,"div",18),t.EFF(5,"Loading available doctors..."),t.k0s()())}function U(o,p){1&o&&(t.j41(0,"div",52),t.nrm(1,"i",53),t.j41(2,"p"),t.EFF(3,"No doctors available"),t.k0s()())}function B(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",58),t.bIt("click",function(){const v=t.eBV(n).$implicit,I=t.XpG(3);return t.Njj(I.startChatWithDoctor(v))}),t.nrm(1,"img",59),t.j41(2,"div",60)(3,"h6",3),t.EFF(4),t.k0s(),t.j41(5,"small",61),t.EFF(6),t.k0s(),t.j41(7,"div",62)(8,"small",61),t.EFF(9),t.k0s()()()()}if(2&o){const n=p.$implicit;t.R7$(1),t.Y8G("src",n.avatar||"assets/images/default-avatar.svg",t.B4B)("alt",n.fullName),t.R7$(3),t.JRh(n.fullName),t.R7$(2),t.JRh(n.specialization),t.R7$(3),t.SpI("",n.yearsOfExperience," years experience")}}function Y(o,p){if(1&o){const n=t.RV6();t.j41(0,"div")(1,"div",54)(2,"input",55),t.bIt("ngModelChange",function(c){t.eBV(n);const v=t.XpG(2);return t.Njj(v.doctorSearchTerm=c)})("input",function(){t.eBV(n);const c=t.XpG(2);return t.Njj(c.filterDoctors())}),t.k0s()(),t.j41(3,"div",56),t.DNE(4,B,10,5,"div",57),t.k0s()()}if(2&o){const n=t.XpG(2);t.R7$(2),t.Y8G("ngModel",n.doctorSearchTerm),t.R7$(2),t.Y8G("ngForOf",n.filteredDoctors)}}function z(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",41)(1,"div",42)(2,"div",43)(3,"div",44)(4,"h5",45),t.EFF(5,"Start New Conversation"),t.k0s(),t.j41(6,"button",46),t.bIt("click",function(){t.eBV(n);const c=t.XpG();return t.Njj(c.showNewChatModal=!1)}),t.k0s()(),t.j41(7,"div",47),t.DNE(8,j,6,0,"div",8),t.DNE(9,U,4,0,"div",48),t.DNE(10,Y,5,2,"div",49),t.k0s(),t.j41(11,"div",50)(12,"button",51),t.bIt("click",function(){t.eBV(n);const c=t.XpG();return t.Njj(c.showNewChatModal=!1)}),t.EFF(13,"Cancel"),t.k0s()()()()()}if(2&o){const n=t.XpG();t.xc7("display",n.showNewChatModal?"block":"none"),t.AVh("show",n.showNewChatModal),t.R7$(8),t.Y8G("ngIf",n.loadingDoctors),t.R7$(1),t.Y8G("ngIf",!n.loadingDoctors&&0===n.availableDoctors.length),t.R7$(1),t.Y8G("ngIf",!n.loadingDoctors&&n.availableDoctors.length>0)}}function X(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",63),t.bIt("click",function(){t.eBV(n);const c=t.XpG();return t.Njj(c.showNewChatModal=!1)}),t.k0s()}if(2&o){const n=t.XpG();t.AVh("show",n.showNewChatModal)}}let V=(()=>{class o{constructor(n,a,c){this.chatService=n,this.userService=a,this.authService=c,this.chats=[],this.selectedChat=null,this.loading=!0,this.showNewChatModal=!1,this.availableDoctors=[],this.filteredDoctors=[],this.loadingDoctors=!1,this.doctorSearchTerm="",this.subscriptions=[]}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadChats(),this.subscribeToChats()}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe())}loadChats(){this.chatService.loadUserChats()}subscribeToChats(){const n=this.chatService.chats$.subscribe({next:c=>{this.chats=c,this.loading=!1},error:c=>{console.error("Failed to load chats:",c),this.loading=!1}});this.subscriptions.push(n);const a=this.chatService.messages$.subscribe({next:c=>{this.updateChatWithNewMessage(c)}});this.subscriptions.push(a)}updateChatWithNewMessage(n){const a=this.chats.findIndex(c=>c.id===n.chatId);if(-1!==a){this.chats[a].lastMessage=n,this.chats[a].updatedAt=n.createdAt,n.sender.id!==this.currentUser?.id&&this.chats[a].unreadCount++;const c=this.chats.splice(a,1)[0];this.chats.unshift(c)}}selectChat(n){this.selectedChat=n,n.unreadCount>0&&this.chatService.markMessagesAsRead(n.id).subscribe({next:()=>{n.unreadCount=0},error:a=>{console.error("Failed to mark messages as read:",a)}})}getOtherParticipant(n){return"PATIENT"===this.currentUser?.role?n.doctor:n.patient}formatLastMessageTime(n){const a=new Date(n),v=((new Date).getTime()-a.getTime())/36e5;return v<1?"Just now":v<24?a.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):a.toLocaleDateString()}onMessageSent(){this.loadChats()}openNewChatModal(){this.showNewChatModal=!0,this.loadAvailableDoctors()}loadAvailableDoctors(){"PATIENT"===this.currentUser?.role&&(this.loadingDoctors=!0,this.userService.getAllDoctors().subscribe({next:n=>{this.availableDoctors=n,this.filteredDoctors=n,this.loadingDoctors=!1},error:n=>{console.error("Failed to load doctors:",n),this.loadingDoctors=!1,this.availableDoctors=[],this.filteredDoctors=[]}}))}filterDoctors(){if(!this.doctorSearchTerm.trim())return void(this.filteredDoctors=this.availableDoctors);const n=this.doctorSearchTerm.toLowerCase();this.filteredDoctors=this.availableDoctors.filter(a=>a.fullName.toLowerCase().includes(n)||a.specialization?.toLowerCase().includes(n))}startChatWithDoctor(n){return this.loadingDoctors=!0,console.log("Starting chat with doctor:",{currentUser:this.currentUser,doctor:n,doctorId:n.id,isAuthenticated:this.authService.isAuthenticated(),token:this.authService.getToken()}),this.authService.isAuthenticated()?n.id?void this.chatService.createOrGetChat(n.id).subscribe({next:a=>{this.showNewChatModal=!1,this.loadingDoctors=!1,this.doctorSearchTerm="",-1===this.chats.findIndex(v=>v.id===a.id)&&this.chats.unshift(a),this.selectChat(a)},error:a=>{console.error("Failed to create chat:",a),this.loadingDoctors=!1,403===a.status?(alert("Access denied. Please check if you are logged in with the correct account."),this.authService.logout()):alert("Failed to create chat: "+(a.error?.message||a.message||"Unknown error"))}}):(console.error("Doctor ID is missing:",n),alert("Error: Doctor ID is missing. Please try again."),void(this.loadingDoctors=!1)):(console.error("User is not authenticated"),alert("You are not logged in. Please log in again."),this.loadingDoctors=!1,void this.authService.logout())}static{this.\u0275fac=function(a){return new(a||o)(t.rXU(R.m),t.rXU(h.D),t.rXU(y.u))}}static{this.\u0275cmp=t.VBU({type:o,selectors:[["app-chat-layout"]],decls:17,vars:6,consts:[[1,"chat-layout"],[1,"chat-sidebar"],[1,"chat-sidebar-header"],[1,"mb-0"],[1,"bi","bi-chat-dots","me-2"],["title","Start new conversation",1,"btn","btn-primary","btn-sm",3,"click"],[1,"bi","bi-plus-lg"],[1,"chat-sidebar-body"],["class","text-center p-3",4,"ngIf"],["class","text-center p-4 text-muted",4,"ngIf"],["class","chat-items",4,"ngIf"],[1,"chat-main"],[3,"chat","messagesSent"],["class","modal fade","tabindex","-1",3,"show","display",4,"ngIf"],["class","modal-backdrop fade",3,"show","click",4,"ngIf"],[1,"text-center","p-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"mt-2"],[1,"text-center","p-4","text-muted"],[1,"bi","bi-chat-square-text","fs-1","mb-3","d-block"],[1,"chat-items"],["class","chat-item",3,"active","click",4,"ngFor","ngForOf"],[1,"chat-item",3,"click"],[1,"chat-avatar"],[1,"avatar-img",3,"src","alt"],["class","online-indicator",4,"ngIf"],[1,"chat-content"],[1,"chat-header"],[1,"chat-name","mb-0"],[1,"chat-time","text-muted"],[1,"chat-preview"],["class","mb-0 text-muted",4,"ngIf"],[1,"chat-meta"],["class","badge bg-primary rounded-pill",4,"ngIf"],[1,"online-indicator"],[1,"mb-0","text-muted"],["class","me-1",4,"ngIf"],[1,"me-1"],[1,"bi","bi-check2-all"],[1,"badge","bg-primary","rounded-pill"],["tabindex","-1",1,"modal","fade"],[1,"modal-dialog"],[1,"modal-content"],[1,"modal-header"],[1,"modal-title"],["type","button",1,"btn-close",3,"click"],[1,"modal-body"],["class","text-center p-3 text-muted",4,"ngIf"],[4,"ngIf"],[1,"modal-footer"],["type","button",1,"btn","btn-secondary",3,"click"],[1,"text-center","p-3","text-muted"],[1,"bi","bi-person-x","fs-1","mb-3","d-block"],[1,"mb-3"],["type","text","placeholder","Search doctors...",1,"form-control",3,"ngModel","ngModelChange","input"],[1,"doctor-list"],["class","doctor-item",3,"click",4,"ngFor","ngForOf"],[1,"doctor-item",3,"click"],[1,"doctor-avatar",3,"src","alt"],[1,"doctor-info"],[1,"text-muted"],[1,"doctor-details"],[1,"modal-backdrop","fade",3,"click"]],template:function(a,c){1&a&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h5",3),t.nrm(4,"i",4),t.EFF(5," Messages "),t.k0s(),t.j41(6,"button",5),t.bIt("click",function(){return c.openNewChatModal()}),t.nrm(7,"i",6),t.k0s()(),t.nrm(8,"app-websocket-status"),t.j41(9,"div",7),t.DNE(10,b,6,0,"div",8),t.DNE(11,A,6,0,"div",9),t.DNE(12,$,2,1,"div",10),t.k0s()(),t.j41(13,"div",11)(14,"app-chat-window",12),t.bIt("messagesSent",function(){return c.onMessageSent()}),t.k0s()()(),t.DNE(15,z,14,7,"div",13),t.DNE(16,X,1,2,"div",14)),2&a&&(t.R7$(10),t.Y8G("ngIf",c.loading),t.R7$(1),t.Y8G("ngIf",!c.loading&&0===c.chats.length),t.R7$(1),t.Y8G("ngIf",!c.loading&&c.chats.length>0),t.R7$(2),t.Y8G("chat",c.selectedChat),t.R7$(1),t.Y8G("ngIf",c.showNewChatModal),t.R7$(1),t.Y8G("ngIf",c.showNewChatModal))},dependencies:[e.Sq,e.bT,i.me,i.BC,i.vS,C.B,x.E,e.P9],styles:[".chat-layout[_ngcontent-%COMP%]{display:flex;height:calc(100vh - 120px);background:#fff;border-radius:8px;box-shadow:0 2px 10px #0000001a;overflow:hidden}.chat-sidebar[_ngcontent-%COMP%]{width:350px;border-right:1px solid #e9ecef;display:flex;flex-direction:column;background:#f8f9fa}.chat-sidebar-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #e9ecef;background:#fff;display:flex;justify-content:space-between;align-items:center}.chat-sidebar-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{color:#495057;font-weight:600}.chat-sidebar-header[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:50%;width:36px;height:36px;display:flex;align-items:center;justify-content:center;padding:0}.chat-sidebar-body[_ngcontent-%COMP%]{flex:1;overflow-y:auto}.chat-items[_ngcontent-%COMP%]{padding:.5rem 0}.chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem 1rem;cursor:pointer;transition:background-color .2s;border-bottom:1px solid #f1f3f4}.chat-item[_ngcontent-%COMP%]:hover{background-color:#e9ecef}.chat-item.active[_ngcontent-%COMP%]{background-color:#e3f2fd;border-left:3px solid #2196f3}.chat-avatar[_ngcontent-%COMP%]{position:relative;margin-right:.75rem}.chat-avatar[_ngcontent-%COMP%]   .avatar-img[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;object-fit:cover}.chat-avatar[_ngcontent-%COMP%]   .online-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:12px;height:12px;background:#4caf50;border:2px solid #fff;border-radius:50%}.chat-content[_ngcontent-%COMP%]{flex:1;min-width:0}.chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-bottom:.25rem}.chat-name[_ngcontent-%COMP%]{font-size:.9rem;font-weight:600;color:#212529;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-time[_ngcontent-%COMP%]{font-size:.75rem;white-space:nowrap}.chat-preview[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:.8rem;line-height:1.3;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.chat-meta[_ngcontent-%COMP%]{margin-left:.5rem;display:flex;flex-direction:column;align-items:center}.chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.7rem;min-width:18px;height:18px;display:flex;align-items:center;justify-content:center}.chat-main[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column}.modal.show[_ngcontent-%COMP%]{background:rgba(0,0,0,.5)}.doctor-list[_ngcontent-%COMP%]{max-height:400px;overflow-y:auto}.doctor-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem;border:1px solid #e9ecef;border-radius:8px;margin-bottom:.5rem;cursor:pointer;transition:all .2s}.doctor-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#2196f3;transform:translateY(-1px);box-shadow:0 2px 8px #0000001a}.doctor-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.doctor-avatar[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;object-fit:cover;margin-right:.75rem}.doctor-info[_ngcontent-%COMP%]{flex:1}.doctor-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-bottom:.25rem;color:#212529;font-weight:600}.doctor-info[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]{font-size:.85rem}.doctor-info[_ngcontent-%COMP%]   .doctor-details[_ngcontent-%COMP%]{margin-top:.25rem}@media (max-width: 768px){.chat-layout[_ngcontent-%COMP%]{height:calc(100vh - 80px)}.chat-sidebar[_ngcontent-%COMP%]{width:100%;position:absolute;z-index:10;height:100%;transform:translate(-100%);transition:transform .3s}.chat-sidebar.show[_ngcontent-%COMP%]{transform:translate(0)}.chat-main[_ngcontent-%COMP%]{width:100%}}.text-center[_ngcontent-%COMP%]   .spinner-border-sm[_ngcontent-%COMP%]{width:1.5rem;height:1.5rem}.text-center[_ngcontent-%COMP%]   .fs-1[_ngcontent-%COMP%]{font-size:3rem!important;opacity:.3}"]})}}return o})();var W=m(1129),H=m(5567);const J=["messagesContainer"],K=["messageInput"],Q=["fileInput"];function Z(o,p){if(1&o&&(t.j41(0,"span"),t.EFF(1),t.k0s()),2&o){const n=t.XpG().ngIf;t.R7$(1),t.SpI(" - ",n.statusMessage,"")}}function q(o,p){if(1&o&&(t.j41(0,"p",38),t.EFF(1),t.nI1(2,"lowercase"),t.DNE(3,Z,2,1,"span",39),t.k0s()),2&o){const n=p.ngIf;t.R7$(1),t.SpI(" ",t.bMT(2,2,n.status)," "),t.R7$(2),t.Y8G("ngIf",n.statusMessage)}}function tt(o,p){if(1&o&&(t.j41(0,"div",31)(1,"div",32),t.nrm(2,"img",33)(3,"span",34),t.k0s(),t.j41(4,"div",35)(5,"h3"),t.EFF(6),t.k0s(),t.j41(7,"p",36),t.EFF(8),t.k0s(),t.DNE(9,q,4,4,"p",37),t.k0s()()),2&o){const n=t.XpG();t.R7$(2),t.Y8G("src",n.chat.doctor.avatar||"/assets/images/default-avatar.png",t.B4B)("alt",n.chat.doctor.fullName),t.R7$(1),t.AVh("online",n.presenceService.isUserOnline(n.chat.doctor.id))("offline",!n.presenceService.isUserOnline(n.chat.doctor.id)),t.R7$(3),t.JRh(n.chat.doctor.fullName),t.R7$(2),t.JRh(n.chat.doctor.specialization),t.R7$(1),t.Y8G("ngIf",n.presenceService.getUserPresence(n.chat.doctor.id))}}function et(o,p){1&o&&(t.j41(0,"div",40)(1,"div",41),t.nrm(2,"i",42),t.EFF(3," Connection lost. Trying to reconnect... "),t.k0s()())}function nt(o,p){1&o&&t.nrm(0,"i",46)}function it(o,p){1&o&&(t.j41(0,"span"),t.EFF(1,"Load earlier messages"),t.k0s())}function st(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",43)(1,"button",44),t.bIt("click",function(){t.eBV(n);const c=t.XpG();return t.Njj(c.loadMoreMessages())}),t.DNE(2,nt,1,0,"i",45),t.DNE(3,it,2,0,"span",39),t.k0s()()}if(2&o){const n=t.XpG();t.R7$(1),t.Y8G("disabled",n.isLoading),t.R7$(1),t.Y8G("ngIf",n.isLoading),t.R7$(1),t.Y8G("ngIf",!n.isLoading)}}function at(o,p){if(1&o&&(t.j41(0,"div",56),t.nrm(1,"img",57),t.j41(2,"span",58),t.EFF(3),t.k0s(),t.j41(4,"span",59),t.EFF(5),t.nI1(6,"date"),t.k0s()()),2&o){const n=t.XpG().$implicit;t.R7$(1),t.Y8G("src",n.sender.avatar||"/assets/images/default-avatar.png",t.B4B)("alt",n.sender.fullName),t.R7$(2),t.JRh(n.sender.fullName),t.R7$(2),t.JRh(t.i5U(6,4,n.createdAt,"short"))}}function ot(o,p){1&o&&(t.j41(0,"span"),t.EFF(1,"..."),t.k0s())}function r(o,p){if(1&o&&(t.j41(0,"div",60)(1,"div",61),t.nrm(2,"i",62),t.j41(3,"span"),t.EFF(4),t.k0s()(),t.j41(5,"div",63),t.EFF(6),t.nI1(7,"slice"),t.DNE(8,ot,2,0,"span",39),t.k0s()()),2&o){const n=t.XpG().$implicit;t.R7$(4),t.SpI("Replying to ",n.replyToMessage.sender.fullName,""),t.R7$(2),t.SpI(" ",t.brH(7,3,n.replyToMessage.content,0,100)," "),t.R7$(2),t.Y8G("ngIf",n.replyToMessage.content.length>100)}}function O(o,p){if(1&o&&(t.j41(0,"div",64),t.EFF(1),t.k0s()),2&o){const n=t.XpG().$implicit;t.R7$(1),t.SpI(" ",n.content," ")}}function s(o,p){if(1&o&&(t.j41(0,"div",72),t.nrm(1,"img",73),t.k0s()),2&o){const n=t.XpG(2).$implicit;t.R7$(1),t.Y8G("src",n.fileUrl,t.B4B)("alt",n.fileName)}}function d(o,p){if(1&o&&(t.j41(0,"div",65)(1,"div",66),t.nrm(2,"i",21),t.j41(3,"span",67),t.EFF(4),t.k0s(),t.j41(5,"span",68),t.EFF(6),t.k0s()(),t.DNE(7,s,2,2,"div",69),t.j41(8,"a",70),t.nrm(9,"i",71),t.EFF(10," Download "),t.k0s()()),2&o){const n=t.XpG().$implicit,a=t.XpG();t.R7$(4),t.JRh(n.fileName),t.R7$(2),t.SpI("(",a.formatFileSize(n.fileSize||0),")"),t.R7$(1),t.Y8G("ngIf",null==n.fileType?null:n.fileType.startsWith("image/")),t.R7$(1),t.Y8G("href",n.fileUrl,t.B4B)("download",n.fileName)}}function g(o,p){1&o&&t.nrm(0,"i",81)}function _(o,p){1&o&&t.nrm(0,"i",82)}function T(o,p){1&o&&t.nrm(0,"i",83)}function F(o,p){1&o&&t.nrm(0,"i",84)}function ct(o,p){1&o&&t.nrm(0,"i",85)}function N(o,p){if(1&o&&(t.j41(0,"div",74)(1,"span",75),t.DNE(2,g,1,0,"i",76),t.DNE(3,_,1,0,"i",77),t.DNE(4,T,1,0,"i",78),t.DNE(5,F,1,0,"i",79),t.DNE(6,ct,1,0,"i",80),t.k0s(),t.j41(7,"span",59),t.EFF(8),t.nI1(9,"date"),t.k0s()()),2&o){const n=t.XpG().$implicit;t.R7$(1),t.Y8G("ngSwitch",n.status),t.R7$(1),t.Y8G("ngSwitchCase","SENDING"),t.R7$(1),t.Y8G("ngSwitchCase","SENT"),t.R7$(1),t.Y8G("ngSwitchCase","DELIVERED"),t.R7$(1),t.Y8G("ngSwitchCase","READ"),t.R7$(1),t.Y8G("ngSwitchCase","FAILED"),t.R7$(2),t.JRh(t.i5U(9,7,n.createdAt,"short"))}}function L(o,p){if(1&o&&(t.j41(0,"div",88)(1,"span",89),t.EFF(2),t.k0s(),t.j41(3,"span",90),t.EFF(4),t.k0s()()),2&o){const n=p.$implicit;t.R7$(2),t.JRh(n.emoji),t.R7$(2),t.JRh(n.count)}}function rt(o,p){if(1&o&&(t.j41(0,"div",86),t.DNE(1,L,5,2,"div",87),t.k0s()),2&o){const n=t.XpG().$implicit,a=t.XpG();t.R7$(1),t.Y8G("ngForOf",a.getMessageReactions(n))}}function lt(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",91)(1,"button",92),t.bIt("click",function(){t.eBV(n);const c=t.XpG().$implicit,v=t.XpG();return t.Njj(v.replyTo(c))}),t.nrm(2,"i",62),t.k0s(),t.j41(3,"button",93),t.bIt("click",function(){t.eBV(n);const c=t.XpG().$implicit,v=t.XpG();return t.Njj(v.addReaction(c.id,"\u{1f44d}"))}),t.nrm(4,"i",94),t.k0s()()}}function gt(o,p){if(1&o&&(t.j41(0,"div",47),t.DNE(1,at,7,7,"div",48),t.DNE(2,r,9,7,"div",49),t.j41(3,"div",50),t.DNE(4,O,2,1,"div",51),t.DNE(5,d,11,5,"div",52),t.DNE(6,N,10,10,"div",53),t.k0s(),t.DNE(7,rt,2,1,"div",54),t.DNE(8,lt,5,0,"div",55),t.k0s()),2&o){const n=p.$implicit,a=t.XpG();t.AVh("own-message",n.sender.id===(null==a.currentUser?null:a.currentUser.id))("other-message",n.sender.id!==(null==a.currentUser?null:a.currentUser.id)),t.R7$(1),t.Y8G("ngIf",n.sender.id!==(null==a.currentUser?null:a.currentUser.id)),t.R7$(1),t.Y8G("ngIf",n.replyToMessage),t.R7$(2),t.Y8G("ngIf",n.content),t.R7$(1),t.Y8G("ngIf",n.hasAttachment),t.R7$(1),t.Y8G("ngIf",n.sender.id===(null==a.currentUser?null:a.currentUser.id)),t.R7$(1),t.Y8G("ngIf",n.reactions&&a.Object.keys(n.reactions).length>0),t.R7$(1),t.Y8G("ngIf",n.sender.id!==(null==a.currentUser?null:a.currentUser.id))}}function dt(o,p){if(1&o&&(t.j41(0,"div",95)(1,"div",96),t.nrm(2,"span")(3,"span")(4,"span"),t.k0s(),t.j41(5,"span",97),t.EFF(6),t.k0s()()),2&o){const n=t.XpG();t.R7$(6),t.JRh(n.getTypingText())}}function mt(o,p){1&o&&(t.j41(0,"span"),t.EFF(1,"..."),t.k0s())}function pt(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",98)(1,"div",99),t.nrm(2,"i",62),t.j41(3,"span"),t.EFF(4),t.k0s(),t.j41(5,"button",100),t.bIt("click",function(){t.eBV(n);const c=t.XpG();return t.Njj(c.cancelReply())}),t.nrm(6,"i",101),t.k0s()(),t.j41(7,"div",63),t.EFF(8),t.nI1(9,"slice"),t.DNE(10,mt,2,0,"span",39),t.k0s()()}if(2&o){const n=t.XpG();t.R7$(4),t.SpI("Replying to ",n.replyToMessage.sender.fullName,""),t.R7$(4),t.SpI(" ",t.brH(9,3,n.replyToMessage.content,0,100)," "),t.R7$(2),t.Y8G("ngIf",n.replyToMessage.content.length>100)}}function ht(o,p){if(1&o){const n=t.RV6();t.j41(0,"div",102)(1,"div",103),t.nrm(2,"i",21),t.j41(3,"span",67),t.EFF(4),t.k0s(),t.j41(5,"span",68),t.EFF(6),t.k0s(),t.j41(7,"button",100),t.bIt("click",function(){t.eBV(n);const c=t.XpG();return t.Njj(c.removeSelectedFile())}),t.nrm(8,"i",101),t.k0s()()()}if(2&o){const n=t.XpG();t.R7$(4),t.JRh(n.selectedFile.name),t.R7$(2),t.SpI("(",n.formatFileSize(n.selectedFile.size),")")}}function ut(o,p){1&o&&(t.j41(0,"div",104)(1,"div",105),t.nrm(2,"i",106),t.j41(3,"p"),t.EFF(4,"Drop files here to send"),t.k0s()()())}const ft=[{path:"",canActivate:[w.q],component:V},{path:"enhanced",canActivate:[w.q],component:(()=>{class o{constructor(n,a,c,v,I,S,bt){this.route=n,this.router=a,this.fb=c,this.chatService=v,this.presenceService=I,this.authService=S,this.notificationService=bt,this.chat=null,this.messages=[],this.isLoading=!1,this.isConnected=!1,this.isTyping=!1,this.typingUsers=[],this.showEmojiPicker=!1,this.showAttachmentMenu=!1,this.replyToMessage=null,this.selectedFile=null,this.dragOver=!1,this.currentPage=0,this.pageSize=50,this.hasMoreMessages=!0,this.subscriptions=[],this.shouldScrollToBottom=!0,this.Object=Object,this.messageForm=this.fb.group({content:["",[i.k0.required,i.k0.maxLength(1e3)]]})}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.initializeChat(),this.setupSubscriptions()}ngOnDestroy(){this.subscriptions.forEach(n=>n.unsubscribe()),this.typingTimeout&&clearTimeout(this.typingTimeout)}ngAfterViewChecked(){this.shouldScrollToBottom&&(this.scrollToBottom(),this.shouldScrollToBottom=!1)}initializeChat(){const n=this.route.snapshot.queryParams.chatId;n?(this.isLoading=!0,this.loadChatMessages(n),this.chatService.subscribeToChatMessages(n)):this.router.navigate(["/chat"])}setupSubscriptions(){this.subscriptions.push(this.chatService.connectionStatus$.subscribe(n=>{this.isConnected=n})),this.subscriptions.push(this.chatService.messages$.subscribe(n=>{this.addMessage(n),this.shouldScrollToBottom=!0,n.sender.id!==this.currentUser?.id&&this.chatService.markMessageAsRead(n.id)})),this.subscriptions.push(this.chatService.typing$.subscribe(n=>{this.handleTypingNotification(n)})),this.subscriptions.push(this.chatService.messageStatus$.subscribe(n=>{this.handleMessageStatusUpdate(n)})),this.subscriptions.push(this.chatService.messageReactions$.subscribe(n=>{this.handleMessageReaction(n)})),this.subscriptions.push(this.presenceService.getTypingNotifications().subscribe(n=>{this.handlePresenceTyping(n)}))}loadChatMessages(n){this.chatService.getChatMessages(n,this.currentPage,this.pageSize).subscribe({next:a=>{this.messages=a.reverse(),this.isLoading=!1,this.shouldScrollToBottom=!0,this.hasMoreMessages=a.length===this.pageSize},error:a=>{console.error("Error loading messages:",a),this.isLoading=!1,this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to load chat messages",priority:"high"})}})}addMessage(n){const a=this.messages.findIndex(c=>c.id===n.id);-1===a?this.messages.push(n):this.messages[a]=n}handleTypingNotification(n){if(n.userId===this.currentUser?.id)return;const a=n.userEmail||`User ${n.userId}`;"typing"===n.status?this.typingUsers.includes(a)||this.typingUsers.push(a):this.typingUsers=this.typingUsers.filter(c=>c!==a)}handlePresenceTyping(n){parseInt(this.route.snapshot.queryParams.chatId)}handleMessageStatusUpdate(n){const a=this.messages.findIndex(c=>c.id===n.messageId);-1!==a&&(this.messages[a].status=n.status,this.messages[a].readAt=n.timestamp)}handleMessageReaction(n){const a=this.messages.findIndex(c=>c.id===n.messageId);-1!==a&&(this.messages[a].reactions||(this.messages[a].reactions={}),"added"===n.action?this.messages[a].reactions[n.userId]=n.reaction:delete this.messages[a].reactions[n.userId])}sendMessage(){if(this.messageForm.invalid||!this.isConnected)return;const n=this.messageForm.get("content")?.value?.trim();if(!n&&!this.selectedFile)return;const a=this.route.snapshot.queryParams.chatId;try{this.selectedFile?(this.chatService.sendMessageWithAttachment(a,n,this.selectedFile).subscribe({next:c=>{this.addMessage(c),this.shouldScrollToBottom=!0},error:c=>{console.error("Error sending message with attachment:",c),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to send message with attachment",priority:"high"})}}),this.selectedFile=null):this.replyToMessage?(this.chatService.replyToMessage(a,n,this.replyToMessage.id),this.replyToMessage=null):this.chatService.sendMessage(a,n),this.messageForm.reset(),this.stopTyping()}catch(c){console.error("Error sending message:",c),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to send message",priority:"high"})}}onTyping(){this.isTyping||(this.isTyping=!0,this.presenceService.startTyping(this.route.snapshot.queryParams.chatId)),this.typingTimeout&&clearTimeout(this.typingTimeout),this.typingTimeout=setTimeout(()=>{this.stopTyping()},2e3)}stopTyping(){this.isTyping&&(this.isTyping=!1,this.presenceService.stopTyping(this.route.snapshot.queryParams.chatId)),this.typingTimeout&&(clearTimeout(this.typingTimeout),this.typingTimeout=null)}addReaction(n,a){this.chatService.addMessageReaction(n,a)}replyTo(n){this.replyToMessage=n,this.messageInput.nativeElement.focus()}cancelReply(){this.replyToMessage=null}onFileSelected(n){const a=n.target.files[0];a&&(this.selectedFile=a)}removeSelectedFile(){this.selectedFile=null,this.fileInput&&(this.fileInput.nativeElement.value="")}onDragOver(n){n.preventDefault(),this.dragOver=!0}onDragLeave(n){n.preventDefault(),this.dragOver=!1}onDrop(n){n.preventDefault(),this.dragOver=!1;const a=n.dataTransfer?.files;a&&a.length>0&&(this.selectedFile=a[0])}scrollToBottom(){if(this.messagesContainer){const n=this.messagesContainer.nativeElement;n.scrollTop=n.scrollHeight}}loadMoreMessages(){this.hasMoreMessages&&!this.isLoading&&(this.currentPage++,this.chatService.getChatMessages(this.route.snapshot.queryParams.chatId,this.currentPage,this.pageSize).subscribe({next:a=>{a.length>0?(this.messages=[...a.reverse(),...this.messages],this.hasMoreMessages=a.length===this.pageSize):this.hasMoreMessages=!1},error:a=>{console.error("Error loading more messages:",a),this.currentPage--}}))}getTypingText(){return 0===this.typingUsers.length?"":1===this.typingUsers.length?`${this.typingUsers[0]} is typing...`:2===this.typingUsers.length?`${this.typingUsers[0]} and ${this.typingUsers[1]} are typing...`:`${this.typingUsers.length} people are typing...`}formatFileSize(n){if(0===n)return"0 Bytes";const v=Math.floor(Math.log(n)/Math.log(1024));return parseFloat((n/Math.pow(1024,v)).toFixed(2))+" "+["Bytes","KB","MB","GB"][v]}trackByMessageId(n,a){return a.id}getMessageReactions(n){if(!n.reactions)return[];const a={};return Object.values(n.reactions).forEach(c=>{a[c]=(a[c]||0)+1}),Object.entries(a).map(([c,v])=>({emoji:c,count:v}))}static{this.\u0275fac=function(a){return new(a||o)(t.rXU(P.nX),t.rXU(P.Ix),t.rXU(i.ok),t.rXU(R.m),t.rXU(W.B),t.rXU(y.u),t.rXU(H.J))}}static{this.\u0275cmp=t.VBU({type:o,selectors:[["app-enhanced-chat"]],viewQuery:function(a,c){if(1&a&&(t.GBs(J,5),t.GBs(K,5),t.GBs(Q,5)),2&a){let v;t.mGM(v=t.lsd())&&(c.messagesContainer=v.first),t.mGM(v=t.lsd())&&(c.messageInput=v.first),t.mGM(v=t.lsd())&&(c.fileInput=v.first)}},decls:31,vars:15,consts:[[1,"enhanced-chat-container",3,"dragover","dragleave","drop"],[1,"chat-header"],[1,"chat-info"],["class","participant-info",4,"ngIf"],[1,"chat-actions"],["title","Start Video Call",1,"btn","btn-outline-primary","btn-sm",3,"disabled"],[1,"fas","fa-video"],["title","Chat Settings",1,"btn","btn-outline-secondary","btn-sm"],[1,"fas","fa-cog"],["class","connection-status",4,"ngIf"],[1,"messages-container"],["messagesContainer",""],["class","load-more",4,"ngIf"],["class","message",3,"own-message","other-message",4,"ngFor","ngForOf","ngForTrackBy"],["class","typing-indicator",4,"ngIf"],["class","reply-preview",4,"ngIf"],["class","file-preview",4,"ngIf"],[1,"message-input-container"],[1,"message-form",3,"formGroup","ngSubmit"],[1,"input-group"],["type","button","title","Attach File",1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-paperclip"],["type","text","formControlName","content","placeholder","Type a message...",1,"form-control",3,"disabled","input","blur"],["messageInput",""],["type","button","title","Add Emoji",1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-smile"],["type","submit",1,"btn","btn-primary",3,"disabled"],[1,"fas","fa-paper-plane"],["type","file","accept","image/*,application/pdf,.doc,.docx,.txt",2,"display","none",3,"change"],["fileInput",""],["class","drag-overlay",4,"ngIf"],[1,"participant-info"],[1,"avatar"],[3,"src","alt"],[1,"status-indicator"],[1,"details"],[1,"specialization"],["class","status",4,"ngIf"],[1,"status"],[4,"ngIf"],[1,"connection-status"],[1,"alert","alert-warning"],[1,"fas","fa-exclamation-triangle"],[1,"load-more"],[1,"btn","btn-link",3,"disabled","click"],["class","fas fa-spinner fa-spin",4,"ngIf"],[1,"fas","fa-spinner","fa-spin"],[1,"message"],["class","message-header",4,"ngIf"],["class","reply-context",4,"ngIf"],[1,"message-content"],["class","message-text",4,"ngIf"],["class","message-attachment",4,"ngIf"],["class","message-status",4,"ngIf"],["class","message-reactions",4,"ngIf"],["class","message-actions",4,"ngIf"],[1,"message-header"],[1,"sender-avatar",3,"src","alt"],[1,"sender-name"],[1,"message-time"],[1,"reply-context"],[1,"reply-indicator"],[1,"fas","fa-reply"],[1,"reply-content"],[1,"message-text"],[1,"message-attachment"],[1,"attachment-info"],[1,"filename"],[1,"filesize"],["class","image-preview",4,"ngIf"],[1,"download-link",3,"href","download"],[1,"fas","fa-download"],[1,"image-preview"],[1,"attachment-image",3,"src","alt"],[1,"message-status"],[1,"status-icon",3,"ngSwitch"],["class","fas fa-clock text-muted","title","Sending",4,"ngSwitchCase"],["class","fas fa-check text-muted","title","Sent",4,"ngSwitchCase"],["class","fas fa-check-double text-info","title","Delivered",4,"ngSwitchCase"],["class","fas fa-check-double text-primary","title","Read",4,"ngSwitchCase"],["class","fas fa-exclamation-triangle text-danger","title","Failed",4,"ngSwitchCase"],["title","Sending",1,"fas","fa-clock","text-muted"],["title","Sent",1,"fas","fa-check","text-muted"],["title","Delivered",1,"fas","fa-check-double","text-info"],["title","Read",1,"fas","fa-check-double","text-primary"],["title","Failed",1,"fas","fa-exclamation-triangle","text-danger"],[1,"message-reactions"],["class","reaction",4,"ngFor","ngForOf"],[1,"reaction"],[1,"emoji"],[1,"count"],[1,"message-actions"],["title","Reply",1,"btn","btn-sm","btn-link",3,"click"],["title","Like",1,"btn","btn-sm","btn-link",3,"click"],[1,"fas","fa-thumbs-up"],[1,"typing-indicator"],[1,"typing-animation"],[1,"typing-text"],[1,"reply-preview"],[1,"reply-header"],[1,"btn","btn-sm","btn-link","ms-auto",3,"click"],[1,"fas","fa-times"],[1,"file-preview"],[1,"file-info"],[1,"drag-overlay"],[1,"drag-content"],[1,"fas","fa-cloud-upload-alt"]],template:function(a,c){if(1&a){const v=t.RV6();t.j41(0,"div",0),t.bIt("dragover",function(S){return c.onDragOver(S)})("dragleave",function(S){return c.onDragLeave(S)})("drop",function(S){return c.onDrop(S)}),t.j41(1,"div",1)(2,"div",2),t.DNE(3,tt,10,9,"div",3),t.k0s(),t.j41(4,"div",4)(5,"button",5),t.nrm(6,"i",6),t.k0s(),t.j41(7,"button",7),t.nrm(8,"i",8),t.k0s()()(),t.DNE(9,et,4,0,"div",9),t.j41(10,"div",10,11),t.DNE(12,st,4,3,"div",12),t.DNE(13,gt,9,11,"div",13),t.DNE(14,dt,7,1,"div",14),t.k0s(),t.DNE(15,pt,11,7,"div",15),t.DNE(16,ht,9,2,"div",16),t.j41(17,"div",17)(18,"form",18),t.bIt("ngSubmit",function(){return c.sendMessage()}),t.j41(19,"div",19)(20,"button",20),t.bIt("click",function(){t.eBV(v);const S=t.sdS(29);return t.Njj(S.click())}),t.nrm(21,"i",21),t.k0s(),t.j41(22,"input",22,23),t.bIt("input",function(){return c.onTyping()})("blur",function(){return c.stopTyping()}),t.k0s(),t.j41(24,"button",24),t.bIt("click",function(){return c.showEmojiPicker=!c.showEmojiPicker}),t.nrm(25,"i",25),t.k0s(),t.j41(26,"button",26),t.nrm(27,"i",27),t.k0s()()(),t.j41(28,"input",28,29),t.bIt("change",function(S){return c.onFileSelected(S)}),t.k0s()(),t.DNE(30,ut,5,0,"div",30),t.k0s()}2&a&&(t.AVh("drag-over",c.dragOver),t.R7$(3),t.Y8G("ngIf",c.chat),t.R7$(2),t.Y8G("disabled",!c.isConnected),t.R7$(4),t.Y8G("ngIf",!c.isConnected),t.R7$(3),t.Y8G("ngIf",c.hasMoreMessages),t.R7$(1),t.Y8G("ngForOf",c.messages)("ngForTrackBy",c.trackByMessageId),t.R7$(1),t.Y8G("ngIf",c.typingUsers.length>0),t.R7$(1),t.Y8G("ngIf",c.replyToMessage),t.R7$(1),t.Y8G("ngIf",c.selectedFile),t.R7$(2),t.Y8G("formGroup",c.messageForm),t.R7$(4),t.Y8G("disabled",!c.isConnected),t.R7$(4),t.Y8G("disabled",c.messageForm.invalid||!c.isConnected),t.R7$(4),t.Y8G("ngIf",c.dragOver))},dependencies:[e.Sq,e.bT,e.ux,e.e1,i.qT,i.me,i.BC,i.cb,i.j4,i.JD,e.GH,e.P9,e.vh],styles:[".enhanced-chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;background:#f8f9fa;position:relative}.enhanced-chat-container.drag-over[_ngcontent-%COMP%]   .drag-overlay[_ngcontent-%COMP%]{display:flex}.chat-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:1rem;background:white;border-bottom:1px solid #e9ecef;box-shadow:0 2px 4px #0000001a}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.75rem}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]{position:relative}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:50%;object-fit:cover}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{position:absolute;bottom:2px;right:2px;width:12px;height:12px;border-radius:50%;border:2px solid white}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]   .status-indicator.online[_ngcontent-%COMP%]{background:#28a745}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]   .status-indicator.offline[_ngcontent-%COMP%]{background:#6c757d}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{margin:0;font-size:1.1rem;font-weight:600;color:#212529}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .specialization[_ngcontent-%COMP%]{margin:0;font-size:.875rem;color:#6c757d}.chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%]{margin:0;font-size:.75rem;color:#28a745;text-transform:capitalize}.chat-header[_ngcontent-%COMP%]   .chat-actions[_ngcontent-%COMP%]{display:flex;gap:.5rem}.connection-status[_ngcontent-%COMP%]{padding:.5rem 1rem}.connection-status[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{margin:0;padding:.5rem 1rem;font-size:.875rem}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:1rem;display:flex;flex-direction:column;gap:1rem}.messages-container[_ngcontent-%COMP%]   .load-more[_ngcontent-%COMP%]{text-align:center;margin-bottom:1rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{display:flex;flex-direction:column;max-width:70%}.messages-container[_ngcontent-%COMP%]   .message.own-message[_ngcontent-%COMP%]{align-self:flex-end}.messages-container[_ngcontent-%COMP%]   .message.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:#007bff;color:#fff;border-radius:18px 18px 4px}.messages-container[_ngcontent-%COMP%]   .message.other-message[_ngcontent-%COMP%]{align-self:flex-start}.messages-container[_ngcontent-%COMP%]   .message.other-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:white;color:#212529;border-radius:18px 18px 18px 4px;box-shadow:0 1px 2px #0000001a}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.25rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .sender-avatar[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;object-fit:cover}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .sender-name[_ngcontent-%COMP%]{font-weight:600;font-size:.875rem;color:#495057}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-header[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d;margin-left:auto}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .reply-context[_ngcontent-%COMP%]{margin-bottom:.5rem;padding:.5rem;background:rgba(0,0,0,.05);border-radius:8px;border-left:3px solid #007bff}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .reply-context[_ngcontent-%COMP%]   .reply-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;font-size:.75rem;color:#6c757d;margin-bottom:.25rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .reply-context[_ngcontent-%COMP%]   .reply-content[_ngcontent-%COMP%]{font-size:.875rem;color:#495057;font-style:italic}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{padding:.75rem 1rem;position:relative}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]{word-wrap:break-word;line-height:1.4}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]{margin-top:.5rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]   .attachment-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.5rem;font-size:.875rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]   .attachment-info[_ngcontent-%COMP%]   .filename[_ngcontent-%COMP%]{font-weight:500}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]   .attachment-info[_ngcontent-%COMP%]   .filesize[_ngcontent-%COMP%]{color:#6c757d}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]{margin-bottom:.5rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]   .image-preview[_ngcontent-%COMP%]   .attachment-image[_ngcontent-%COMP%]{max-width:100%;max-height:200px;border-radius:8px;object-fit:cover}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]   .download-link[_ngcontent-%COMP%]{display:inline-flex;align-items:center;gap:.25rem;color:#007bff;text-decoration:none;font-size:.875rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-attachment[_ngcontent-%COMP%]   .download-link[_ngcontent-%COMP%]:hover{text-decoration:underline}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-status[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:flex-end;gap:.25rem;margin-top:.25rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-status[_ngcontent-%COMP%]   .status-icon[_ngcontent-%COMP%]{font-size:.75rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]   .message-status[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{font-size:.75rem;color:#ffffffb3}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-reactions[_ngcontent-%COMP%]{display:flex;gap:.25rem;margin-top:.25rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-reactions[_ngcontent-%COMP%]   .reaction[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.25rem;padding:.125rem .375rem;background:#f8f9fa;border-radius:12px;font-size:.75rem;border:1px solid #e9ecef}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-reactions[_ngcontent-%COMP%]   .reaction[_ngcontent-%COMP%]   .emoji[_ngcontent-%COMP%]{font-size:.875rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-reactions[_ngcontent-%COMP%]   .reaction[_ngcontent-%COMP%]   .count[_ngcontent-%COMP%]{color:#6c757d}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-actions[_ngcontent-%COMP%]{display:flex;gap:.25rem;margin-top:.25rem;opacity:0;transition:opacity .2s}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]   .message-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.75rem}.messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]:hover   .message-actions[_ngcontent-%COMP%]{opacity:1}.messages-container[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;background:white;border-radius:18px;max-width:200px;box-shadow:0 1px 2px #0000001a}.messages-container[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-animation[_ngcontent-%COMP%]{display:flex;gap:.25rem}.messages-container[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-animation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:6px;height:6px;background:#6c757d;border-radius:50%;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.messages-container[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-animation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:-.32s}.messages-container[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-animation[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:-.16s}.messages-container[_ngcontent-%COMP%]   .typing-indicator[_ngcontent-%COMP%]   .typing-text[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.reply-preview[_ngcontent-%COMP%]{padding:.75rem 1rem;background:#e3f2fd;border-top:1px solid #bbdefb}.reply-preview[_ngcontent-%COMP%]   .reply-header[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;color:#1976d2;margin-bottom:.25rem}.reply-preview[_ngcontent-%COMP%]   .reply-content[_ngcontent-%COMP%]{font-size:.875rem;color:#424242;font-style:italic}.file-preview[_ngcontent-%COMP%]{padding:.75rem 1rem;background:#f3e5f5;border-top:1px solid #e1bee7}.file-preview[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;font-size:.875rem;color:#7b1fa2}.file-preview[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .filename[_ngcontent-%COMP%]{font-weight:500}.file-preview[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .filesize[_ngcontent-%COMP%]{color:#9c27b0}.message-input-container[_ngcontent-%COMP%]{padding:1rem;background:white;border-top:1px solid #e9ecef}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{border:1px solid #ced4da;border-radius:24px;padding:.75rem 1rem}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{border-color:#007bff;box-shadow:0 0 0 .2rem #007bff40}.message-input-container[_ngcontent-%COMP%]   .message-form[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:50%;width:40px;height:40px;display:flex;align-items:center;justify-content:center}.drag-overlay[_ngcontent-%COMP%]{position:absolute;inset:0;background:rgba(0,123,255,.1);display:none;align-items:center;justify-content:center;z-index:1000}.drag-overlay[_ngcontent-%COMP%]   .drag-content[_ngcontent-%COMP%]{text-align:center;color:#007bff}.drag-overlay[_ngcontent-%COMP%]   .drag-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:3rem;margin-bottom:1rem}.drag-overlay[_ngcontent-%COMP%]   .drag-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.25rem;font-weight:500}@keyframes _ngcontent-%COMP%_typing{0%,80%,to{transform:scale(0)}40%{transform:scale(1)}}@media (max-width: 768px){.enhanced-chat-container[_ngcontent-%COMP%]   .messages-container[_ngcontent-%COMP%]   .message[_ngcontent-%COMP%]{max-width:85%}.enhanced-chat-container[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]{padding:.75rem}.enhanced-chat-container[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px}.enhanced-chat-container[_ngcontent-%COMP%]   .chat-header[_ngcontent-%COMP%]   .participant-info[_ngcontent-%COMP%]   .details[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1rem}}"]})}}return o})()}];let Ct=(()=>{class o{static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275mod=t.$C({type:o})}static{this.\u0275inj=t.G2t({imports:[P.iI.forChild(ft),P.iI]})}}return o})();var Mt=m(3887);let vt=(()=>{class o{static{this.\u0275fac=function(a){return new(a||o)}}static{this.\u0275mod=t.$C({type:o})}static{this.\u0275inj=t.G2t({imports:[e.MD,i.YN,i.X1,P.iI,Ct,Mt.G]})}}return o})()},7337:(G,E,m)=>{m.d(E,{Y:()=>i,y:()=>e});var e=function(P){return P.PENDING="PENDING",P.SCHEDULED="SCHEDULED",P.CONFIRMED="CONFIRMED",P.COMPLETED="COMPLETED",P.CANCELLED="CANCELLED",P.NO_SHOW="NO_SHOW",P}(e||{}),i=function(P){return P.IN_PERSON="IN_PERSON",P.VIDEO_CALL="VIDEO_CALL",P}(i||{})},9545:(G,E,m)=>{m.d(E,{h:()=>w});var e=m(1626),i=m(7337),P=m(6276);let w=(()=>{class t{constructor(h){this.http=h,this.apiUrl="http://localhost:8080/api"}getDoctors(h){let y=new e.Nl;return h&&(y=y.set("specialization",h)),this.http.get(`${this.apiUrl}/doctors`,{params:y})}getDoctor(h){return this.http.get(`${this.apiUrl}/doctors/${h}`)}getSpecializations(){return this.http.get(`${this.apiUrl}/doctors/specializations`)}getAvailableTimeSlots(h,y){const C=(new e.Nl).set("date",y);return this.http.get(`${this.apiUrl}/doctors/${h}/time-slots`,{params:C})}getAppointments(h,y,C,x){let b=new e.Nl;return h&&(b=b.set("status",h)),y&&(b=b.set("type",y)),C&&(b=b.set("startDate",C)),x&&(b=b.set("endDate",x)),this.http.get(`${this.apiUrl}/appointments`,{params:b})}getAppointment(h){return this.http.get(`${this.apiUrl}/appointments/${h}`)}createAppointment(h){return this.http.post(`${this.apiUrl}/appointments`,h)}updateAppointment(h,y){return this.http.put(`${this.apiUrl}/appointments/${h}`,y)}cancelAppointment(h){return this.http.delete(`${this.apiUrl}/appointments/${h}`)}getTodayAppointments(){return this.http.get(`${this.apiUrl}/appointments/today`)}getPatientAppointments(){return this.http.get(`${this.apiUrl}/appointments`)}createAppointmentChat(h,y,C="PRE_APPOINTMENT",x){return this.http.post(`${this.apiUrl}/chats/appointment/${h}`,{participantId:y,chatType:C,subject:x})}getStatusDisplayName(h){switch(h){case i.y.PENDING:return"Pending";case i.y.SCHEDULED:return"Scheduled";case i.y.CONFIRMED:return"Confirmed";case i.y.COMPLETED:return"Completed";case i.y.CANCELLED:return"Cancelled";case i.y.NO_SHOW:return"No Show";default:return h}}getTypeDisplayName(h){switch(h){case i.Y.IN_PERSON:return"In Person";case i.Y.VIDEO_CALL:return"Video Call";default:return h}}getStatusBadgeClass(h){switch(h){case i.y.PENDING:return"badge-warning";case i.y.SCHEDULED:return"badge-info";case i.y.CONFIRMED:return"badge-primary";case i.y.COMPLETED:return"badge-success";case i.y.CANCELLED:return"badge-danger";default:return"badge-secondary"}}static{this.\u0275fac=function(y){return new(y||t)(P.KVO(e.Qq))}}static{this.\u0275prov=P.jDH({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})()},1129:(G,E,m)=>{m.d(E,{B:()=>x});var e=m(4412),i=m(3794),P=m(5877),w=m(2869),t=m(25),R=m.n(t),h=m(5312),y=m(6276),C=m(8010);let x=(()=>{class b{constructor(u){this.authService=u,this.stompClient=null,this.wsUrl=`${h.c.apiUrl}/ws`,this.userPresences$=new e.t(new Map),this.currentUserPresence$=new e.t(null),this.typingNotifications$=new i.B,this.connectionStatus$=new e.t(!1),this.typingTimeouts=new Map,setTimeout(()=>{this.initializeConnection()},100)}initializeConnection(){if(!this.authService.isAuthenticated())return;const u=this.authService.getToken();u&&(this.stompClient=new w.K({webSocketFactory:()=>new(R())(this.wsUrl),connectHeaders:{Authorization:`Bearer ${u}`},debug:l=>{console.log("Presence STOMP Debug:",l)},onConnect:()=>{this.connectionStatus$.next(!0),console.log("Presence WebSocket connected"),this.subscribeToPresenceUpdates(),this.startHeartbeat(),this.updatePresence("ONLINE")},onWebSocketClose:()=>{this.connectionStatus$.next(!1),console.log("Presence WebSocket disconnected"),this.stopHeartbeat(),setTimeout(()=>{this.authService.isAuthenticated()&&this.connect()},5e3)},onStompError:l=>{console.error("Presence STOMP error:",l),this.connectionStatus$.next(!1)}}),this.stompClient.activate())}subscribeToPresenceUpdates(){this.stompClient?.connected&&(this.stompClient.subscribe("/topic/presence",u=>{const l=JSON.parse(u.body);this.handlePresenceUpdate(l)}),this.stompClient.subscribe("/topic/chat/+/typing",u=>{const l=JSON.parse(u.body);this.handleTypingNotification(l)}))}handlePresenceUpdate(u){const l={userId:u.userId,userName:u.userName,status:u.status,statusMessage:u.statusMessage,lastSeen:new Date(u.lastSeen)},M=this.userPresences$.value;M.set(l.userId,l),this.userPresences$.next(new Map(M));const f=this.authService.getCurrentUser();f&&f.id===l.userId&&this.currentUserPresence$.next(l)}handleTypingNotification(u){const l={userId:u.userId,chatId:u.chatId,isTyping:u.isTyping,timestamp:new Date(u.timestamp)};this.typingNotifications$.next(l);const M=this.userPresences$.value,f=M.get(l.userId);f&&(f.isTyping=l.isTyping,f.typingInChatId=l.isTyping?l.chatId:void 0,M.set(l.userId,f),this.userPresences$.next(new Map(M)))}startHeartbeat(){this.heartbeatInterval=(0,P.Y)(3e4).subscribe(()=>{this.sendHeartbeat()})}stopHeartbeat(){this.heartbeatInterval&&(this.heartbeatInterval.unsubscribe(),this.heartbeatInterval=null)}sendHeartbeat(){this.stompClient?.connected&&this.stompClient.publish({destination:"/app/presence/heartbeat",headers:{Authorization:`Bearer ${this.authService.getToken()}`}})}connect(){this.stompClient?.connected||this.initializeConnection()}disconnect(){this.updatePresence("OFFLINE"),this.stopHeartbeat(),this.stompClient&&(this.stompClient.deactivate(),this.connectionStatus$.next(!1))}updatePresence(u,l){if(!this.stompClient?.connected)return;const M=this.authService.getToken();if(!M)return;const f={status:u,statusMessage:l,deviceInfo:navigator.userAgent,ipAddress:""};this.stompClient.publish({destination:"/app/presence/update",body:JSON.stringify(f),headers:{Authorization:`Bearer ${M}`}})}startTyping(u){if(!this.stompClient?.connected)return;const l=this.authService.getToken();if(!l)return;this.stompClient.publish({destination:`/app/chat/${u}/typing`,body:"typing",headers:{Authorization:`Bearer ${l}`}});const M=this.typingTimeouts.get(u);M&&clearTimeout(M);const f=setTimeout(()=>{this.stopTyping(u)},3e3);this.typingTimeouts.set(u,f)}stopTyping(u){if(!this.stompClient?.connected)return;const l=this.authService.getToken();if(!l)return;this.stompClient.publish({destination:`/app/chat/${u}/typing`,body:"stopped",headers:{Authorization:`Bearer ${l}`}});const M=this.typingTimeouts.get(u);M&&(clearTimeout(M),this.typingTimeouts.delete(u))}getUserPresence(u){return this.userPresences$.value.get(u)||null}isUserOnline(u){const l=this.getUserPresence(u);return!!l&&["ONLINE","BUSY","AWAY"].includes(l.status)}isUserTyping(u,l){const M=this.getUserPresence(u);return!(!M||!M.isTyping||l&&M.typingInChatId!==l)}getUserPresences(){return this.userPresences$.asObservable()}getCurrentUserPresence(){return this.currentUserPresence$.asObservable()}getTypingNotifications(){return this.typingNotifications$.asObservable()}getConnectionStatus(){return this.connectionStatus$.asObservable()}getOnlineUsers(){return Array.from(this.userPresences$.value.values()).filter(l=>["ONLINE","BUSY","AWAY"].includes(l.status))}static{this.\u0275fac=function(l){return new(l||b)(y.KVO(C.u))}}static{this.\u0275prov=y.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})}}return b})()},453:(G,E,m)=>{m.d(E,{U:()=>u});var e=m(5877),i=m(6276),P=m(177);function w(l,M){if(1&l&&(i.j41(0,"span",4),i.EFF(1),i.k0s()),2&l){const f=i.XpG();i.R7$(1),i.JRh(f.getStatusText())}}function t(l,M){if(1&l&&(i.j41(0,"div",9),i.nrm(1,"i",10),i.j41(2,"small",11),i.EFF(3),i.k0s()()),2&l){const f=i.XpG(2);i.R7$(3),i.JRh(f.availability.expectedResponseTime)}}function R(l,M){if(1&l&&(i.j41(0,"div",9),i.nrm(1,"i",12),i.j41(2,"small",11),i.EFF(3),i.k0s()()),2&l){const f=i.XpG(2);i.R7$(3),i.Lme(" Chat hours: ",f.availability.chatStartTime," - ",f.availability.chatEndTime," ")}}function h(l,M){if(1&l&&(i.j41(0,"div",9),i.nrm(1,"i",13),i.j41(2,"small",11),i.EFF(3),i.k0s()()),2&l){const f=i.XpG(2);i.R7$(3),i.JRh(f.availability.customMessage)}}function y(l,M){if(1&l&&(i.j41(0,"div",9),i.nrm(1,"i",14),i.j41(2,"small",11),i.EFF(3),i.k0s()()),2&l){const f=i.XpG(2);i.R7$(3),i.SpI("Last seen ",f.getLastSeenText(),"")}}function C(l,M){1&l&&(i.j41(0,"div",15)(1,"div",16),i.nrm(2,"i",17),i.j41(3,"small"),i.EFF(4,"Outside chat hours"),i.k0s()()())}function x(l,M){1&l&&(i.j41(0,"div",18)(1,"div",19),i.nrm(2,"i",20),i.j41(3,"small"),i.EFF(4,"Currently in consultation"),i.k0s()()())}function b(l,M){1&l&&(i.j41(0,"div",18)(1,"div",21),i.nrm(2,"i",22),i.j41(3,"small"),i.EFF(4,"Emergency contacts only"),i.k0s()()())}function A(l,M){if(1&l&&(i.j41(0,"div",5),i.DNE(1,t,4,1,"div",6),i.DNE(2,R,4,2,"div",6),i.DNE(3,h,4,1,"div",6),i.DNE(4,y,4,1,"div",6),i.DNE(5,C,5,0,"div",7),i.DNE(6,x,5,0,"div",8),i.DNE(7,b,5,0,"div",8),i.k0s()),2&l){const f=i.XpG();i.R7$(1),i.Y8G("ngIf",f.availability.expectedResponseTime),i.R7$(1),i.Y8G("ngIf",f.availability.chatStartTime&&f.availability.chatEndTime),i.R7$(1),i.Y8G("ngIf",f.availability.customMessage),i.R7$(1),i.Y8G("ngIf","OFFLINE"===f.availability.status&&f.availability.lastSeen),i.R7$(1),i.Y8G("ngIf",!f.isWithinChatHours()),i.R7$(1),i.Y8G("ngIf","BUSY"===f.availability.status),i.R7$(1),i.Y8G("ngIf","DO_NOT_DISTURB"===f.availability.status)}}let u=(()=>{class l{constructor(){this.showDetails=!0,this.size="md",this.availability=null,this.loading=!1}ngOnInit(){this.loadAvailability(),this.startPeriodicRefresh()}ngOnDestroy(){this.refreshSubscription&&this.refreshSubscription.unsubscribe()}loadAvailability(){this.availability={status:"ONLINE",expectedResponseTime:"Within 2 hours",customMessage:"Available for consultations",chatStartTime:"09:00",chatEndTime:"17:00"}}startPeriodicRefresh(){this.refreshSubscription=(0,e.Y)(3e5).subscribe(()=>{this.loadAvailability()})}getStatusIcon(){if(!this.availability)return"fas fa-circle text-secondary";switch(this.availability.status){case"ONLINE":return"fas fa-circle text-success";case"BUSY":return"fas fa-circle text-warning";case"AWAY":return"fas fa-circle text-info";case"DO_NOT_DISTURB":return"fas fa-minus-circle text-danger";default:return"fas fa-circle text-secondary"}}getStatusText(){if(!this.availability)return"Unknown";switch(this.availability.status){case"ONLINE":return"Online";case"BUSY":return"Busy";case"AWAY":return"Away";case"DO_NOT_DISTURB":return"Do Not Disturb";default:return"Offline"}}getStatusClass(){if(!this.availability)return"status-offline";switch(this.availability.status){case"ONLINE":return"status-online";case"BUSY":return"status-busy";case"AWAY":return"status-away";case"DO_NOT_DISTURB":return"status-dnd";default:return"status-offline"}}isAvailable(){return"ONLINE"===this.availability?.status||"AWAY"===this.availability?.status}getLastSeenText(){if(!this.availability?.lastSeen)return"";const f=new Date(this.availability.lastSeen),k=(new Date).getTime()-f.getTime(),$=Math.floor(k/6e4),j=Math.floor($/60),U=Math.floor(j/24);return $<1?"Just now":$<60?`${$} minute${$>1?"s":""} ago`:j<24?`${j} hour${j>1?"s":""} ago`:`${U} day${U>1?"s":""} ago`}isWithinChatHours(){if(!this.availability?.chatStartTime||!this.availability?.chatEndTime)return!0;const D=(new Date).toTimeString().slice(0,5);return D>=this.availability.chatStartTime&&D<=this.availability.chatEndTime}static{this.\u0275fac=function(D){return new(D||l)}}static{this.\u0275cmp=i.VBU({type:l,selectors:[["app-doctor-availability"]],inputs:{doctorId:"doctorId",showDetails:"showDetails",size:"size"},decls:5,vars:8,consts:[[1,"doctor-availability"],[1,"status-indicator"],["class","status-text ms-2",4,"ngIf"],["class","availability-details mt-2",4,"ngIf"],[1,"status-text","ms-2"],[1,"availability-details","mt-2"],["class","detail-item",4,"ngIf"],["class","availability-warning mt-2",4,"ngIf"],["class","status-message mt-2",4,"ngIf"],[1,"detail-item"],[1,"fas","fa-clock","text-muted","me-2"],[1,"text-muted"],[1,"fas","fa-calendar-clock","text-muted","me-2"],[1,"fas","fa-comment","text-muted","me-2"],[1,"fas","fa-eye","text-muted","me-2"],[1,"availability-warning","mt-2"],[1,"alert","alert-warning","py-1","px-2","mb-0"],[1,"fas","fa-exclamation-triangle","me-1"],[1,"status-message","mt-2"],[1,"alert","alert-info","py-1","px-2","mb-0"],[1,"fas","fa-user-clock","me-1"],[1,"alert","alert-danger","py-1","px-2","mb-0"],[1,"fas","fa-ban","me-1"]],template:function(D,k){1&D&&(i.j41(0,"div",0)(1,"div",1),i.nrm(2,"i"),i.DNE(3,w,2,1,"span",2),i.k0s(),i.DNE(4,A,8,7,"div",3),i.k0s()),2&D&&(i.HbH("size-"+k.size),i.R7$(1),i.HbH(k.getStatusClass()),i.R7$(1),i.HbH(k.getStatusIcon()),i.R7$(1),i.Y8G("ngIf","sm"!==k.size),i.R7$(1),i.Y8G("ngIf",k.showDetails&&k.availability&&"sm"!==k.size))},dependencies:[P.bT],styles:[".doctor-availability[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{display:flex;align-items:center}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-online[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#28a745}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-busy[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#ffc107}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-away[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#17a2b8}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-dnd[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#dc3545}.doctor-availability[_ngcontent-%COMP%]   .status-indicator.status-offline[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{color:#6c757d}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{display:flex;align-items:center;margin-bottom:.25rem}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:14px;font-size:.75rem}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]{font-size:.75rem;border-radius:4px}.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{font-size:.875rem}.doctor-availability.size-sm[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.75rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{font-size:1.125rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:1rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{font-weight:500}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]{margin-top:.75rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{margin-bottom:.5rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{width:16px;font-size:.875rem}.doctor-availability.size-lg[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{font-size:.875rem}.status-online[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 2s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}@media (max-width: 576px){.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.doctor-availability[_ngcontent-%COMP%]   .availability-details[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-bottom:.25rem}}"]})}}return l})()},271:(G,E,m)=>{m.d(E,{B:()=>R});var e=m(8359),i=m(6276),P=m(5538),w=m(177);function t(h,y){if(1&h){const C=i.RV6();i.j41(0,"button",3),i.bIt("click",function(){i.eBV(C);const b=i.XpG();return i.Njj(b.reconnect())}),i.nrm(1,"i",4),i.EFF(2),i.k0s()}if(2&h){const C=i.XpG();i.Y8G("disabled",C.isReconnecting),i.R7$(1),i.AVh("fa-spin",C.isReconnecting),i.R7$(1),i.SpI(" ",C.isReconnecting?"Connecting...":"Reconnect"," ")}}let R=(()=>{class h{constructor(C){this.chatService=C,this.isConnected=!1,this.isReconnecting=!1,this.statusText="Connecting...",this.statusClass="status-connecting",this.iconClass="fas fa-circle-notch fa-spin",this.subscription=new e.yU}ngOnInit(){this.subscription.add(this.chatService.connectionStatus$.subscribe(C=>{this.isConnected=C,this.updateStatus(C)}))}ngOnDestroy(){this.subscription.unsubscribe()}updateStatus(C){C?(this.statusText="Real-time chat connected",this.statusClass="status-connected",this.iconClass="fas fa-check-circle",this.isReconnecting=!1):(this.statusText="Chat disconnected - some features may not work",this.statusClass="status-disconnected",this.iconClass="fas fa-exclamation-circle")}reconnect(){this.isReconnecting=!0,this.statusText="Reconnecting...",this.statusClass="status-connecting",this.iconClass="fas fa-circle-notch fa-spin",this.chatService.disconnect(),setTimeout(()=>{this.chatService.forceConnect()},1e3)}static{this.\u0275fac=function(x){return new(x||h)(i.rXU(P.m))}}static{this.\u0275cmp=i.VBU({type:h,selectors:[["app-websocket-status"]],decls:5,vars:5,consts:[[1,"websocket-status",3,"ngClass"],[1,"status-text"],["class","btn btn-sm btn-outline-primary ms-2",3,"disabled","click",4,"ngIf"],[1,"btn","btn-sm","btn-outline-primary","ms-2",3,"disabled","click"],[1,"fas","fa-sync"]],template:function(x,b){1&x&&(i.j41(0,"div",0),i.nrm(1,"i"),i.j41(2,"span",1),i.EFF(3),i.k0s(),i.DNE(4,t,3,4,"button",2),i.k0s()),2&x&&(i.Y8G("ngClass",b.statusClass),i.R7$(1),i.HbH(b.iconClass),i.R7$(2),i.JRh(b.statusText),i.R7$(1),i.Y8G("ngIf",!b.isConnected))},dependencies:[w.YU,w.bT],styles:[".websocket-status[_ngcontent-%COMP%]{display:flex;align-items:center;padding:8px 12px;border-radius:6px;font-size:.875rem;margin-bottom:1rem;transition:all .3s ease}.status-connected[_ngcontent-%COMP%]{background-color:#d4edda;color:#155724;border:1px solid #c3e6cb}.status-disconnected[_ngcontent-%COMP%]{background-color:#f8d7da;color:#721c24;border:1px solid #f5c6cb}.status-connecting[_ngcontent-%COMP%]{background-color:#fff3cd;color:#856404;border:1px solid #ffeaa7}.status-text[_ngcontent-%COMP%]{margin-left:8px;font-weight:500}.fa-spin[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_fa-spin 1s infinite linear}@keyframes _ngcontent-%COMP%_fa-spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}"]})}}return h})()}}]);