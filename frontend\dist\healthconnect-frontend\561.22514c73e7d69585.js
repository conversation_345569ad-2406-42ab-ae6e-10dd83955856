"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[561],{9561:(P,l,r)=>{r.r(l),r.d(l,{DoctorModule:()=>L});var m=r(2434),p=r(3887),h=r(1713),t=r(6276),u=r(8010),b=r(3443),g=r(9545),v=r(5538),d=r(177),f=r(9339),_=r(628);const C=["chatWindow"];function D(n,a){1&n&&(t.j41(0,"div",17)(1,"div",18)(2,"span",19),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",20),t.EFF(5,"Loading your dashboard..."),t.k0s()())}function x(n,a){if(1&n&&(t.j41(0,"div",21),t.nrm(1,"i",22),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function y(n,a){if(1&n&&(t.j41(0,"span"),t.EFF(1),t.k0s()),2&n){const e=t.XpG(2);t.R7$(1),t.SpI(" \u2022 ",null==e.currentUser?null:e.currentUser.affiliation,"")}}function F(n,a){if(1&n&&(t.j41(0,"div",50)(1,"div",34)(2,"div",39)(3,"div",59)(4,"div",60),t.nrm(5,"i"),t.k0s(),t.j41(6,"div",61)(7,"h6",62),t.EFF(8),t.k0s(),t.j41(9,"h3",63),t.EFF(10),t.k0s(),t.j41(11,"small"),t.nrm(12,"i"),t.EFF(13),t.k0s()()()()()()),2&n){const e=a.$implicit,i=t.XpG(2);t.R7$(5),t.STu("bi bi-",e.icon," fs-2 ",e.color,""),t.R7$(3),t.JRh(e.title),t.R7$(2),t.JRh(e.value),t.R7$(1),t.HbH(i.getChangeClass(e.changeType)),t.R7$(1),t.HbH(i.getChangeIcon(e.changeType)),t.R7$(1),t.SpI(" ",e.change," ")}}function j(n,a){1&n&&(t.j41(0,"div",64),t.nrm(1,"i",65),t.j41(2,"p"),t.EFF(3,"No appointments scheduled for today"),t.k0s()())}function k(n,a){if(1&n&&(t.j41(0,"span"),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" \u2022 ",e.reasonForVisit,"")}}function E(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",66)(1,"div",59)(2,"div",60)(3,"div",67),t.nrm(4,"i"),t.k0s()(),t.j41(5,"div")(6,"h6",63),t.EFF(7),t.k0s(),t.j41(8,"p",68),t.nrm(9,"i",69),t.EFF(10),t.j41(11,"span",70)(12,"span"),t.EFF(13),t.nI1(14,"titlecase"),t.k0s()()(),t.j41(15,"small",71),t.EFF(16),t.DNE(17,k,2,1,"span",3),t.k0s()()(),t.j41(18,"div",72)(19,"button",73),t.bIt("click",function(){const s=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.navigateTo("/appointments/"+s.id))}),t.nrm(20,"i"),t.EFF(21),t.k0s()()()}if(2&n){const e=a.$implicit,i=t.XpG(2);t.R7$(4),t.STu("bi bi-",i.getAppointmentTypeIcon(e.type)," ",i.getAppointmentTypeClass(e.type),""),t.R7$(3),t.JRh(e.patient.fullName),t.R7$(3),t.Lme("",e.startTime," - ",e.endTime," "),t.R7$(2),t.HbH(i.getStatusBadgeClass(e.status)),t.R7$(1),t.SpI(" ",t.bMT(14,16,e.status)," "),t.R7$(3),t.SpI(" ","VIDEO_CALL"===e.type?"Video Consultation":"In-Person Visit"," "),t.R7$(1),t.Y8G("ngIf",e.reasonForVisit),t.R7$(3),t.ZvI("bi bi-","VIDEO_CALL"===e.type?"camera-video":"person"," me-1"),t.R7$(1),t.SpI(" ","VIDEO_CALL"===e.type?"Join Call":"View Details"," ")}}function T(n,a){1&n&&t.nrm(0,"hr",79)}function R(n,a){if(1&n&&(t.j41(0,"div",74)(1,"div",75)(2,"div",60)(3,"div",76),t.nrm(4,"i"),t.k0s()(),t.j41(5,"div",61)(6,"h6",77),t.EFF(7),t.k0s(),t.j41(8,"p",68),t.EFF(9),t.k0s(),t.j41(10,"small",71),t.EFF(11),t.k0s()()(),t.DNE(12,T,1,0,"hr",78),t.k0s()),2&n){const e=a.$implicit,i=a.last;t.R7$(4),t.STu("bi bi-",e.icon," ",e.color,""),t.R7$(3),t.JRh(e.title),t.R7$(2),t.JRh(e.description),t.R7$(2),t.JRh(e.time),t.R7$(1),t.Y8G("ngIf",!i)}}function I(n,a){1&n&&(t.j41(0,"div",64),t.nrm(1,"i",80),t.j41(2,"p"),t.EFF(3,"No messages yet"),t.k0s()())}function w(n,a){if(1&n&&(t.j41(0,"p",68),t.EFF(1),t.nI1(2,"slice"),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.Lme(" ",t.brH(2,2,e.lastMessage.content,0,50),"",e.lastMessage.content.length>50?"...":""," ")}}function M(n,a){if(1&n&&(t.j41(0,"small",71),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit,i=t.XpG(2);t.R7$(1),t.SpI(" ",i.formatChatTime(e.lastMessage.createdAt)," ")}}function $(n,a){if(1&n&&(t.j41(0,"span",87),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ",e.unreadCount," ")}}function S(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",81)(1,"div",59)(2,"div",60),t.nrm(3,"img",82),t.k0s(),t.j41(4,"div")(5,"h6",63),t.EFF(6),t.k0s(),t.DNE(7,w,3,6,"p",83),t.DNE(8,M,2,1,"small",84),t.k0s()(),t.j41(9,"div",72),t.DNE(10,$,2,1,"span",85),t.j41(11,"button",38),t.bIt("click",function(){const s=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.openChat(s))}),t.nrm(12,"i",86),t.EFF(13,"Reply "),t.k0s()()()}if(2&n){const e=a.$implicit;t.R7$(3),t.Y8G("src",e.patient.avatar||"/assets/images/default-avatar.png",t.B4B)("alt",e.patient.fullName),t.R7$(3),t.JRh(e.patient.fullName),t.R7$(1),t.Y8G("ngIf",e.lastMessage),t.R7$(1),t.Y8G("ngIf",e.lastMessage),t.R7$(2),t.Y8G("ngIf",e.unreadCount>0)}}function O(n,a){if(1&n){const e=t.RV6();t.j41(0,"div")(1,"div",23)(2,"div",24)(3,"div",25)(4,"div")(5,"h1",26),t.EFF(6),t.k0s(),t.j41(7,"p",27),t.nrm(8,"i",28),t.EFF(9),t.DNE(10,y,2,1,"span",3),t.k0s()(),t.j41(11,"button",29),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.refreshData())}),t.nrm(12,"i",30),t.EFF(13,"Refresh "),t.k0s()()()(),t.j41(14,"div",23),t.DNE(15,F,14,11,"div",31),t.k0s(),t.j41(16,"div",32)(17,"div",33)(18,"div",34)(19,"div",35)(20,"h6",36),t.nrm(21,"i",37),t.EFF(22,"Today's Schedule "),t.k0s(),t.j41(23,"button",38),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.navigateTo("/appointments"))}),t.EFF(24," View All "),t.k0s()(),t.j41(25,"div",39),t.DNE(26,j,4,0,"div",40),t.DNE(27,E,22,18,"div",41),t.k0s()()(),t.j41(28,"div",42)(29,"div",34)(30,"div",43)(31,"h6",36),t.nrm(32,"i",44),t.EFF(33,"Recent Activities "),t.k0s()(),t.j41(34,"div",39),t.DNE(35,R,13,8,"div",45),t.k0s()()()(),t.j41(36,"div",23)(37,"div",24)(38,"div",46)(39,"div",35)(40,"h6",36),t.nrm(41,"i",9),t.EFF(42,"Recent Messages "),t.k0s(),t.j41(43,"button",38),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.openChatModal())}),t.nrm(44,"i",47),t.EFF(45,"View All Messages "),t.k0s()(),t.j41(46,"div",39),t.DNE(47,I,4,0,"div",40),t.DNE(48,S,14,6,"div",48),t.k0s()()()(),t.j41(49,"div",32)(50,"div",24)(51,"div",46)(52,"div",43)(53,"h6",36),t.nrm(54,"i",49),t.EFF(55,"Quick Actions "),t.k0s()(),t.j41(56,"div",39)(57,"div",32)(58,"div",50)(59,"button",51),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.navigateTo("/patients"))}),t.nrm(60,"i",52),t.j41(61,"span"),t.EFF(62,"Manage Patients"),t.k0s()()(),t.j41(63,"div",50)(64,"button",53),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.navigateTo("/appointments"))}),t.nrm(65,"i",54),t.j41(66,"span"),t.EFF(67,"Schedule Appointment"),t.k0s()()(),t.j41(68,"div",50)(69,"button",55),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.navigateTo("/chat"))}),t.nrm(70,"i",56),t.j41(71,"span"),t.EFF(72,"Messages"),t.k0s()()(),t.j41(73,"div",50)(74,"button",57),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.navigateTo("/reports"))}),t.nrm(75,"i",58),t.j41(76,"span"),t.EFF(77,"View Reports"),t.k0s()()()()()()()()()}if(2&n){const e=t.XpG();t.R7$(6),t.Lme("",e.getGreeting(),", Dr. ",null==e.currentUser?null:e.currentUser.fullName,"!"),t.R7$(3),t.SpI("",(null==e.currentUser?null:e.currentUser.specialization)||"General Practice"," "),t.R7$(1),t.Y8G("ngIf",null==e.currentUser?null:e.currentUser.affiliation),t.R7$(5),t.Y8G("ngForOf",e.dashboardStats),t.R7$(11),t.Y8G("ngIf",0===e.realTodayAppointments.length),t.R7$(1),t.Y8G("ngForOf",e.realTodayAppointments),t.R7$(8),t.Y8G("ngForOf",e.recentActivities),t.R7$(12),t.Y8G("ngIf",0===e.recentChats.length),t.R7$(1),t.Y8G("ngForOf",e.recentChats)}}const G=[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",component:(()=>{class n{constructor(e,i,o,s,c){this.authService=e,this.userService=i,this.appointmentService=o,this.chatService=s,this.router=c,this.currentUser=null,this.isLoading=!0,this.error="",this.realTodayAppointments=[],this.recentChats=[],this.dashboardStats=[{title:"Total Patients",value:"156",change:"+12%",changeType:"increase",icon:"people",color:"text-primary"},{title:"Today's Appointments",value:"8",change:"+2",changeType:"increase",icon:"calendar-check",color:"text-success"},{title:"Pending Reviews",value:"5",change:"-3",changeType:"decrease",icon:"clipboard-check",color:"text-warning"},{title:"Messages",value:"12",change:"+4",changeType:"increase",icon:"chat-dots",color:"text-info"}],this.todayAppointments=[{id:1,patientName:"John Smith",time:"09:00 AM",type:"VIDEO_CALL",status:"SCHEDULED"},{id:2,patientName:"Sarah Johnson",time:"10:30 AM",type:"IN_PERSON",status:"SCHEDULED"},{id:3,patientName:"Michael Brown",time:"02:00 PM",type:"VIDEO_CALL",status:"SCHEDULED"},{id:4,patientName:"Emily Davis",time:"03:30 PM",type:"IN_PERSON",status:"SCHEDULED"}],this.recentActivities=[{title:"New Patient Registration",description:"Alice Wilson registered as a new patient",time:"30 minutes ago",icon:"person-plus",color:"text-success"},{title:"Appointment Rescheduled",description:"Tom Anderson moved appointment to tomorrow",time:"1 hour ago",icon:"calendar-event",color:"text-warning"},{title:"Message Received",description:"New message from Lisa Parker about medication",time:"2 hours ago",icon:"envelope",color:"text-info"},{title:"Lab Results Available",description:"Blood test results for David Miller are ready",time:"3 hours ago",icon:"file-medical",color:"text-primary"}]}ngOnInit(){this.loadUserData(),this.loadTodayAppointments(),this.loadRecentChats()}loadUserData(){this.authService.currentUser$.subscribe({next:e=>{this.currentUser=e,this.isLoading=!1},error:e=>{this.error="Failed to load user data",this.isLoading=!1}})}loadTodayAppointments(){this.appointmentService.getTodayAppointments().subscribe({next:e=>{this.realTodayAppointments=e,this.updateDashboardStats(e.length)},error:e=>{console.error("Failed to load today appointments:",e)}})}updateDashboardStats(e){const i=this.dashboardStats.findIndex(o=>"Today's Appointments"===o.title);-1!==i&&(this.dashboardStats[i].value=e.toString())}getGreeting(){const e=(new Date).getHours();return e<12?"Good morning":e<18?"Good afternoon":"Good evening"}getChangeClass(e){switch(e){case"increase":return"text-success";case"decrease":return"text-danger";default:return"text-muted"}}getChangeIcon(e){switch(e){case"increase":return"bi-arrow-up";case"decrease":return"bi-arrow-down";default:return"bi-dash"}}getAppointmentTypeIcon(e){return"VIDEO_CALL"===e?"camera-video":"geo-alt"}getAppointmentTypeClass(e){return"VIDEO_CALL"===e?"text-primary":"text-success"}getStatusBadgeClass(e){switch(e){case"SCHEDULED":return"badge bg-primary";case"COMPLETED":return"badge bg-success";case"CANCELLED":return"badge bg-danger";default:return"badge bg-secondary"}}startAppointment(e){"VIDEO_CALL"===e.type?this.router.navigate(["/video-call"],{queryParams:{appointmentId:e.id}}):this.router.navigate(["/appointments",e.id])}navigateTo(e){this.router.navigate([e])}refreshData(){this.isLoading=!0,this.loadTodayAppointments(),this.loadRecentChats(),setTimeout(()=>{this.isLoading=!1},1e3)}loadRecentChats(){this.chatService.getUserChats().subscribe({next:e=>{this.recentChats=e.slice(0,3)},error:e=>{console.error("Failed to load chats:",e)}})}openChatModal(){const e=document.getElementById("chatModal");e&&new window.bootstrap.Modal(e).show()}openChat(e){this.openChatModal(),setTimeout(()=>{this.onChatSelected(e)},300)}onChatSelected(e){this.chatWindow&&this.chatWindow.loadChat(e)}formatChatTime(e){const i=new Date(e),s=((new Date).getTime()-i.getTime())/36e5;return s<1?"Just now":s<24?i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):i.toLocaleDateString()}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(u.u),t.rXU(b.D),t.rXU(g.h),t.rXU(v.m),t.rXU(m.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-doctor-dashboard"]],viewQuery:function(i,o){if(1&i&&t.GBs(C,5),2&i){let s;t.mGM(s=t.lsd())&&(o.chatWindow=s.first)}},decls:19,vars:3,consts:[[1,"container-fluid","py-4"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],[4,"ngIf"],["id","chatModal","tabindex","-1","aria-labelledby","chatModalLabel","aria-hidden","true",1,"modal","fade"],[1,"modal-dialog","modal-xl"],[1,"modal-content"],[1,"modal-header"],["id","chatModalLabel",1,"modal-title"],[1,"bi","bi-chat-dots","me-2"],["type","button","data-bs-dismiss","modal","aria-label","Close",1,"btn-close"],[1,"modal-body","p-0",2,"height","600px"],[1,"row","h-100","g-0"],[1,"col-md-4","border-end"],[3,"chatSelected"],[1,"col-md-8"],["chatWindow",""],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3","text-muted"],["role","alert",1,"alert","alert-danger"],[1,"bi","bi-exclamation-triangle","me-2"],[1,"row","mb-4"],[1,"col-12"],[1,"d-flex","justify-content-between","align-items-center"],[1,"h3","mb-1"],[1,"text-muted","mb-0"],[1,"bi","bi-hospital","me-2"],[1,"btn","btn-outline-primary",3,"click"],[1,"bi","bi-arrow-clockwise","me-2"],["class","col-md-3 col-sm-6 mb-3",4,"ngFor","ngForOf"],[1,"row"],[1,"col-md-8","mb-4"],[1,"card","h-100"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"bi","bi-calendar-day","me-2"],[1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"card-body"],["class","text-center py-4 text-muted",4,"ngIf"],["class","appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border",4,"ngFor","ngForOf"],[1,"col-md-4","mb-4"],[1,"card-header"],[1,"bi","bi-activity","me-2"],["class","activity-item",4,"ngFor","ngForOf"],[1,"card"],[1,"bi","bi-chat-plus","me-1"],["class","chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border",4,"ngFor","ngForOf"],[1,"bi","bi-lightning","me-2"],[1,"col-md-3","col-sm-6","mb-3"],[1,"btn","btn-outline-primary","w-100","py-3",3,"click"],[1,"bi","bi-people","d-block","fs-4","mb-2"],[1,"btn","btn-outline-success","w-100","py-3",3,"click"],[1,"bi","bi-calendar-plus","d-block","fs-4","mb-2"],[1,"btn","btn-outline-info","w-100","py-3",3,"click"],[1,"bi","bi-chat-dots","d-block","fs-4","mb-2"],[1,"btn","btn-outline-warning","w-100","py-3",3,"click"],[1,"bi","bi-graph-up","d-block","fs-4","mb-2"],[1,"d-flex","align-items-center"],[1,"flex-shrink-0","me-3"],[1,"flex-grow-1"],[1,"card-title","text-muted","mb-1"],[1,"mb-1"],[1,"text-center","py-4","text-muted"],[1,"bi","bi-calendar-x","display-6","mb-3"],[1,"appointment-item","d-flex","align-items-center","justify-content-between","p-3","mb-2","rounded","border"],[1,"rounded-circle","bg-light","d-flex","align-items-center","justify-content-center",2,"width","45px","height","45px"],[1,"mb-1","text-muted","small"],[1,"bi","bi-clock","me-1"],[1,"ms-2"],[1,"text-muted"],[1,"flex-shrink-0"],[1,"btn","btn-sm","btn-primary",3,"click"],[1,"activity-item"],[1,"d-flex","align-items-start"],[1,"rounded-circle","bg-light","d-flex","align-items-center","justify-content-center",2,"width","35px","height","35px"],[1,"mb-1","small"],["class","my-3",4,"ngIf"],[1,"my-3"],[1,"bi","bi-chat-square-text","display-6","mb-3"],[1,"chat-preview","d-flex","align-items-center","justify-content-between","p-3","mb-2","rounded","border"],[1,"rounded-circle",2,"width","45px","height","45px","object-fit","cover",3,"src","alt"],["class","mb-1 text-muted small",4,"ngIf"],["class","text-muted",4,"ngIf"],["class","badge bg-primary rounded-pill me-2",4,"ngIf"],[1,"bi","bi-chat","me-1"],[1,"badge","bg-primary","rounded-pill","me-2"]],template:function(i,o){1&i&&(t.j41(0,"div",0),t.DNE(1,D,6,0,"div",1),t.DNE(2,x,3,1,"div",2),t.DNE(3,O,78,10,"div",3),t.k0s(),t.j41(4,"div",4)(5,"div",5)(6,"div",6)(7,"div",7)(8,"h5",8),t.nrm(9,"i",9),t.EFF(10,"Messages "),t.k0s(),t.nrm(11,"button",10),t.k0s(),t.j41(12,"div",11)(13,"div",12)(14,"div",13)(15,"app-chat-list",14),t.bIt("chatSelected",function(c){return o.onChatSelected(c)}),t.k0s()(),t.j41(16,"div",15),t.nrm(17,"app-chat-window",null,16),t.k0s()()()()()()),2&i&&(t.R7$(1),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error&&!o.isLoading),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error))},dependencies:[d.Sq,d.bT,f.q,_.E,d.P9,d.PV],styles:[".appointment-item[_ngcontent-%COMP%]{background-color:#f8f9fa;border:1px solid #e9ecef;transition:all .3s ease}.appointment-item[_ngcontent-%COMP%]:hover{background-color:#e9ecef;border-color:#dee2e6}.activity-item[_ngcontent-%COMP%]{padding-bottom:1rem}.activity-item[_ngcontent-%COMP%]:last-child{padding-bottom:0}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 2px 4px #0000001a}.card-header[_ngcontent-%COMP%]{background-color:transparent;border-bottom:1px solid #e9ecef;font-weight:600}.btn-outline-primary[_ngcontent-%COMP%], .btn-outline-success[_ngcontent-%COMP%], .btn-outline-info[_ngcontent-%COMP%], .btn-outline-warning[_ngcontent-%COMP%]{border-width:2px;font-weight:500;transition:all .3s ease}.btn-outline-primary[_ngcontent-%COMP%]:hover, .btn-outline-success[_ngcontent-%COMP%]:hover, .btn-outline-info[_ngcontent-%COMP%]:hover, .btn-outline-warning[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.badge[_ngcontent-%COMP%]{font-size:.7rem;padding:.25rem .5rem}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.text-success[_ngcontent-%COMP%]{color:#198754!important}.text-warning[_ngcontent-%COMP%]{color:#ffc107!important}.text-info[_ngcontent-%COMP%]{color:#0dcaf0!important}.text-danger[_ngcontent-%COMP%]{color:#dc3545!important}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.card-body[_ngcontent-%COMP%]{padding:1rem}.appointment-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start!important}.appointment-item[_ngcontent-%COMP%]   .flex-shrink-0[_ngcontent-%COMP%]:last-child{margin-top:1rem;align-self:stretch}.appointment-item[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{width:100%}}"]})}}return n})()}];let L=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[p.G,h.ChatModule,m.iI.forChild(G)]})}}return n})()}}]);