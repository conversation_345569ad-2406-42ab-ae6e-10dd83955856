"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[792],{4978:(qe,ye,O)=>{O.d(ye,{q:()=>se});var c=O(6276),C=O(8010),ce=O(2434);let se=(()=>{class X{constructor(ee,de){this.authService=ee,this.router=de}canActivate(ee,de){if(this.authService.isAuthenticated()){const le=ee.data.roles;if(le&&le.length>0&&!this.authService.hasRole(le)){const H=this.authService.getCurrentUser();return this.router.navigate("DOCTOR"===H?.role?["/doctor/dashboard"]:"PATIENT"===H?.role?["/patient/dashboard"]:["/auth/login"]),!1}return!0}return this.router.navigate(["/auth/login"],{queryParams:{returnUrl:de.url}}),!1}static{this.\u0275fac=function(de){return new(de||X)(c.KVO(C.u),c.KVO(ce.Ix))}}static{this.\u0275prov=c.jDH({token:X,factory:X.\u0275fac,providedIn:"root"})}}return X})()},8010:(qe,ye,O)=>{O.d(ye,{u:()=>le});var c=O(4412),C=O(8141),ce=O(9437),se=O(8810),X=O(5312),ne=O(6276),ee=O(1626),de=O(2434);let le=(()=>{class fe{constructor(G,ve){this.http=G,this.router=ve,this.API_URL=X.c.apiUrl+"/auth",this.currentUserSubject=new c.t(null),this.currentUser$=this.currentUserSubject.asObservable(),this.loadUserFromStorage()}loadUserFromStorage(){const G=this.getToken(),ve=localStorage.getItem("currentUser");if(G&&ve&&!this.isTokenExpired(G))try{const _e=JSON.parse(ve);this.currentUserSubject.next(_e)}catch(_e){console.error("Error parsing stored user data:",_e),this.clearAuthData()}else this.clearAuthData()}clearAuthData(){localStorage.removeItem("token"),localStorage.removeItem("currentUser"),this.currentUserSubject.next(null)}register(G){return this.http.post(`${this.API_URL}/register`,G).pipe((0,C.M)(ve=>{ve.token&&this.setAuthData(ve)}),(0,ce.W)(this.handleError))}login(G){return this.http.post(`${this.API_URL}/login`,G).pipe((0,C.M)(ve=>{ve.token&&this.setAuthData(ve)}),(0,ce.W)(this.handleError))}logout(){this.clearAuthData(),this.router.url.includes("/auth")||this.router.navigate(["/auth/login"])}setAuthData(G){localStorage.setItem("token",G.token);const ve={id:G.id,fullName:G.fullName,email:G.email,role:G.role,avatar:G.avatar,specialization:G.specialization,licenseNumber:G.licenseNumber,affiliation:G.affiliation,yearsOfExperience:G.yearsOfExperience,phoneNumber:G.phoneNumber,address:G.address,createdAt:G.createdAt,updatedAt:G.updatedAt};localStorage.setItem("currentUser",JSON.stringify(ve)),this.currentUserSubject.next(ve)}getToken(){return localStorage.getItem("token")}getCurrentUser(){return this.currentUserSubject.value}isAuthenticated(){const G=this.getToken();return!!G&&!this.isTokenExpired(G)}hasRole(G){const ve=this.getCurrentUser();return!!ve&&G.includes(ve.role)}isTokenExpired(G){try{const ve=JSON.parse(atob(G.split(".")[1])),_e=Math.floor(Date.now()/1e3);return ve.exp<_e}catch{return!0}}handleError(G){let ve="An error occurred";return G.error?"string"==typeof G.error?ve=G.error:G.error.message&&(ve=G.error.message):G.message&&(ve=G.message),(0,se.$)(()=>new Error(ve))}static{this.\u0275fac=function(ve){return new(ve||fe)(ne.KVO(ee.Qq),ne.KVO(de.Ix))}}static{this.\u0275prov=ne.jDH({token:fe,factory:fe.\u0275fac,providedIn:"root"})}}return fe})()},7436:(qe,ye,O)=>{O.d(ye,{s:()=>de});var c=O(4412),C=O(7673),ce=O(9437),se=O(6354),X=O(5312),ne=O(6276),ee=O(1626);let de=(()=>{class le{constructor(H){this.http=H,this.apiUrl=`${X.c.apiUrl}/api/i18n`,this.currentLanguage$=new c.t("en"),this.translations$=new c.t({}),this.supportedLanguages=[{code:"en",name:"English",flag:"\u{1f1fa}\u{1f1f8}"},{code:"es",name:"Espa\xf1ol",flag:"\u{1f1ea}\u{1f1f8}"},{code:"fr",name:"Fran\xe7ais",flag:"\u{1f1eb}\u{1f1f7}"},{code:"de",name:"Deutsch",flag:"\u{1f1e9}\u{1f1ea}"},{code:"pt",name:"Portugu\xeas",flag:"\u{1f1f5}\u{1f1f9}"}],this.initializeLanguage()}initializeLanguage(){const H=localStorage.getItem("healthconnect_language"),G=navigator.language.split("-")[0],ve=H||this.supportedLanguages.find(_e=>_e.code===G)?.code||"en";this.setLanguage(ve)}getCurrentLanguage(){return this.currentLanguage$.asObservable()}getCurrentLanguageValue(){return this.currentLanguage$.value}getTranslations(){return this.translations$.asObservable()}getSupportedLanguages(){return this.supportedLanguages}setLanguage(H){this.supportedLanguages.find(G=>G.code===H)&&(this.currentLanguage$.next(H),localStorage.setItem("healthconnect_language",H),this.loadTranslations(H))}loadTranslations(H){this.getTranslationsFromServer(H).subscribe({next:G=>{this.translations$.next(G.translations)},error:G=>{console.error("Failed to load translations:",G),this.loadDefaultTranslations()}})}getTranslationsFromServer(H){return this.http.get(`${this.apiUrl}/translations/${H}`).pipe((0,ce.W)(G=>(console.error("Error fetching translations from server:",G),(0,C.of)(this.getDefaultTranslations(H)))))}translate(H,G){return this.translations$.pipe((0,se.T)(ve=>{let _e=ve[H]||H;return G&&Object.keys(G).forEach(Ve=>{_e=_e.replace(`{${Ve}}`,G[Ve])}),_e}))}translateSync(H,G){let _e=this.translations$.value[H]||H;return G&&Object.keys(G).forEach(Ve=>{_e=_e.replace(`{${Ve}}`,G[Ve])}),_e}translateBatch(H){return this.http.post(`${this.apiUrl}/translate/batch`,{keys:H,language:this.getCurrentLanguageValue()}).pipe((0,se.T)(G=>G.translations),(0,ce.W)(G=>{console.error("Error in batch translation:",G);const ve=this.translations$.value,_e={};return H.forEach(Ve=>{_e[Ve]=ve[Ve]||Ve}),(0,C.of)(_e)}))}detectLanguage(H){return this.http.post(`${this.apiUrl}/detect-language`,{text:H}).pipe((0,ce.W)(G=>(console.error("Error detecting language:",G),(0,C.of)({detectedLanguage:"en",confidence:.5}))))}formatMessage(H,G){return this.http.post(`${this.apiUrl}/format-message`,{messageKey:H,language:this.getCurrentLanguageValue(),parameters:G}).pipe((0,se.T)(ve=>ve.formattedMessage),(0,ce.W)(ve=>(console.error("Error formatting message:",ve),this.translate(H))))}loadDefaultTranslations(){const H=this.getDefaultTranslations(this.getCurrentLanguageValue());this.translations$.next(H.translations)}getDefaultTranslations(H){const G={en:{welcome:"Welcome to HealthConnect",login:"Login",logout:"Logout",register:"Register",dashboard:"Dashboard",appointments:"Appointments",prescriptions:"Prescriptions",chat:"Chat",video_consultation:"Video Consultation",profile:"Profile",settings:"Settings",doctor:"Doctor",patient:"Patient",success:"Success",error:"Error",cancel:"Cancel",save:"Save",delete:"Delete",edit:"Edit",view:"View",search:"Search",loading:"Loading...",no_data:"No data available",confirm:"Confirm",yes:"Yes",no:"No"},es:{welcome:"Bienvenido a HealthConnect",login:"Iniciar Sesi\xf3n",logout:"Cerrar Sesi\xf3n",register:"Registrarse",dashboard:"Panel de Control",appointments:"Citas",prescriptions:"Recetas",chat:"Chat",video_consultation:"Consulta por Video",profile:"Perfil",settings:"Configuraci\xf3n",doctor:"Doctor",patient:"Paciente",success:"\xc9xito",error:"Error",cancel:"Cancelar",save:"Guardar",delete:"Eliminar",edit:"Editar",view:"Ver",search:"Buscar",loading:"Cargando...",no_data:"No hay datos disponibles",confirm:"Confirmar",yes:"S\xed",no:"No"},fr:{welcome:"Bienvenue \xe0 HealthConnect",login:"Connexion",logout:"D\xe9connexion",register:"S'inscrire",dashboard:"Tableau de Bord",appointments:"Rendez-vous",prescriptions:"Ordonnances",chat:"Chat",video_consultation:"Consultation Vid\xe9o",profile:"Profil",settings:"Param\xe8tres",doctor:"Docteur",patient:"Patient",success:"Succ\xe8s",error:"Erreur",cancel:"Annuler",save:"Sauvegarder",delete:"Supprimer",edit:"Modifier",view:"Voir",search:"Rechercher",loading:"Chargement...",no_data:"Aucune donn\xe9e disponible",confirm:"Confirmer",yes:"Oui",no:"Non"}};return{language:H,translations:G[H]||G.en,count:Object.keys(G[H]||G.en).length}}getLanguageName(H){const G=this.supportedLanguages.find(ve=>ve.code===H);return G?G.name:H}getLanguageFlag(H){return this.supportedLanguages.find(ve=>ve.code===H)?.flag||"\u{1f310}"}static{this.\u0275fac=function(G){return new(G||le)(ne.KVO(ee.Qq))}}static{this.\u0275prov=ne.jDH({token:le,factory:le.\u0275fac,providedIn:"root"})}}return le})()},5567:(qe,ye,O)=>{O.d(ye,{J:()=>se});var c=O(467),C=O(4412),ce=O(6276);let se=(()=>{class X{constructor(){this.notifications$=new C.t([]),this.unreadCount$=new C.t(0),this.loadNotifications()}getNotifications(){return this.notifications$.asObservable()}getUnreadCount(){return this.unreadCount$.asObservable()}addNotification(ee){const de={...ee,id:this.generateId(),timestamp:new Date,read:!1},fe=[de,...this.notifications$.value];this.notifications$.next(fe),this.updateUnreadCount(),this.saveNotifications(fe),this.showBrowserNotification(de)}markAsRead(ee){const de=this.notifications$.value.map(le=>le.id===ee?{...le,read:!0}:le);this.notifications$.next(de),this.updateUnreadCount(),this.saveNotifications(de)}markAllAsRead(){const ee=this.notifications$.value.map(de=>({...de,read:!0}));this.notifications$.next(ee),this.updateUnreadCount(),this.saveNotifications(ee)}removeNotification(ee){const de=this.notifications$.value.filter(le=>le.id!==ee);this.notifications$.next(de),this.updateUnreadCount(),this.saveNotifications(de)}clearAll(){this.notifications$.next([]),this.unreadCount$.next(0),this.saveNotifications([])}addMessageNotification(ee,de,le){this.addNotification({type:"message",title:`New message from ${ee.fullName}`,message:de.length>100?de.substring(0,100)+"...":de,priority:"medium",fromUser:{id:ee.id,name:ee.fullName,avatar:ee.avatar},actionUrl:`/chat?chatId=${le}`,actionText:"Reply"})}addAppointmentNotification(ee,de){let le="",fe="",H="medium";switch(ee){case"booked":le="Appointment Booked",fe=`Your appointment with ${de.doctor.fullName} is scheduled for ${de.date}`;break;case"reminder":le="Appointment Reminder",fe=`Your appointment with ${de.doctor.fullName} is in 1 hour`,H="high";break;case"cancelled":le="Appointment Cancelled",fe=`Your appointment with ${de.doctor.fullName} has been cancelled`,H="high"}this.addNotification({type:"appointment",title:le,message:fe,priority:H,actionUrl:`/appointments/${de.id}`,actionText:"View Details"})}addUrgentNotification(ee,de,le){this.addNotification({type:"urgent",title:ee,message:de,priority:"urgent",actionUrl:le,actionText:le?"View":void 0})}loadNotifications(){const ee=localStorage.getItem("healthconnect_notifications");if(ee)try{const de=JSON.parse(ee).map(le=>({...le,timestamp:new Date(le.timestamp)}));this.notifications$.next(de),this.updateUnreadCount()}catch(de){console.error("Error loading notifications:",de)}}saveNotifications(ee){try{localStorage.setItem("healthconnect_notifications",JSON.stringify(ee))}catch(de){console.error("Error saving notifications:",de)}}updateUnreadCount(){const ee=this.notifications$.value.filter(de=>!de.read).length;this.unreadCount$.next(ee)}generateId(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}showBrowserNotification(ee){var de=this;return(0,c.A)(function*(){"Notification"in window&&("granted"===Notification.permission?new Notification(ee.title,{body:ee.message,icon:"/assets/icons/icon-192x192.png",badge:"/assets/icons/icon-72x72.png",tag:ee.id}):"denied"!==Notification.permission&&"granted"===(yield Notification.requestPermission())&&de.showBrowserNotification(ee))})()}requestNotificationPermission(){return(0,c.A)(function*(){return"Notification"in window&&("granted"===Notification.permission||"denied"!==Notification.permission&&"granted"===(yield Notification.requestPermission()))})()}static{this.\u0275fac=function(de){return new(de||X)}}static{this.\u0275prov=ce.jDH({token:X,factory:X.\u0275fac,providedIn:"root"})}}return X})()},3443:(qe,ye,O)=>{O.d(ye,{D:()=>de});var c=O(1626),C=O(9437),ce=O(8141),se=O(8810),X=O(5312),ne=O(6276),ee=O(8010);let de=(()=>{class le{constructor(H,G){this.http=H,this.authService=G,this.API_URL=X.c.apiUrl+"/users"}getHeaders(){const H=this.authService.getToken();return new c.Lr({"Content-Type":"application/json",Authorization:`Bearer ${H}`})}getCurrentUserProfile(){return this.http.get(`${this.API_URL}/me`,{headers:this.getHeaders()}).pipe((0,C.W)(this.handleError))}updateProfile(H){return this.http.put(`${this.API_URL}/me`,H,{headers:this.getHeaders()}).pipe((0,ce.M)(G=>{const ve=this.authService.getCurrentUser();if(ve){const _e={...ve,...G};localStorage.setItem("currentUser",JSON.stringify(_e))}}),(0,C.W)(this.handleError))}getUserById(H){return this.http.get(`${this.API_URL}/${H}`,{headers:this.getHeaders()}).pipe((0,C.W)(this.handleError))}getAllDoctors(){return this.http.get(`${this.API_URL}/doctors`,{headers:this.getHeaders()}).pipe((0,C.W)(this.handleError))}searchDoctors(H){let G=new c.Nl;return H&&(G=G.set("specialization",H)),this.http.get(`${this.API_URL}/doctors`,{headers:this.getHeaders(),params:G}).pipe((0,C.W)(this.handleError))}getDoctorById(H){return this.http.get(`${this.API_URL}/doctors/${H}`,{headers:this.getHeaders()}).pipe((0,C.W)(this.handleError))}getSpecializations(){return this.http.get(`${this.API_URL}/doctors/specializations`,{headers:this.getHeaders()}).pipe((0,C.W)(this.handleError))}handleError(H){let G="An error occurred";return H.error?"string"==typeof H.error?G=H.error:H.error.message&&(G=H.error.message):H.message&&(G=H.message),(0,se.$)(()=>new Error(G))}static{this.\u0275fac=function(G){return new(G||le)(ne.KVO(c.Qq),ne.KVO(ee.u))}}static{this.\u0275prov=ne.jDH({token:le,factory:le.\u0275fac,providedIn:"root"})}}return le})()},3887:(qe,ye,O)=>{O.d(ye,{G:()=>ne});var c=O(177),C=O(4341),ce=O(2434),se=O(1626),X=O(6276);let ne=(()=>{class ee{static{this.\u0275fac=function(fe){return new(fe||ee)}}static{this.\u0275mod=X.$C({type:ee})}static{this.\u0275inj=X.G2t({imports:[c.MD,C.X1,C.YN,ce.iI,se.q1,c.MD,C.X1,C.YN,ce.iI,se.q1]})}}return ee})()},5312:(qe,ye,O)=>{O.d(ye,{c:()=>c});const c={production:!1,apiUrl:"http://localhost:8080/api",wsUrl:"http://localhost:8080/api/ws",appName:"HealthConnect",version:"1.0.0",websocket:{url:"http://localhost:8080/api/ws",reconnectInterval:3e3,maxReconnectAttempts:5,heartbeatIncoming:25e3,heartbeatOutgoing:25e3}}},1413:(qe,ye,O)=>{var c=O(345),C=O(6276);class ce{}class se{}const X="*";function le(_,a=null){return{type:2,steps:_,options:a}}function fe(_){return{type:6,styles:_,offset:null}}class Rt{constructor(a=0,l=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=a+l}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}onStart(a){this._originalOnStartFns.push(a),this._onStartFns.push(a)}onDone(a){this._originalOnDoneFns.push(a),this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(a=>a()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(a){this._position=this.totalTime?a*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(a){const l="start"==a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}class yt{constructor(a){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=a;let l=0,y=0,M=0;const R=this.players.length;0==R?queueMicrotask(()=>this._onFinish()):this.players.forEach(L=>{L.onDone(()=>{++l==R&&this._onFinish()}),L.onDestroy(()=>{++y==R&&this._onDestroy()}),L.onStart(()=>{++M==R&&this._onStart()})}),this.totalTime=this.players.reduce((L,U)=>Math.max(L,U.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}init(){this.players.forEach(a=>a.init())}onStart(a){this._onStartFns.push(a)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(a=>a()),this._onStartFns=[])}onDone(a){this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(a=>a.play())}pause(){this.players.forEach(a=>a.pause())}restart(){this.players.forEach(a=>a.restart())}finish(){this._onFinish(),this.players.forEach(a=>a.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(a=>a.destroy()),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}reset(){this.players.forEach(a=>a.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(a){const l=a*this.totalTime;this.players.forEach(y=>{const M=y.totalTime?Math.min(1,l/y.totalTime):1;y.setPosition(M)})}getPosition(){const a=this.players.reduce((l,y)=>null===l||y.totalTime>l.totalTime?y:l,null);return null!=a?a.getPosition():0}beforeDestroy(){this.players.forEach(a=>{a.beforeDestroy&&a.beforeDestroy()})}triggerCallback(a){const l="start"==a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}function Ke(_){return new C.wOt(3e3,!1)}function vt(_){switch(_.length){case 0:return new Rt;case 1:return _[0];default:return new yt(_)}}function ht(_,a,l=new Map,y=new Map){const M=[],R=[];let L=-1,U=null;if(a.forEach(J=>{const ge=J.get("offset"),Ue=ge==L,He=Ue&&U||new Map;J.forEach((Ft,wt)=>{let st=wt,at=Ft;if("offset"!==wt)switch(st=_.normalizePropertyName(st,M),at){case"!":at=l.get(wt);break;case X:at=y.get(wt);break;default:at=_.normalizeStyleValue(wt,st,at,M)}He.set(st,at)}),Ue||R.push(He),U=He,L=ge}),M.length)throw function ut(_){return new C.wOt(3502,!1)}();return R}function nr(_,a,l,y){switch(a){case"start":_.onStart(()=>y(l&&Fn(l,"start",_)));break;case"done":_.onDone(()=>y(l&&Fn(l,"done",_)));break;case"destroy":_.onDestroy(()=>y(l&&Fn(l,"destroy",_)))}}function Fn(_,a,l){const R=sn(_.element,_.triggerName,_.fromState,_.toState,a||_.phaseName,l.totalTime??_.totalTime,!!l.disabled),L=_._data;return null!=L&&(R._data=L),R}function sn(_,a,l,y,M="",R=0,L){return{element:_,triggerName:a,fromState:l,toState:y,phaseName:M,totalTime:R,disabled:!!L}}function an(_,a,l){let y=_.get(a);return y||_.set(a,y=l),y}function En(_){const a=_.indexOf(":");return[_.substring(1,a),_.slice(a+1)]}const Mt=(()=>typeof document>"u"?null:document.documentElement)();function Xt(_){const a=_.parentNode||_.host||null;return a===Mt?null:a}let pt=null,ti=!1;function _t(_,a){for(;a;){if(a===_)return!0;a=Xt(a)}return!1}function dt(_,a,l){if(l)return Array.from(_.querySelectorAll(a));const y=_.querySelector(a);return y?[y]:[]}let Kn=(()=>{class _{validateStyleProperty(l){return function hi(_){pt||(pt=function Ir(){return typeof document<"u"?document.body:null}()||{},ti=!!pt.style&&"WebkitAppearance"in pt.style);let a=!0;return pt.style&&!function Oi(_){return"ebkit"==_.substring(1,6)}(_)&&(a=_ in pt.style,!a&&ti&&(a="Webkit"+_.charAt(0).toUpperCase()+_.slice(1)in pt.style)),a}(l)}matchesElement(l,y){return!1}containsElement(l,y){return _t(l,y)}getParentElement(l){return Xt(l)}query(l,y,M){return dt(l,y,M)}computeStyle(l,y,M){return M||""}animate(l,y,M,R,L,U=[],J){return new Rt(M,R)}static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})(),Xn=(()=>{class _{static{this.NOOP=new Kn}}return _})();const rr=1e3,Lr="ng-enter",he="ng-leave",q="ng-trigger",V=".ng-trigger",W="ng-animating",pe=".ng-animating";function Fe(_){if("number"==typeof _)return _;const a=_.match(/^(-?[\.\d]+)(m?s)/);return!a||a.length<2?0:We(parseFloat(a[1]),a[2])}function We(_,a){return"s"===a?_*rr:_}function At(_,a,l){return _.hasOwnProperty("duration")?_:function $t(_,a,l){let M,R=0,L="";if("string"==typeof _){const U=_.match(/^(-?[\.\d]+)(m?s)(?:\s+(-?[\.\d]+)(m?s))?(?:\s+([-a-z]+(?:\(.+?\))?))?$/i);if(null===U)return a.push(Ke()),{duration:0,delay:0,easing:""};M=We(parseFloat(U[1]),U[2]);const J=U[3];null!=J&&(R=We(parseFloat(J),U[4]));const ge=U[5];ge&&(L=ge)}else M=_;if(!l){let U=!1,J=a.length;M<0&&(a.push(function we(){return new C.wOt(3100,!1)}()),U=!0),R<0&&(a.push(function ke(){return new C.wOt(3101,!1)}()),U=!0),U&&a.splice(J,0,Ke())}return{duration:M,delay:R,easing:L}}(_,a,l)}function ln(_,a={}){return Object.keys(_).forEach(l=>{a[l]=_[l]}),a}function Bn(_){const a=new Map;return Object.keys(_).forEach(l=>{a.set(l,_[l])}),a}function j(_,a=new Map,l){if(l)for(let[y,M]of l)a.set(y,M);for(let[y,M]of _)a.set(y,M);return a}function $(_,a,l){a.forEach((y,M)=>{const R=In(M);l&&!l.has(M)&&l.set(M,_.style[R]),_.style[R]=y})}function ue(_,a){a.forEach((l,y)=>{const M=In(y);_.style[M]=""})}function Oe(_){return Array.isArray(_)?1==_.length?_[0]:le(_):_}const Le=new RegExp("{{\\s*(.+?)\\s*}}","g");function Ut(_){let a=[];if("string"==typeof _){let l;for(;l=Le.exec(_);)a.push(l[1]);Le.lastIndex=0}return a}function tt(_,a,l){const y=_.toString(),M=y.replace(Le,(R,L)=>{let U=a[L];return null==U&&(l.push(function ot(_){return new C.wOt(3003,!1)}()),U=""),U.toString()});return M==y?_:M}function vn(_){const a=[];let l=_.next();for(;!l.done;)a.push(l.value),l=_.next();return a}const $n=/-+([a-z0-9])/g;function In(_){return _.replace($n,(...a)=>a[1].toUpperCase())}function ct(_,a,l){switch(a.type){case 7:return _.visitTrigger(a,l);case 0:return _.visitState(a,l);case 1:return _.visitTransition(a,l);case 2:return _.visitSequence(a,l);case 3:return _.visitGroup(a,l);case 4:return _.visitAnimate(a,l);case 5:return _.visitKeyframes(a,l);case 6:return _.visitStyle(a,l);case 8:return _.visitReference(a,l);case 9:return _.visitAnimateChild(a,l);case 10:return _.visitAnimateRef(a,l);case 11:return _.visitQuery(a,l);case 12:return _.visitStagger(a,l);default:throw function rt(_){return new C.wOt(3004,!1)}()}}function Je(_,a){return window.getComputedStyle(_)[a]}const Vr="*";function Ki(_,a){const l=[];return"string"==typeof _?_.split(/\s*,\s*/).forEach(y=>function ri(_,a,l){if(":"==_[0]){const J=function Xi(_,a){switch(_){case":enter":return"void => *";case":leave":return"* => void";case":increment":return(l,y)=>parseFloat(y)>parseFloat(l);case":decrement":return(l,y)=>parseFloat(y)<parseFloat(l);default:return a.push(function be(_){return new C.wOt(3016,!1)}()),"* => *"}}(_,l);if("function"==typeof J)return void a.push(J);_=J}const y=_.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);if(null==y||y.length<4)return l.push(function Ie(_){return new C.wOt(3015,!1)}()),a;const M=y[1],R=y[2],L=y[3];a.push(Ni(M,L));"<"==R[0]&&!(M==Vr&&L==Vr)&&a.push(Ni(L,M))}(y,l,a)):l.push(_),l}const qn=new Set(["true","1"]),mr=new Set(["false","0"]);function Ni(_,a){const l=qn.has(_)||mr.has(_),y=qn.has(a)||mr.has(a);return(M,R)=>{let L=_==Vr||_==M,U=a==Vr||a==R;return!L&&l&&"boolean"==typeof M&&(L=M?qn.has(_):mr.has(_)),!U&&y&&"boolean"==typeof R&&(U=R?qn.has(a):mr.has(a)),L&&U}}const Ar=new RegExp("s*:selfs*,?","g");function yr(_,a,l,y){return new jr(_).build(a,l,y)}class jr{constructor(a){this._driver=a}build(a,l,y){const M=new Zi(l);return this._resetContextStyleTimingState(M),ct(this,Oe(a),M)}_resetContextStyleTimingState(a){a.currentQuerySelector="",a.collectedStyles=new Map,a.collectedStyles.set("",new Map),a.currentTime=0}visitTrigger(a,l){let y=l.queryCount=0,M=l.depCount=0;const R=[],L=[];return"@"==a.name.charAt(0)&&l.errors.push(function cn(){return new C.wOt(3006,!1)}()),a.definitions.forEach(U=>{if(this._resetContextStyleTimingState(l),0==U.type){const J=U,ge=J.name;ge.toString().split(/\s*,\s*/).forEach(Ue=>{J.name=Ue,R.push(this.visitState(J,l))}),J.name=ge}else if(1==U.type){const J=this.visitTransition(U,l);y+=J.queryCount,M+=J.depCount,L.push(J)}else l.errors.push(function et(){return new C.wOt(3007,!1)}())}),{type:7,name:a.name,states:R,transitions:L,queryCount:y,depCount:M,options:null}}visitState(a,l){const y=this.visitStyle(a.styles,l),M=a.options&&a.options.params||null;if(y.containsDynamicStyles){const R=new Set,L=M||{};y.styles.forEach(U=>{U instanceof Map&&U.forEach(J=>{Ut(J).forEach(ge=>{L.hasOwnProperty(ge)||R.add(ge)})})}),R.size&&(vn(R.values()),l.errors.push(function gt(_,a){return new C.wOt(3008,!1)}()))}return{type:0,name:a.name,style:y,options:M?{params:M}:null}}visitTransition(a,l){l.queryCount=0,l.depCount=0;const y=ct(this,Oe(a.animation),l);return{type:1,matchers:Ki(a.expr,l.errors),animation:y,queryCount:l.queryCount,depCount:l.depCount,options:Gr(a.options)}}visitSequence(a,l){return{type:2,steps:a.steps.map(y=>ct(this,y,l)),options:Gr(a.options)}}visitGroup(a,l){const y=l.currentTime;let M=0;const R=a.steps.map(L=>{l.currentTime=y;const U=ct(this,L,l);return M=Math.max(M,l.currentTime),U});return l.currentTime=M,{type:3,steps:R,options:Gr(a.options)}}visitAnimate(a,l){const y=function No(_,a){if(_.hasOwnProperty("duration"))return _;if("number"==typeof _)return io(At(_,a).duration,0,"");const l=_;if(l.split(/\s+/).some(R=>"{"==R.charAt(0)&&"{"==R.charAt(1))){const R=io(0,0,"");return R.dynamic=!0,R.strValue=l,R}const M=At(l,a);return io(M.duration,M.delay,M.easing)}(a.timings,l.errors);l.currentAnimateTimings=y;let M,R=a.styles?a.styles:fe({});if(5==R.type)M=this.visitKeyframes(R,l);else{let L=a.styles,U=!1;if(!L){U=!0;const ge={};y.easing&&(ge.easing=y.easing),L=fe(ge)}l.currentTime+=y.duration+y.delay;const J=this.visitStyle(L,l);J.isEmptyStep=U,M=J}return l.currentAnimateTimings=null,{type:4,timings:y,style:M,options:null}}visitStyle(a,l){const y=this._makeStyleAst(a,l);return this._validateStyleAst(y,l),y}_makeStyleAst(a,l){const y=[],M=Array.isArray(a.styles)?a.styles:[a.styles];for(let U of M)"string"==typeof U?U===X?y.push(U):l.errors.push(new C.wOt(3002,!1)):y.push(Bn(U));let R=!1,L=null;return y.forEach(U=>{if(U instanceof Map&&(U.has("easing")&&(L=U.get("easing"),U.delete("easing")),!R))for(let J of U.values())if(J.toString().indexOf("{{")>=0){R=!0;break}}),{type:6,styles:y,easing:L,offset:a.offset,containsDynamicStyles:R,options:null}}_validateStyleAst(a,l){const y=l.currentAnimateTimings;let M=l.currentTime,R=l.currentTime;y&&R>0&&(R-=y.duration+y.delay),a.styles.forEach(L=>{"string"!=typeof L&&L.forEach((U,J)=>{const ge=l.collectedStyles.get(l.currentQuerySelector),Ue=ge.get(J);let He=!0;Ue&&(R!=M&&R>=Ue.startTime&&M<=Ue.endTime&&(l.errors.push(function Mn(_,a,l,y,M){return new C.wOt(3010,!1)}()),He=!1),R=Ue.startTime),He&&ge.set(J,{startTime:R,endTime:M}),l.options&&function Me(_,a,l){const y=a.params||{},M=Ut(_);M.length&&M.forEach(R=>{y.hasOwnProperty(R)||l.push(function Te(_){return new C.wOt(3001,!1)}())})}(U,l.options,l.errors)})})}visitKeyframes(a,l){const y={type:5,styles:[],options:null};if(!l.currentAnimateTimings)return l.errors.push(function Sn(){return new C.wOt(3011,!1)}()),y;let R=0;const L=[];let U=!1,J=!1,ge=0;const Ue=a.steps.map(jn=>{const St=this._makeStyleAst(jn,l);let ar=null!=St.offset?St.offset:function Oo(_){if("string"==typeof _)return null;let a=null;if(Array.isArray(_))_.forEach(l=>{if(l instanceof Map&&l.has("offset")){const y=l;a=parseFloat(y.get("offset")),y.delete("offset")}});else if(_ instanceof Map&&_.has("offset")){const l=_;a=parseFloat(l.get("offset")),l.delete("offset")}return a}(St.styles),Gt=0;return null!=ar&&(R++,Gt=St.offset=ar),J=J||Gt<0||Gt>1,U=U||Gt<ge,ge=Gt,L.push(Gt),St});J&&l.errors.push(function fn(){return new C.wOt(3012,!1)}()),U&&l.errors.push(function Z(){return new C.wOt(3200,!1)}());const He=a.steps.length;let Ft=0;R>0&&R<He?l.errors.push(function te(){return new C.wOt(3202,!1)}()):0==R&&(Ft=1/(He-1));const wt=He-1,st=l.currentTime,at=l.currentAnimateTimings,mn=at.duration;return Ue.forEach((jn,St)=>{const ar=Ft>0?St==wt?1:Ft*St:L[St],Gt=ar*mn;l.currentTime=st+at.delay+Gt,at.duration=Gt,this._validateStyleAst(jn,l),jn.offset=ar,y.styles.push(jn)}),y}visitReference(a,l){return{type:8,animation:ct(this,Oe(a.animation),l),options:Gr(a.options)}}visitAnimateChild(a,l){return l.depCount++,{type:9,options:Gr(a.options)}}visitAnimateRef(a,l){return{type:10,animation:this.visitReference(a.animation,l),options:Gr(a.options)}}visitQuery(a,l){const y=l.currentQuerySelector,M=a.options||{};l.queryCount++,l.currentQuery=a;const[R,L]=function ir(_){const a=!!_.split(/\s*,\s*/).find(l=>":self"==l);return a&&(_=_.replace(Ar,"")),_=_.replace(/@\*/g,V).replace(/@\w+/g,l=>V+"-"+l.slice(1)).replace(/:animating/g,pe),[_,a]}(a.selector);l.currentQuerySelector=y.length?y+" "+R:R,an(l.collectedStyles,l.currentQuerySelector,new Map);const U=ct(this,Oe(a.animation),l);return l.currentQuery=null,l.currentQuerySelector=y,{type:11,selector:R,limit:M.limit||0,optional:!!M.optional,includeSelf:L,animation:U,originalSelector:a.selector,options:Gr(a.options)}}visitStagger(a,l){l.currentQuery||l.errors.push(function ie(){return new C.wOt(3013,!1)}());const y="full"===a.timings?{duration:0,delay:0,easing:"full"}:At(a.timings,l.errors,!0);return{type:12,animation:ct(this,Oe(a.animation),l),timings:y,options:null}}}class Zi{constructor(a){this.errors=a,this.queryCount=0,this.depCount=0,this.currentTransition=null,this.currentQuery=null,this.currentQuerySelector=null,this.currentAnimateTimings=null,this.currentTime=0,this.collectedStyles=new Map,this.options=null,this.unsupportedCSSPropertiesFound=new Set}}function Gr(_){return _?(_=ln(_)).params&&(_.params=function Hn(_){return _?ln(_):null}(_.params)):_={},_}function io(_,a,l){return{duration:_,delay:a,easing:l}}function gi(_,a,l,y,M,R,L=null,U=!1){return{type:1,element:_,keyframes:a,preStyleProps:l,postStyleProps:y,duration:M,delay:R,totalTime:M+R,easing:L,subTimeline:U}}class mi{constructor(){this._map=new Map}get(a){return this._map.get(a)||[]}append(a,l){let y=this._map.get(a);y||this._map.set(a,y=[]),y.push(...l)}has(a){return this._map.has(a)}clear(){this._map.clear()}}const Ji=new RegExp(":enter","g"),$o=new RegExp(":leave","g");function yi(_,a,l,y,M,R=new Map,L=new Map,U,J,ge=[]){return(new Pi).buildKeyframes(_,a,l,y,M,R,L,U,J,ge)}class Pi{buildKeyframes(a,l,y,M,R,L,U,J,ge,Ue=[]){ge=ge||new mi;const He=new so(a,l,ge,M,R,Ue,[]);He.options=J;const Ft=J.delay?Fe(J.delay):0;He.currentTimeline.delayNextStep(Ft),He.currentTimeline.setStyles([L],null,He.errors,J),ct(this,y,He);const wt=He.timelines.filter(st=>st.containsAnimation());if(wt.length&&U.size){let st;for(let at=wt.length-1;at>=0;at--){const mn=wt[at];if(mn.element===l){st=mn;break}}st&&!st.allowOnlyTimelineStyles()&&st.setStyles([U],null,He.errors,J)}return wt.length?wt.map(st=>st.buildKeyframes()):[gi(l,[],[],[],0,Ft,"",!1)]}visitTrigger(a,l){}visitState(a,l){}visitTransition(a,l){}visitAnimateChild(a,l){const y=l.subInstructions.get(l.element);if(y){const M=l.createSubContext(a.options),R=l.currentTimeline.currentTime,L=this._visitSubInstructions(y,M,M.options);R!=L&&l.transformIntoNewTimeline(L)}l.previousNode=a}visitAnimateRef(a,l){const y=l.createSubContext(a.options);y.transformIntoNewTimeline(),this._applyAnimationRefDelays([a.options,a.animation.options],l,y),this.visitReference(a.animation,y),l.transformIntoNewTimeline(y.currentTimeline.currentTime),l.previousNode=a}_applyAnimationRefDelays(a,l,y){for(const M of a){const R=M?.delay;if(R){const L="number"==typeof R?R:Fe(tt(R,M?.params??{},l.errors));y.delayNextStep(L)}}}_visitSubInstructions(a,l,y){let R=l.currentTimeline.currentTime;const L=null!=y.duration?Fe(y.duration):null,U=null!=y.delay?Fe(y.delay):null;return 0!==L&&a.forEach(J=>{const ge=l.appendInstructionToTimeline(J,L,U);R=Math.max(R,ge.duration+ge.delay)}),R}visitReference(a,l){l.updateOptions(a.options,!0),ct(this,a.animation,l),l.previousNode=a}visitSequence(a,l){const y=l.subContextCount;let M=l;const R=a.options;if(R&&(R.params||R.delay)&&(M=l.createSubContext(R),M.transformIntoNewTimeline(),null!=R.delay)){6==M.previousNode.type&&(M.currentTimeline.snapshotCurrentStyles(),M.previousNode=Wr);const L=Fe(R.delay);M.delayNextStep(L)}a.steps.length&&(a.steps.forEach(L=>ct(this,L,M)),M.currentTimeline.applyStylesToKeyframe(),M.subContextCount>y&&M.transformIntoNewTimeline()),l.previousNode=a}visitGroup(a,l){const y=[];let M=l.currentTimeline.currentTime;const R=a.options&&a.options.delay?Fe(a.options.delay):0;a.steps.forEach(L=>{const U=l.createSubContext(a.options);R&&U.delayNextStep(R),ct(this,L,U),M=Math.max(M,U.currentTimeline.currentTime),y.push(U.currentTimeline)}),y.forEach(L=>l.currentTimeline.mergeTimelineCollectedStyles(L)),l.transformIntoNewTimeline(M),l.previousNode=a}_visitTiming(a,l){if(a.dynamic){const y=a.strValue;return At(l.params?tt(y,l.params,l.errors):y,l.errors)}return{duration:a.duration,delay:a.delay,easing:a.easing}}visitAnimate(a,l){const y=l.currentAnimateTimings=this._visitTiming(a.timings,l),M=l.currentTimeline;y.delay&&(l.incrementTime(y.delay),M.snapshotCurrentStyles());const R=a.style;5==R.type?this.visitKeyframes(R,l):(l.incrementTime(y.duration),this.visitStyle(R,l),M.applyStylesToKeyframe()),l.currentAnimateTimings=null,l.previousNode=a}visitStyle(a,l){const y=l.currentTimeline,M=l.currentAnimateTimings;!M&&y.hasCurrentStyleProperties()&&y.forwardFrame();const R=M&&M.easing||a.easing;a.isEmptyStep?y.applyEmptyStep(R):y.setStyles(a.styles,R,l.errors,l.options),l.previousNode=a}visitKeyframes(a,l){const y=l.currentAnimateTimings,M=l.currentTimeline.duration,R=y.duration,U=l.createSubContext().currentTimeline;U.easing=y.easing,a.styles.forEach(J=>{U.forwardTime((J.offset||0)*R),U.setStyles(J.styles,J.easing,l.errors,l.options),U.applyStylesToKeyframe()}),l.currentTimeline.mergeTimelineCollectedStyles(U),l.transformIntoNewTimeline(M+R),l.previousNode=a}visitQuery(a,l){const y=l.currentTimeline.currentTime,M=a.options||{},R=M.delay?Fe(M.delay):0;R&&(6===l.previousNode.type||0==y&&l.currentTimeline.hasCurrentStyleProperties())&&(l.currentTimeline.snapshotCurrentStyles(),l.previousNode=Wr);let L=y;const U=l.invokeQuery(a.selector,a.originalSelector,a.limit,a.includeSelf,!!M.optional,l.errors);l.currentQueryTotal=U.length;let J=null;U.forEach((ge,Ue)=>{l.currentQueryIndex=Ue;const He=l.createSubContext(a.options,ge);R&&He.delayNextStep(R),ge===l.element&&(J=He.currentTimeline),ct(this,a.animation,He),He.currentTimeline.applyStylesToKeyframe(),L=Math.max(L,He.currentTimeline.currentTime)}),l.currentQueryIndex=0,l.currentQueryTotal=0,l.transformIntoNewTimeline(L),J&&(l.currentTimeline.mergeTimelineCollectedStyles(J),l.currentTimeline.snapshotCurrentStyles()),l.previousNode=a}visitStagger(a,l){const y=l.parentContext,M=l.currentTimeline,R=a.timings,L=Math.abs(R.duration),U=L*(l.currentQueryTotal-1);let J=L*l.currentQueryIndex;switch(R.duration<0?"reverse":R.easing){case"reverse":J=U-J;break;case"full":J=y.currentStaggerTime}const Ue=l.currentTimeline;J&&Ue.delayNextStep(J);const He=Ue.currentTime;ct(this,a.animation,l),l.previousNode=a,y.currentStaggerTime=M.currentTime-He+(M.startTime-y.currentTimeline.startTime)}}const Wr={};class so{constructor(a,l,y,M,R,L,U,J){this._driver=a,this.element=l,this.subInstructions=y,this._enterClassName=M,this._leaveClassName=R,this.errors=L,this.timelines=U,this.parentContext=null,this.currentAnimateTimings=null,this.previousNode=Wr,this.subContextCount=0,this.options={},this.currentQueryIndex=0,this.currentQueryTotal=0,this.currentStaggerTime=0,this.currentTimeline=J||new vi(this._driver,l,0),U.push(this.currentTimeline)}get params(){return this.options.params}updateOptions(a,l){if(!a)return;const y=a;let M=this.options;null!=y.duration&&(M.duration=Fe(y.duration)),null!=y.delay&&(M.delay=Fe(y.delay));const R=y.params;if(R){let L=M.params;L||(L=this.options.params={}),Object.keys(R).forEach(U=>{(!l||!L.hasOwnProperty(U))&&(L[U]=tt(R[U],L,this.errors))})}}_copyOptions(){const a={};if(this.options){const l=this.options.params;if(l){const y=a.params={};Object.keys(l).forEach(M=>{y[M]=l[M]})}}return a}createSubContext(a=null,l,y){const M=l||this.element,R=new so(this._driver,M,this.subInstructions,this._enterClassName,this._leaveClassName,this.errors,this.timelines,this.currentTimeline.fork(M,y||0));return R.previousNode=this.previousNode,R.currentAnimateTimings=this.currentAnimateTimings,R.options=this._copyOptions(),R.updateOptions(a),R.currentQueryIndex=this.currentQueryIndex,R.currentQueryTotal=this.currentQueryTotal,R.parentContext=this,this.subContextCount++,R}transformIntoNewTimeline(a){return this.previousNode=Wr,this.currentTimeline=this.currentTimeline.fork(this.element,a),this.timelines.push(this.currentTimeline),this.currentTimeline}appendInstructionToTimeline(a,l,y){const M={duration:l??a.duration,delay:this.currentTimeline.currentTime+(y??0)+a.delay,easing:""},R=new Kr(this._driver,a.element,a.keyframes,a.preStyleProps,a.postStyleProps,M,a.stretchStartingKeyframe);return this.timelines.push(R),M}incrementTime(a){this.currentTimeline.forwardTime(this.currentTimeline.duration+a)}delayNextStep(a){a>0&&this.currentTimeline.delayNextStep(a)}invokeQuery(a,l,y,M,R,L){let U=[];if(M&&U.push(this.element),a.length>0){a=(a=a.replace(Ji,"."+this._enterClassName)).replace($o,"."+this._leaveClassName);let ge=this._driver.query(this.element,a,1!=y);0!==y&&(ge=y<0?ge.slice(ge.length+y,ge.length):ge.slice(0,y)),U.push(...ge)}return!R&&0==U.length&&L.push(function ae(_){return new C.wOt(3014,!1)}()),U}}class vi{constructor(a,l,y,M){this._driver=a,this.element=l,this.startTime=y,this._elementTimelineStylesLookup=M,this.duration=0,this.easing=null,this._previousKeyframe=new Map,this._currentKeyframe=new Map,this._keyframes=new Map,this._styleSummary=new Map,this._localTimelineStyles=new Map,this._pendingStyles=new Map,this._backFill=new Map,this._currentEmptyStepKeyframe=null,this._elementTimelineStylesLookup||(this._elementTimelineStylesLookup=new Map),this._globalTimelineStyles=this._elementTimelineStylesLookup.get(l),this._globalTimelineStyles||(this._globalTimelineStyles=this._localTimelineStyles,this._elementTimelineStylesLookup.set(l,this._localTimelineStyles)),this._loadKeyframe()}containsAnimation(){switch(this._keyframes.size){case 0:return!1;case 1:return this.hasCurrentStyleProperties();default:return!0}}hasCurrentStyleProperties(){return this._currentKeyframe.size>0}get currentTime(){return this.startTime+this.duration}delayNextStep(a){const l=1===this._keyframes.size&&this._pendingStyles.size;this.duration||l?(this.forwardTime(this.currentTime+a),l&&this.snapshotCurrentStyles()):this.startTime+=a}fork(a,l){return this.applyStylesToKeyframe(),new vi(this._driver,a,l||this.currentTime,this._elementTimelineStylesLookup)}_loadKeyframe(){this._currentKeyframe&&(this._previousKeyframe=this._currentKeyframe),this._currentKeyframe=this._keyframes.get(this.duration),this._currentKeyframe||(this._currentKeyframe=new Map,this._keyframes.set(this.duration,this._currentKeyframe))}forwardFrame(){this.duration+=1,this._loadKeyframe()}forwardTime(a){this.applyStylesToKeyframe(),this.duration=a,this._loadKeyframe()}_updateStyle(a,l){this._localTimelineStyles.set(a,l),this._globalTimelineStyles.set(a,l),this._styleSummary.set(a,{time:this.currentTime,value:l})}allowOnlyTimelineStyles(){return this._currentEmptyStepKeyframe!==this._currentKeyframe}applyEmptyStep(a){a&&this._previousKeyframe.set("easing",a);for(let[l,y]of this._globalTimelineStyles)this._backFill.set(l,y||X),this._currentKeyframe.set(l,X);this._currentEmptyStepKeyframe=this._currentKeyframe}setStyles(a,l,y,M){l&&this._previousKeyframe.set("easing",l);const R=M&&M.params||{},L=function gn(_,a){const l=new Map;let y;return _.forEach(M=>{if("*"===M){y=y||a.keys();for(let R of y)l.set(R,X)}else j(M,l)}),l}(a,this._globalTimelineStyles);for(let[U,J]of L){const ge=tt(J,R,y);this._pendingStyles.set(U,ge),this._localTimelineStyles.has(U)||this._backFill.set(U,this._globalTimelineStyles.get(U)??X),this._updateStyle(U,ge)}}applyStylesToKeyframe(){0!=this._pendingStyles.size&&(this._pendingStyles.forEach((a,l)=>{this._currentKeyframe.set(l,a)}),this._pendingStyles.clear(),this._localTimelineStyles.forEach((a,l)=>{this._currentKeyframe.has(l)||this._currentKeyframe.set(l,a)}))}snapshotCurrentStyles(){for(let[a,l]of this._localTimelineStyles)this._pendingStyles.set(a,l),this._updateStyle(a,l)}getFinalKeyframe(){return this._keyframes.get(this.duration)}get properties(){const a=[];for(let l in this._currentKeyframe)a.push(l);return a}mergeTimelineCollectedStyles(a){a._styleSummary.forEach((l,y)=>{const M=this._styleSummary.get(y);(!M||l.time>M.time)&&this._updateStyle(y,l.value)})}buildKeyframes(){this.applyStylesToKeyframe();const a=new Set,l=new Set,y=1===this._keyframes.size&&0===this.duration;let M=[];this._keyframes.forEach((U,J)=>{const ge=j(U,new Map,this._backFill);ge.forEach((Ue,He)=>{"!"===Ue?a.add(He):Ue===X&&l.add(He)}),y||ge.set("offset",J/this.duration),M.push(ge)});const R=a.size?vn(a.values()):[],L=l.size?vn(l.values()):[];if(y){const U=M[0],J=new Map(U);U.set("offset",0),J.set("offset",1),M=[U,J]}return gi(this.element,M,R,L,this.duration,this.startTime,this.easing,!1)}}class Kr extends vi{constructor(a,l,y,M,R,L,U=!1){super(a,l,L.delay),this.keyframes=y,this.preStyleProps=M,this.postStyleProps=R,this._stretchStartingKeyframe=U,this.timings={duration:L.duration,delay:L.delay,easing:L.easing}}containsAnimation(){return this.keyframes.length>1}buildKeyframes(){let a=this.keyframes,{delay:l,duration:y,easing:M}=this.timings;if(this._stretchStartingKeyframe&&l){const R=[],L=y+l,U=l/L,J=j(a[0]);J.set("offset",0),R.push(J);const ge=j(a[0]);ge.set("offset",Nt(U)),R.push(ge);const Ue=a.length-1;for(let He=1;He<=Ue;He++){let Ft=j(a[He]);const wt=Ft.get("offset");Ft.set("offset",Nt((l+wt*y)/L)),R.push(Ft)}y=L,l=0,M="",a=R}return gi(this.element,a,this.preStyleProps,this.postStyleProps,y,l,M,!0)}}function Nt(_,a=3){const l=Math.pow(10,a-1);return Math.round(_*l)/l}class bn{}const ii=new Set(["width","height","minWidth","minHeight","maxWidth","maxHeight","left","top","bottom","right","fontSize","outlineWidth","outlineOffset","paddingTop","paddingLeft","paddingBottom","paddingRight","marginTop","marginLeft","marginBottom","marginRight","borderRadius","borderWidth","borderTopWidth","borderLeftWidth","borderRightWidth","borderBottomWidth","textIndent","perspective"]);class Ri extends bn{normalizePropertyName(a,l){return In(a)}normalizeStyleValue(a,l,y,M){let R="";const L=y.toString().trim();if(ii.has(l)&&0!==y&&"0"!==y)if("number"==typeof y)R="px";else{const U=y.match(/^[+-]?[\d\.]+([a-z]*)$/);U&&0==U[1].length&&M.push(function Pe(_,a){return new C.wOt(3005,!1)}())}return L+R}}function Xr(_,a,l,y,M,R,L,U,J,ge,Ue,He,Ft){return{type:0,element:_,triggerName:a,isRemovalTransition:M,fromState:l,fromStyles:R,toState:y,toStyles:L,timelines:U,queriedElements:J,preStyleProps:ge,postStyleProps:Ue,totalTime:He,errors:Ft}}const Ur={};class ao{constructor(a,l,y){this._triggerName=a,this.ast=l,this._stateStyles=y}match(a,l,y,M){return function je(_,a,l,y,M){return _.some(R=>R(a,l,y,M))}(this.ast.matchers,a,l,y,M)}buildStyles(a,l,y){let M=this._stateStyles.get("*");return void 0!==a&&(M=this._stateStyles.get(a?.toString())||M),M?M.buildStyles(l,y):new Map}build(a,l,y,M,R,L,U,J,ge,Ue){const He=[],Ft=this.ast.options&&this.ast.options.params||Ur,st=this.buildStyles(y,U&&U.params||Ur,He),at=J&&J.params||Ur,mn=this.buildStyles(M,at,He),jn=new Set,St=new Map,ar=new Map,Gt="void"===M,Gn={params:Dt(at,Ft),delay:this.ast.options?.delay},ui=Ue?[]:yi(a,l,this.ast.animation,R,L,st,mn,Gn,ge,He);let Wn=0;if(ui.forEach(Nn=>{Wn=Math.max(Nn.duration+Nn.delay,Wn)}),He.length)return Xr(l,this._triggerName,y,M,Gt,st,mn,[],[],St,ar,Wn,He);ui.forEach(Nn=>{const wi=Nn.element,ya=an(St,wi,new Set);Nn.preStyleProps.forEach(xr=>ya.add(xr));const Wo=an(ar,wi,new Set);Nn.postStyleProps.forEach(xr=>Wo.add(xr)),wi!==l&&jn.add(wi)});const Er=vn(jn.values());return Xr(l,this._triggerName,y,M,Gt,st,mn,ui,Er,St,ar,Wn)}}function Dt(_,a){const l=ln(a);for(const y in _)_.hasOwnProperty(y)&&null!=_[y]&&(l[y]=_[y]);return l}class qt{constructor(a,l,y){this.styles=a,this.defaultParams=l,this.normalizer=y}buildStyles(a,l){const y=new Map,M=ln(this.defaultParams);return Object.keys(a).forEach(R=>{const L=a[R];null!==L&&(M[R]=L)}),this.styles.styles.forEach(R=>{"string"!=typeof R&&R.forEach((L,U)=>{L&&(L=tt(L,M,l));const J=this.normalizer.normalizePropertyName(U,l);L=this.normalizer.normalizeStyleValue(U,J,L,l),y.set(U,L)})}),y}}class Qr{constructor(a,l,y){this.name=a,this.ast=l,this._normalizer=y,this.transitionFactories=[],this.states=new Map,l.states.forEach(M=>{this.states.set(M.name,new qt(M.style,M.options&&M.options.params||{},y))}),Br(this.states,"true","1"),Br(this.states,"false","0"),l.transitions.forEach(M=>{this.transitionFactories.push(new ao(a,M,this.states))}),this.fallbackTransition=function On(_,a,l){return new ao(_,{type:1,animation:{type:2,steps:[],options:null},matchers:[(L,U)=>!0],options:null,queryCount:0,depCount:0},a)}(a,this.states)}get containsQueries(){return this.ast.queryCount>0}matchTransition(a,l,y,M){return this.transitionFactories.find(L=>L.match(a,l,y,M))||null}matchStyles(a,l,y){return this.fallbackTransition.buildStyles(a,l,y)}}function Br(_,a,l){_.has(a)?_.has(l)||_.set(l,_.get(a)):_.has(l)&&_.set(a,_.get(l))}const Dn=new mi;class Or{constructor(a,l,y){this.bodyNode=a,this._driver=l,this._normalizer=y,this._animations=new Map,this._playersById=new Map,this.players=[]}register(a,l){const y=[],R=yr(this._driver,l,y,[]);if(y.length)throw function xt(_){return new C.wOt(3503,!1)}();this._animations.set(a,R)}_buildPlayer(a,l,y){const M=a.element,R=ht(this._normalizer,a.keyframes,l,y);return this._driver.animate(M,R,a.duration,a.delay,a.easing,[],!0)}create(a,l,y={}){const M=[],R=this._animations.get(a);let L;const U=new Map;if(R?(L=yi(this._driver,l,R,Lr,he,new Map,new Map,y,Dn,M),L.forEach(Ue=>{const He=an(U,Ue.element,new Map);Ue.postStyleProps.forEach(Ft=>He.set(Ft,null))})):(M.push(function Qt(){return new C.wOt(3300,!1)}()),L=[]),M.length)throw function tn(_){return new C.wOt(3504,!1)}();U.forEach((Ue,He)=>{Ue.forEach((Ft,wt)=>{Ue.set(wt,this._driver.computeStyle(He,wt,X))})});const ge=vt(L.map(Ue=>{const He=U.get(Ue.element);return this._buildPlayer(Ue,new Map,He)}));return this._playersById.set(a,ge),ge.onDestroy(()=>this.destroy(a)),this.players.push(ge),ge}destroy(a){const l=this._getPlayer(a);l.destroy(),this._playersById.delete(a);const y=this.players.indexOf(l);y>=0&&this.players.splice(y,1)}_getPlayer(a){const l=this._playersById.get(a);if(!l)throw function Ae(_){return new C.wOt(3301,!1)}();return l}listen(a,l,y,M){const R=sn(l,"","","");return nr(this._getPlayer(a),y,R,M),()=>{}}command(a,l,y,M){if("register"==y)return void this.register(a,M[0]);if("create"==y)return void this.create(a,l,M[0]||{});const R=this._getPlayer(a);switch(y){case"play":R.play();break;case"pause":R.pause();break;case"reset":R.reset();break;case"restart":R.restart();break;case"finish":R.finish();break;case"init":R.init();break;case"setPosition":R.setPosition(parseFloat(M[0]));break;case"destroy":this.destroy(a)}}}const Nr="ng-animate-queued",vr="ng-animate-disabled",oi=[],qr={namespaceId:"",setForRemoval:!1,setForMove:!1,hasAnimation:!1,removedBeforeQueried:!1},hr={namespaceId:"",setForMove:!1,setForRemoval:!1,hasAnimation:!1,removedBeforeQueried:!0},Vn="__ng_removed";class Zr{get params(){return this.options.params}constructor(a,l=""){this.namespaceId=l;const y=a&&a.hasOwnProperty("value");if(this.value=function go(_){return _??null}(y?a.value:a),y){const R=ln(a);delete R.value,this.options=R}else this.options={};this.options.params||(this.options.params={})}absorbOptions(a){const l=a.params;if(l){const y=this.options.params;Object.keys(l).forEach(M=>{null==y[M]&&(y[M]=l[M])})}}}const Jn="void",Pr=new Zr(Jn);class Yi{constructor(a,l,y){this.id=a,this.hostElement=l,this._engine=y,this.players=[],this._triggers=new Map,this._queue=[],this._elementListeners=new Map,this._hostClassName="ng-tns-"+a,Lt(l,this._hostClassName)}listen(a,l,y,M){if(!this._triggers.has(l))throw function hn(_,a){return new C.wOt(3302,!1)}();if(null==y||0==y.length)throw function xn(_){return new C.wOt(3303,!1)}();if(!function Rr(_){return"start"==_||"done"==_}(y))throw function tr(_,a){return new C.wOt(3400,!1)}();const R=an(this._elementListeners,a,[]),L={name:l,phase:y,callback:M};R.push(L);const U=an(this._engine.statesByElement,a,new Map);return U.has(l)||(Lt(a,q),Lt(a,q+"-"+l),U.set(l,Pr)),()=>{this._engine.afterFlush(()=>{const J=R.indexOf(L);J>=0&&R.splice(J,1),this._triggers.has(l)||U.delete(l)})}}register(a,l){return!this._triggers.has(a)&&(this._triggers.set(a,l),!0)}_getTrigger(a){const l=this._triggers.get(a);if(!l)throw function Re(_){return new C.wOt(3401,!1)}();return l}trigger(a,l,y,M=!0){const R=this._getTrigger(l),L=new Ot(this.id,l,a);let U=this._engine.statesByElement.get(a);U||(Lt(a,q),Lt(a,q+"-"+l),this._engine.statesByElement.set(a,U=new Map));let J=U.get(l);const ge=new Zr(y,this.id);if(!(y&&y.hasOwnProperty("value"))&&J&&ge.absorbOptions(J.options),U.set(l,ge),J||(J=Pr),ge.value!==Jn&&J.value===ge.value){if(!function Cr(_,a){const l=Object.keys(_),y=Object.keys(a);if(l.length!=y.length)return!1;for(let M=0;M<l.length;M++){const R=l[M];if(!a.hasOwnProperty(R)||_[R]!==a[R])return!1}return!0}(J.params,ge.params)){const at=[],mn=R.matchStyles(J.value,J.params,at),jn=R.matchStyles(ge.value,ge.params,at);at.length?this._engine.reportError(at):this._engine.afterFlush(()=>{ue(a,mn),$(a,jn)})}return}const Ft=an(this._engine.playersByElement,a,[]);Ft.forEach(at=>{at.namespaceId==this.id&&at.triggerName==l&&at.queued&&at.destroy()});let wt=R.matchTransition(J.value,ge.value,a,ge.params),st=!1;if(!wt){if(!M)return;wt=R.fallbackTransition,st=!0}return this._engine.totalQueuedPlayers++,this._queue.push({element:a,triggerName:l,transition:wt,fromState:J,toState:ge,player:L,isFallbackTransition:st}),st||(Lt(a,Nr),L.onStart(()=>{ki(a,Nr)})),L.onDone(()=>{let at=this.players.indexOf(L);at>=0&&this.players.splice(at,1);const mn=this._engine.playersByElement.get(a);if(mn){let jn=mn.indexOf(L);jn>=0&&mn.splice(jn,1)}}),this.players.push(L),Ft.push(L),L}deregister(a){this._triggers.delete(a),this._engine.statesByElement.forEach(l=>l.delete(a)),this._elementListeners.forEach((l,y)=>{this._elementListeners.set(y,l.filter(M=>M.name!=a))})}clearElementCache(a){this._engine.statesByElement.delete(a),this._elementListeners.delete(a);const l=this._engine.playersByElement.get(a);l&&(l.forEach(y=>y.destroy()),this._engine.playersByElement.delete(a))}_signalRemovalForInnerTriggers(a,l){const y=this._engine.driver.query(a,V,!0);y.forEach(M=>{if(M[Vn])return;const R=this._engine.fetchNamespacesByElement(M);R.size?R.forEach(L=>L.triggerLeaveAnimation(M,l,!1,!0)):this.clearElementCache(M)}),this._engine.afterFlushAnimationsDone(()=>y.forEach(M=>this.clearElementCache(M)))}triggerLeaveAnimation(a,l,y,M){const R=this._engine.statesByElement.get(a),L=new Map;if(R){const U=[];if(R.forEach((J,ge)=>{if(L.set(ge,J.value),this._triggers.has(ge)){const Ue=this.trigger(a,ge,Jn,M);Ue&&U.push(Ue)}}),U.length)return this._engine.markElementAsRemoved(this.id,a,!0,l,L),y&&vt(U).onDone(()=>this._engine.processLeaveNode(a)),!0}return!1}prepareLeaveAnimationListeners(a){const l=this._elementListeners.get(a),y=this._engine.statesByElement.get(a);if(l&&y){const M=new Set;l.forEach(R=>{const L=R.name;if(M.has(L))return;M.add(L);const J=this._triggers.get(L).fallbackTransition,ge=y.get(L)||Pr,Ue=new Zr(Jn),He=new Ot(this.id,L,a);this._engine.totalQueuedPlayers++,this._queue.push({element:a,triggerName:L,transition:J,fromState:ge,toState:Ue,player:He,isFallbackTransition:!0})})}}removeNode(a,l){const y=this._engine;if(a.childElementCount&&this._signalRemovalForInnerTriggers(a,l),this.triggerLeaveAnimation(a,l,!0))return;let M=!1;if(y.totalAnimations){const R=y.players.length?y.playersByQueriedElement.get(a):[];if(R&&R.length)M=!0;else{let L=a;for(;L=L.parentNode;)if(y.statesByElement.get(L)){M=!0;break}}}if(this.prepareLeaveAnimationListeners(a),M)y.markElementAsRemoved(this.id,a,!1,l);else{const R=a[Vn];(!R||R===qr)&&(y.afterFlush(()=>this.clearElementCache(a)),y.destroyInnerAnimations(a),y._onRemovalComplete(a,l))}}insertNode(a,l){Lt(a,this._hostClassName)}drainQueuedTransitions(a){const l=[];return this._queue.forEach(y=>{const M=y.player;if(M.destroyed)return;const R=y.element,L=this._elementListeners.get(R);L&&L.forEach(U=>{if(U.name==y.triggerName){const J=sn(R,y.triggerName,y.fromState.value,y.toState.value);J._data=a,nr(y.player,U.phase,J,U.callback)}}),M.markedForDestroy?this._engine.afterFlush(()=>{M.destroy()}):l.push(y)}),this._queue=[],l.sort((y,M)=>{const R=y.transition.ast.depCount,L=M.transition.ast.depCount;return 0==R||0==L?R-L:this._engine.driver.containsElement(y.element,M.element)?1:-1})}destroy(a){this.players.forEach(l=>l.destroy()),this._signalRemovalForInnerTriggers(this.hostElement,a)}}class xi{_onRemovalComplete(a,l){this.onRemovalComplete(a,l)}constructor(a,l,y){this.bodyNode=a,this.driver=l,this._normalizer=y,this.players=[],this.newHostElements=new Map,this.playersByElement=new Map,this.playersByQueriedElement=new Map,this.statesByElement=new Map,this.disabledNodes=new Set,this.totalAnimations=0,this.totalQueuedPlayers=0,this._namespaceLookup={},this._namespaceList=[],this._flushFns=[],this._whenQuietFns=[],this.namespacesByHostElement=new Map,this.collectedEnterElements=[],this.collectedLeaveElements=[],this.onRemovalComplete=(M,R)=>{}}get queuedPlayers(){const a=[];return this._namespaceList.forEach(l=>{l.players.forEach(y=>{y.queued&&a.push(y)})}),a}createNamespace(a,l){const y=new Yi(a,l,this);return this.bodyNode&&this.driver.containsElement(this.bodyNode,l)?this._balanceNamespaceList(y,l):(this.newHostElements.set(l,y),this.collectEnterElement(l)),this._namespaceLookup[a]=y}_balanceNamespaceList(a,l){const y=this._namespaceList,M=this.namespacesByHostElement;if(y.length-1>=0){let L=!1,U=this.driver.getParentElement(l);for(;U;){const J=M.get(U);if(J){const ge=y.indexOf(J);y.splice(ge+1,0,a),L=!0;break}U=this.driver.getParentElement(U)}L||y.unshift(a)}else y.push(a);return M.set(l,a),a}register(a,l){let y=this._namespaceLookup[a];return y||(y=this.createNamespace(a,l)),y}registerTrigger(a,l,y){let M=this._namespaceLookup[a];M&&M.register(l,y)&&this.totalAnimations++}destroy(a,l){a&&(this.afterFlush(()=>{}),this.afterFlushAnimationsDone(()=>{const y=this._fetchNamespace(a);this.namespacesByHostElement.delete(y.hostElement);const M=this._namespaceList.indexOf(y);M>=0&&this._namespaceList.splice(M,1),y.destroy(l),delete this._namespaceLookup[a]}))}_fetchNamespace(a){return this._namespaceLookup[a]}fetchNamespacesByElement(a){const l=new Set,y=this.statesByElement.get(a);if(y)for(let M of y.values())if(M.namespaceId){const R=this._fetchNamespace(M.namespaceId);R&&l.add(R)}return l}trigger(a,l,y,M){if(si(l)){const R=this._fetchNamespace(a);if(R)return R.trigger(l,y,M),!0}return!1}insertNode(a,l,y,M){if(!si(l))return;const R=l[Vn];if(R&&R.setForRemoval){R.setForRemoval=!1,R.setForMove=!0;const L=this.collectedLeaveElements.indexOf(l);L>=0&&this.collectedLeaveElements.splice(L,1)}if(a){const L=this._fetchNamespace(a);L&&L.insertNode(l,y)}M&&this.collectEnterElement(l)}collectEnterElement(a){this.collectedEnterElements.push(a)}markElementAsDisabled(a,l){l?this.disabledNodes.has(a)||(this.disabledNodes.add(a),Lt(a,vr)):this.disabledNodes.has(a)&&(this.disabledNodes.delete(a),ki(a,vr))}removeNode(a,l,y){if(si(l)){const M=a?this._fetchNamespace(a):null;M?M.removeNode(l,y):this.markElementAsRemoved(a,l,!1,y);const R=this.namespacesByHostElement.get(l);R&&R.id!==a&&R.removeNode(l,y)}else this._onRemovalComplete(l,y)}markElementAsRemoved(a,l,y,M,R){this.collectedLeaveElements.push(l),l[Vn]={namespaceId:a,setForRemoval:M,hasAnimation:y,removedBeforeQueried:!1,previousTriggersValues:R}}listen(a,l,y,M,R){return si(l)?this._fetchNamespace(a).listen(l,y,M,R):()=>{}}_buildInstruction(a,l,y,M,R){return a.transition.build(this.driver,a.element,a.fromState.value,a.toState.value,y,M,a.fromState.options,a.toState.options,l,R)}destroyInnerAnimations(a){let l=this.driver.query(a,V,!0);l.forEach(y=>this.destroyActiveAnimationsForElement(y)),0!=this.playersByQueriedElement.size&&(l=this.driver.query(a,pe,!0),l.forEach(y=>this.finishActiveQueriedAnimationOnElement(y)))}destroyActiveAnimationsForElement(a){const l=this.playersByElement.get(a);l&&l.forEach(y=>{y.queued?y.markedForDestroy=!0:y.destroy()})}finishActiveQueriedAnimationOnElement(a){const l=this.playersByQueriedElement.get(a);l&&l.forEach(y=>y.finish())}whenRenderingDone(){return new Promise(a=>{if(this.players.length)return vt(this.players).onDone(()=>a());a()})}processLeaveNode(a){const l=a[Vn];if(l&&l.setForRemoval){if(a[Vn]=qr,l.namespaceId){this.destroyInnerAnimations(a);const y=this._fetchNamespace(l.namespaceId);y&&y.clearElementCache(a)}this._onRemovalComplete(a,l.setForRemoval)}a.classList?.contains(vr)&&this.markElementAsDisabled(a,!1),this.driver.query(a,".ng-animate-disabled",!0).forEach(y=>{this.markElementAsDisabled(y,!1)})}flush(a=-1){let l=[];if(this.newHostElements.size&&(this.newHostElements.forEach((y,M)=>this._balanceNamespaceList(y,M)),this.newHostElements.clear()),this.totalAnimations&&this.collectedEnterElements.length)for(let y=0;y<this.collectedEnterElements.length;y++)Lt(this.collectedEnterElements[y],"ng-star-inserted");if(this._namespaceList.length&&(this.totalQueuedPlayers||this.collectedLeaveElements.length)){const y=[];try{l=this._flushAnimations(y,a)}finally{for(let M=0;M<y.length;M++)y[M]()}}else for(let y=0;y<this.collectedLeaveElements.length;y++)this.processLeaveNode(this.collectedLeaveElements[y]);if(this.totalQueuedPlayers=0,this.collectedEnterElements.length=0,this.collectedLeaveElements.length=0,this._flushFns.forEach(y=>y()),this._flushFns=[],this._whenQuietFns.length){const y=this._whenQuietFns;this._whenQuietFns=[],l.length?vt(l).onDone(()=>{y.forEach(M=>M())}):y.forEach(M=>M())}}reportError(a){throw function Un(_){return new C.wOt(3402,!1)}()}_flushAnimations(a,l){const y=new mi,M=[],R=new Map,L=[],U=new Map,J=new Map,ge=new Map,Ue=new Set;this.disabledNodes.forEach(Ye=>{Ue.add(Ye);const lt=this.driver.query(Ye,".ng-animate-queued",!0);for(let it=0;it<lt.length;it++)Ue.add(lt[it])});const He=this.bodyNode,Ft=Array.from(this.statesByElement.keys()),wt=Fi(Ft,this.collectedEnterElements),st=new Map;let at=0;wt.forEach((Ye,lt)=>{const it=Lr+at++;st.set(lt,it),Ye.forEach(zt=>Lt(zt,it))});const mn=[],jn=new Set,St=new Set;for(let Ye=0;Ye<this.collectedLeaveElements.length;Ye++){const lt=this.collectedLeaveElements[Ye],it=lt[Vn];it&&it.setForRemoval&&(mn.push(lt),jn.add(lt),it.hasAnimation?this.driver.query(lt,".ng-star-inserted",!0).forEach(zt=>jn.add(zt)):St.add(lt))}const ar=new Map,Gt=Fi(Ft,Array.from(jn));Gt.forEach((Ye,lt)=>{const it=he+at++;ar.set(lt,it),Ye.forEach(zt=>Lt(zt,it))}),a.push(()=>{wt.forEach((Ye,lt)=>{const it=st.get(lt);Ye.forEach(zt=>ki(zt,it))}),Gt.forEach((Ye,lt)=>{const it=ar.get(lt);Ye.forEach(zt=>ki(zt,it))}),mn.forEach(Ye=>{this.processLeaveNode(Ye)})});const Gn=[],ui=[];for(let Ye=this._namespaceList.length-1;Ye>=0;Ye--)this._namespaceList[Ye].drainQueuedTransitions(l).forEach(it=>{const zt=it.player,Xe=it.element;if(Gn.push(zt),this.collectedEnterElements.length){const Pn=Xe[Vn];if(Pn&&Pn.setForMove){if(Pn.previousTriggersValues&&Pn.previousTriggersValues.has(it.triggerName)){const Co=Pn.previousTriggersValues.get(it.triggerName),$r=this.statesByElement.get(it.element);if($r&&$r.has(it.triggerName)){const Xo=$r.get(it.triggerName);Xo.value=Co,$r.set(it.triggerName,Xo)}}return void zt.destroy()}}const bi=!He||!this.driver.containsElement(He,Xe),ci=ar.get(Xe),Hi=st.get(Xe),Cn=this._buildInstruction(it,y,Hi,ci,bi);if(Cn.errors&&Cn.errors.length)return void ui.push(Cn);if(bi)return zt.onStart(()=>ue(Xe,Cn.fromStyles)),zt.onDestroy(()=>$(Xe,Cn.toStyles)),void M.push(zt);if(it.isFallbackTransition)return zt.onStart(()=>ue(Xe,Cn.fromStyles)),zt.onDestroy(()=>$(Xe,Cn.toStyles)),void M.push(zt);const Ko=[];Cn.timelines.forEach(Pn=>{Pn.stretchStartingKeyframe=!0,this.disabledNodes.has(Pn.element)||Ko.push(Pn)}),Cn.timelines=Ko,y.append(Xe,Cn.timelines),L.push({instruction:Cn,player:zt,element:Xe}),Cn.queriedElements.forEach(Pn=>an(U,Pn,[]).push(zt)),Cn.preStyleProps.forEach((Pn,Co)=>{if(Pn.size){let $r=J.get(Co);$r||J.set(Co,$r=new Set),Pn.forEach((Xo,Ss)=>$r.add(Ss))}}),Cn.postStyleProps.forEach((Pn,Co)=>{let $r=ge.get(Co);$r||ge.set(Co,$r=new Set),Pn.forEach((Xo,Ss)=>$r.add(Ss))})});if(ui.length){const Ye=[];ui.forEach(lt=>{Ye.push(function _n(_,a){return new C.wOt(3505,!1)}())}),Gn.forEach(lt=>lt.destroy()),this.reportError(Ye)}const Wn=new Map,Er=new Map;L.forEach(Ye=>{const lt=Ye.element;y.has(lt)&&(Er.set(lt,lt),this._beforeAnimationBuild(Ye.player.namespaceId,Ye.instruction,Wn))}),M.forEach(Ye=>{const lt=Ye.element;this._getPreviousPlayers(lt,!1,Ye.namespaceId,Ye.triggerName,null).forEach(zt=>{an(Wn,lt,[]).push(zt),zt.destroy()})});const Nn=mn.filter(Ye=>Li(Ye,J,ge)),wi=new Map;zn(wi,this.driver,St,ge,X).forEach(Ye=>{Li(Ye,J,ge)&&Nn.push(Ye)});const Wo=new Map;wt.forEach((Ye,lt)=>{zn(Wo,this.driver,new Set(Ye),J,"!")}),Nn.forEach(Ye=>{const lt=wi.get(Ye),it=Wo.get(Ye);wi.set(Ye,new Map([...lt?.entries()??[],...it?.entries()??[]]))});const xr=[],Lo=[],cs={};L.forEach(Ye=>{const{element:lt,player:it,instruction:zt}=Ye;if(y.has(lt)){if(Ue.has(lt))return it.onDestroy(()=>$(lt,zt.toStyles)),it.disabled=!0,it.overrideTotalTime(zt.totalTime),void M.push(it);let Xe=cs;if(Er.size>1){let ci=lt;const Hi=[];for(;ci=ci.parentNode;){const Cn=Er.get(ci);if(Cn){Xe=Cn;break}Hi.push(ci)}Hi.forEach(Cn=>Er.set(Cn,Xe))}const bi=this._buildAnimation(it.namespaceId,zt,Wn,R,Wo,wi);if(it.setRealPlayer(bi),Xe===cs)xr.push(it);else{const ci=this.playersByElement.get(Xe);ci&&ci.length&&(it.parentPlayer=vt(ci)),M.push(it)}}else ue(lt,zt.fromStyles),it.onDestroy(()=>$(lt,zt.toStyles)),Lo.push(it),Ue.has(lt)&&M.push(it)}),Lo.forEach(Ye=>{const lt=R.get(Ye.element);if(lt&&lt.length){const it=vt(lt);Ye.setRealPlayer(it)}}),M.forEach(Ye=>{Ye.parentPlayer?Ye.syncPlayerEvents(Ye.parentPlayer):Ye.destroy()});for(let Ye=0;Ye<mn.length;Ye++){const lt=mn[Ye],it=lt[Vn];if(ki(lt,he),it&&it.hasAnimation)continue;let zt=[];if(U.size){let bi=U.get(lt);bi&&bi.length&&zt.push(...bi);let ci=this.driver.query(lt,pe,!0);for(let Hi=0;Hi<ci.length;Hi++){let Cn=U.get(ci[Hi]);Cn&&Cn.length&&zt.push(...Cn)}}const Xe=zt.filter(bi=>!bi.destroyed);Xe.length?Yn(this,lt,Xe):this.processLeaveNode(lt)}return mn.length=0,xr.forEach(Ye=>{this.players.push(Ye),Ye.onDone(()=>{Ye.destroy();const lt=this.players.indexOf(Ye);this.players.splice(lt,1)}),Ye.play()}),xr}afterFlush(a){this._flushFns.push(a)}afterFlushAnimationsDone(a){this._whenQuietFns.push(a)}_getPreviousPlayers(a,l,y,M,R){let L=[];if(l){const U=this.playersByQueriedElement.get(a);U&&(L=U)}else{const U=this.playersByElement.get(a);if(U){const J=!R||R==Jn;U.forEach(ge=>{ge.queued||!J&&ge.triggerName!=M||L.push(ge)})}}return(y||M)&&(L=L.filter(U=>!(y&&y!=U.namespaceId||M&&M!=U.triggerName))),L}_beforeAnimationBuild(a,l,y){const R=l.element,L=l.isRemovalTransition?void 0:a,U=l.isRemovalTransition?void 0:l.triggerName;for(const J of l.timelines){const ge=J.element,Ue=ge!==R,He=an(y,ge,[]);this._getPreviousPlayers(ge,Ue,L,U,l.toState).forEach(wt=>{const st=wt.getRealPlayer();st.beforeDestroy&&st.beforeDestroy(),wt.destroy(),He.push(wt)})}ue(R,l.fromStyles)}_buildAnimation(a,l,y,M,R,L){const U=l.triggerName,J=l.element,ge=[],Ue=new Set,He=new Set,Ft=l.timelines.map(st=>{const at=st.element;Ue.add(at);const mn=at[Vn];if(mn&&mn.removedBeforeQueried)return new Rt(st.duration,st.delay);const jn=at!==J,St=function or(_){const a=[];return eo(_,a),a}((y.get(at)||oi).map(Wn=>Wn.getRealPlayer())).filter(Wn=>!!Wn.element&&Wn.element===at),ar=R.get(at),Gt=L.get(at),Gn=ht(this._normalizer,st.keyframes,ar,Gt),ui=this._buildPlayer(st,Gn,St);if(st.subTimeline&&M&&He.add(at),jn){const Wn=new Ot(a,U,at);Wn.setRealPlayer(ui),ge.push(Wn)}return ui});ge.forEach(st=>{an(this.playersByQueriedElement,st.element,[]).push(st),st.onDone(()=>function Ho(_,a,l){let y=_.get(a);if(y){if(y.length){const M=y.indexOf(l);y.splice(M,1)}0==y.length&&_.delete(a)}return y}(this.playersByQueriedElement,st.element,st))}),Ue.forEach(st=>Lt(st,W));const wt=vt(Ft);return wt.onDestroy(()=>{Ue.forEach(st=>ki(st,W)),$(J,l.toStyles)}),He.forEach(st=>{an(M,st,[]).push(wt)}),wt}_buildPlayer(a,l,y){return l.length>0?this.driver.animate(a.element,l,a.duration,a.delay,a.easing,y):new Rt(a.duration,a.delay)}}class Ot{constructor(a,l,y){this.namespaceId=a,this.triggerName=l,this.element=y,this._player=new Rt,this._containsRealPlayer=!1,this._queuedCallbacks=new Map,this.destroyed=!1,this.parentPlayer=null,this.markedForDestroy=!1,this.disabled=!1,this.queued=!0,this.totalTime=0}setRealPlayer(a){this._containsRealPlayer||(this._player=a,this._queuedCallbacks.forEach((l,y)=>{l.forEach(M=>nr(a,y,void 0,M))}),this._queuedCallbacks.clear(),this._containsRealPlayer=!0,this.overrideTotalTime(a.totalTime),this.queued=!1)}getRealPlayer(){return this._player}overrideTotalTime(a){this.totalTime=a}syncPlayerEvents(a){const l=this._player;l.triggerCallback&&a.onStart(()=>l.triggerCallback("start")),a.onDone(()=>this.finish()),a.onDestroy(()=>this.destroy())}_queueEvent(a,l){an(this._queuedCallbacks,a,[]).push(l)}onDone(a){this.queued&&this._queueEvent("done",a),this._player.onDone(a)}onStart(a){this.queued&&this._queueEvent("start",a),this._player.onStart(a)}onDestroy(a){this.queued&&this._queueEvent("destroy",a),this._player.onDestroy(a)}init(){this._player.init()}hasStarted(){return!this.queued&&this._player.hasStarted()}play(){!this.queued&&this._player.play()}pause(){!this.queued&&this._player.pause()}restart(){!this.queued&&this._player.restart()}finish(){this._player.finish()}destroy(){this.destroyed=!0,this._player.destroy()}reset(){!this.queued&&this._player.reset()}setPosition(a){this.queued||this._player.setPosition(a)}getPosition(){return this.queued?0:this._player.getPosition()}triggerCallback(a){const l=this._player;l.triggerCallback&&l.triggerCallback(a)}}function si(_){return _&&1===_.nodeType}function ai(_,a){const l=_.style.display;return _.style.display=a??"none",l}function zn(_,a,l,y,M){const R=[];l.forEach(J=>R.push(ai(J)));const L=[];y.forEach((J,ge)=>{const Ue=new Map;J.forEach(He=>{const Ft=a.computeStyle(ge,He,M);Ue.set(He,Ft),(!Ft||0==Ft.length)&&(ge[Vn]=hr,L.push(ge))}),_.set(ge,Ue)});let U=0;return l.forEach(J=>ai(J,R[U++])),L}function Fi(_,a){const l=new Map;if(_.forEach(U=>l.set(U,[])),0==a.length)return l;const M=new Set(a),R=new Map;function L(U){if(!U)return 1;let J=R.get(U);if(J)return J;const ge=U.parentNode;return J=l.has(ge)?ge:M.has(ge)?1:L(ge),R.set(U,J),J}return a.forEach(U=>{const J=L(U);1!==J&&l.get(J).push(U)}),l}function Lt(_,a){_.classList?.add(a)}function ki(_,a){_.classList?.remove(a)}function Yn(_,a,l){vt(l).onDone(()=>_.processLeaveNode(a))}function eo(_,a){for(let l=0;l<_.length;l++){const y=_[l];y instanceof yt?eo(y.players,a):a.push(y)}}function Li(_,a,l){const y=l.get(_);if(!y)return!1;let M=a.get(_);return M?y.forEach(R=>M.add(R)):a.set(_,y),l.delete(_),!0}class pr{constructor(a,l,y){this.bodyNode=a,this._driver=l,this._normalizer=y,this._triggerCache={},this.onRemovalComplete=(M,R)=>{},this._transitionEngine=new xi(a,l,y),this._timelineEngine=new Or(a,l,y),this._transitionEngine.onRemovalComplete=(M,R)=>this.onRemovalComplete(M,R)}registerTrigger(a,l,y,M,R){const L=a+"-"+M;let U=this._triggerCache[L];if(!U){const J=[],Ue=yr(this._driver,R,J,[]);if(J.length)throw function Bt(_,a){return new C.wOt(3404,!1)}();U=function Zn(_,a,l){return new Qr(_,a,l)}(M,Ue,this._normalizer),this._triggerCache[L]=U}this._transitionEngine.registerTrigger(l,M,U)}register(a,l){this._transitionEngine.register(a,l)}destroy(a,l){this._transitionEngine.destroy(a,l)}onInsert(a,l,y,M){this._transitionEngine.insertNode(a,l,y,M)}onRemove(a,l,y){this._transitionEngine.removeNode(a,l,y)}disableAnimations(a,l){this._transitionEngine.markElementAsDisabled(a,l)}process(a,l,y,M){if("@"==y.charAt(0)){const[R,L]=En(y);this._timelineEngine.command(R,l,L,M)}else this._transitionEngine.trigger(a,l,y,M)}listen(a,l,y,M,R){if("@"==y.charAt(0)){const[L,U]=En(y);return this._timelineEngine.listen(L,l,U,R)}return this._transitionEngine.listen(a,l,y,M,R)}flush(a=-1){this._transitionEngine.flush(a)}get players(){return[...this._transitionEngine.players,...this._timelineEngine.players]}whenRenderingDone(){return this._transitionEngine.whenRenderingDone()}afterFlushAnimationsDone(a){this._transitionEngine.afterFlushAnimationsDone(a)}}let en=(()=>{class _{static{this.initialStylesByElement=new WeakMap}constructor(l,y,M){this._element=l,this._startStyles=y,this._endStyles=M,this._state=0;let R=_.initialStylesByElement.get(l);R||_.initialStylesByElement.set(l,R=new Map),this._initialStyles=R}start(){this._state<1&&(this._startStyles&&$(this._element,this._startStyles,this._initialStyles),this._state=1)}finish(){this.start(),this._state<2&&($(this._element,this._initialStyles),this._endStyles&&($(this._element,this._endStyles),this._endStyles=null),this._state=1)}destroy(){this.finish(),this._state<3&&(_.initialStylesByElement.delete(this._element),this._startStyles&&(ue(this._element,this._startStyles),this._endStyles=null),this._endStyles&&(ue(this._element,this._endStyles),this._endStyles=null),$(this._element,this._initialStyles),this._state=3)}}return _})();function mo(_){let a=null;return _.forEach((l,y)=>{(function es(_){return"display"===_||"position"===_})(y)&&(a=a||new Map,a.set(y,l))}),a}class b{constructor(a,l,y,M){this.element=a,this.keyframes=l,this.options=y,this._specialStyles=M,this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._initialized=!1,this._finished=!1,this._started=!1,this._destroyed=!1,this._originalOnDoneFns=[],this._originalOnStartFns=[],this.time=0,this.parentPlayer=null,this.currentSnapshot=new Map,this._duration=y.duration,this._delay=y.delay||0,this.time=this._duration+this._delay}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(a=>a()),this._onDoneFns=[])}init(){this._buildPlayer(),this._preparePlayerBeforeStart()}_buildPlayer(){if(this._initialized)return;this._initialized=!0;const a=this.keyframes;this.domPlayer=this._triggerWebAnimation(this.element,a,this.options),this._finalKeyframe=a.length?a[a.length-1]:new Map;const l=()=>this._onFinish();this.domPlayer.addEventListener("finish",l),this.onDestroy(()=>{this.domPlayer.removeEventListener("finish",l)})}_preparePlayerBeforeStart(){this._delay?this._resetDomPlayerState():this.domPlayer.pause()}_convertKeyframesToObject(a){const l=[];return a.forEach(y=>{l.push(Object.fromEntries(y))}),l}_triggerWebAnimation(a,l,y){return a.animate(this._convertKeyframesToObject(l),y)}onStart(a){this._originalOnStartFns.push(a),this._onStartFns.push(a)}onDone(a){this._originalOnDoneFns.push(a),this._onDoneFns.push(a)}onDestroy(a){this._onDestroyFns.push(a)}play(){this._buildPlayer(),this.hasStarted()||(this._onStartFns.forEach(a=>a()),this._onStartFns=[],this._started=!0,this._specialStyles&&this._specialStyles.start()),this.domPlayer.play()}pause(){this.init(),this.domPlayer.pause()}finish(){this.init(),this._specialStyles&&this._specialStyles.finish(),this._onFinish(),this.domPlayer.finish()}reset(){this._resetDomPlayerState(),this._destroyed=!1,this._finished=!1,this._started=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}_resetDomPlayerState(){this.domPlayer&&this.domPlayer.cancel()}restart(){this.reset(),this.play()}hasStarted(){return this._started}destroy(){this._destroyed||(this._destroyed=!0,this._resetDomPlayerState(),this._onFinish(),this._specialStyles&&this._specialStyles.destroy(),this._onDestroyFns.forEach(a=>a()),this._onDestroyFns=[])}setPosition(a){void 0===this.domPlayer&&this.init(),this.domPlayer.currentTime=a*this.time}getPosition(){return+(this.domPlayer.currentTime??0)/this.time}get totalTime(){return this._delay+this._duration}beforeDestroy(){const a=new Map;this.hasStarted()&&this._finalKeyframe.forEach((y,M)=>{"offset"!==M&&a.set(M,this._finished?y:Je(this.element,M))}),this.currentSnapshot=a}triggerCallback(a){const l="start"===a?this._onStartFns:this._onDoneFns;l.forEach(y=>y()),l.length=0}}class S{validateStyleProperty(a){return!0}validateAnimatableStyleProperty(a){return!0}matchesElement(a,l){return!1}containsElement(a,l){return _t(a,l)}getParentElement(a){return Xt(a)}query(a,l,y){return dt(a,l,y)}computeStyle(a,l,y){return window.getComputedStyle(a)[l]}animate(a,l,y,M,R,L=[]){const J={duration:y,delay:M,fill:0==M?"both":"forwards"};R&&(J.easing=R);const ge=new Map,Ue=L.filter(wt=>wt instanceof b);(function Tr(_,a){return 0===_||0===a})(y,M)&&Ue.forEach(wt=>{wt.currentSnapshot.forEach((st,at)=>ge.set(at,st))});let He=function kn(_){return _.length?_[0]instanceof Map?_:_.map(a=>Bn(a)):[]}(l).map(wt=>j(wt));He=function wn(_,a,l){if(l.size&&a.length){let y=a[0],M=[];if(l.forEach((R,L)=>{y.has(L)||M.push(L),y.set(L,R)}),M.length)for(let R=1;R<a.length;R++){let L=a[R];M.forEach(U=>L.set(U,Je(_,U)))}}return a}(a,He,ge);const Ft=function Di(_,a){let l=null,y=null;return Array.isArray(a)&&a.length?(l=mo(a[0]),a.length>1&&(y=mo(a[a.length-1]))):a instanceof Map&&(l=mo(a)),l||y?new en(_,l,y):null}(a,He);return new b(a,He,J,Ft)}}var v=O(177);let P=(()=>{class _ extends ce{constructor(l,y){super(),this._nextAnimationId=0,this._renderer=l.createRenderer(y.body,{id:"0",encapsulation:C.gXe.None,styles:[],data:{animation:[]}})}build(l){const y=this._nextAnimationId.toString();this._nextAnimationId++;const M=Array.isArray(l)?le(l):l;return Ct(this._renderer,null,y,"register",[M]),new B(y,this._renderer)}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(C._9s),C.KVO(v.qQ))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})();class B extends se{constructor(a,l){super(),this._id=a,this._renderer=l}create(a,l){return new ze(this._id,a,l||{},this._renderer)}}class ze{constructor(a,l,y,M){this.id=a,this.element=l,this._renderer=M,this.parentPlayer=null,this._started=!1,this.totalTime=0,this._command("create",y)}_listen(a,l){return this._renderer.listen(this.element,`@@${this.id}:${a}`,l)}_command(a,...l){return Ct(this._renderer,this.element,this.id,a,l)}onDone(a){this._listen("done",a)}onStart(a){this._listen("start",a)}onDestroy(a){this._listen("destroy",a)}init(){this._command("init")}hasStarted(){return this._started}play(){this._command("play"),this._started=!0}pause(){this._command("pause")}restart(){this._command("restart")}finish(){this._command("finish")}destroy(){this._command("destroy")}reset(){this._command("reset"),this._started=!1}setPosition(a){this._command("setPosition",a)}getPosition(){return this._renderer.engine.players[+this.id]?.getPosition()??0}}function Ct(_,a,l,y,M){return _.setProperty(a,`@@${l}:${y}`,M)}const Vi="@.disabled";let Ci=(()=>{class _{constructor(l,y,M){this.delegate=l,this.engine=y,this._zone=M,this._currentId=0,this._microtaskId=1,this._animationCallbacksBuffer=[],this._rendererCache=new Map,this._cdRecurDepth=0,y.onRemovalComplete=(R,L)=>{const U=L?.parentNode(R);U&&L.removeChild(U,R)}}createRenderer(l,y){const R=this.delegate.createRenderer(l,y);if(!(l&&y&&y.data&&y.data.animation)){let Ue=this._rendererCache.get(R);return Ue||(Ue=new ji("",R,this.engine,()=>this._rendererCache.delete(R)),this._rendererCache.set(R,Ue)),Ue}const L=y.id,U=y.id+"-"+this._currentId;this._currentId++,this.engine.register(U,l);const J=Ue=>{Array.isArray(Ue)?Ue.forEach(J):this.engine.registerTrigger(L,U,l,Ue.name,Ue)};return y.data.animation.forEach(J),new Ui(this,U,R,this.engine)}begin(){this._cdRecurDepth++,this.delegate.begin&&this.delegate.begin()}_scheduleCountTask(){queueMicrotask(()=>{this._microtaskId++})}scheduleListenerCallback(l,y,M){l>=0&&l<this._microtaskId?this._zone.run(()=>y(M)):(0==this._animationCallbacksBuffer.length&&queueMicrotask(()=>{this._zone.run(()=>{this._animationCallbacksBuffer.forEach(R=>{const[L,U]=R;L(U)}),this._animationCallbacksBuffer=[]})}),this._animationCallbacksBuffer.push([y,M]))}end(){this._cdRecurDepth--,0==this._cdRecurDepth&&this._zone.runOutsideAngular(()=>{this._scheduleCountTask(),this.engine.flush(this._microtaskId)}),this.delegate.end&&this.delegate.end()}whenRenderingDone(){return this.engine.whenRenderingDone()}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(C._9s),C.KVO(pr),C.KVO(C.SKi))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})();class ji{constructor(a,l,y,M){this.namespaceId=a,this.delegate=l,this.engine=y,this._onDestroy=M}get data(){return this.delegate.data}destroyNode(a){this.delegate.destroyNode?.(a)}destroy(){this.engine.destroy(this.namespaceId,this.delegate),this.engine.afterFlushAnimationsDone(()=>{queueMicrotask(()=>{this.delegate.destroy()})}),this._onDestroy?.()}createElement(a,l){return this.delegate.createElement(a,l)}createComment(a){return this.delegate.createComment(a)}createText(a){return this.delegate.createText(a)}appendChild(a,l){this.delegate.appendChild(a,l),this.engine.onInsert(this.namespaceId,l,a,!1)}insertBefore(a,l,y,M=!0){this.delegate.insertBefore(a,l,y),this.engine.onInsert(this.namespaceId,l,a,M)}removeChild(a,l,y){this.engine.onRemove(this.namespaceId,l,this.delegate)}selectRootElement(a,l){return this.delegate.selectRootElement(a,l)}parentNode(a){return this.delegate.parentNode(a)}nextSibling(a){return this.delegate.nextSibling(a)}setAttribute(a,l,y,M){this.delegate.setAttribute(a,l,y,M)}removeAttribute(a,l,y){this.delegate.removeAttribute(a,l,y)}addClass(a,l){this.delegate.addClass(a,l)}removeClass(a,l){this.delegate.removeClass(a,l)}setStyle(a,l,y,M){this.delegate.setStyle(a,l,y,M)}removeStyle(a,l,y){this.delegate.removeStyle(a,l,y)}setProperty(a,l,y){"@"==l.charAt(0)&&l==Vi?this.disableAnimations(a,!!y):this.delegate.setProperty(a,l,y)}setValue(a,l){this.delegate.setValue(a,l)}listen(a,l,y){return this.delegate.listen(a,l,y)}disableAnimations(a,l){this.engine.disableAnimations(a,l)}}class Ui extends ji{constructor(a,l,y,M,R){super(l,y,M,R),this.factory=a,this.namespaceId=l}setProperty(a,l,y){"@"==l.charAt(0)?"."==l.charAt(1)&&l==Vi?this.disableAnimations(a,y=void 0===y||!!y):this.engine.process(this.namespaceId,a,l.slice(1),y):this.delegate.setProperty(a,l,y)}listen(a,l,y){if("@"==l.charAt(0)){const M=function li(_){switch(_){case"body":return document.body;case"document":return document;case"window":return window;default:return _}}(a);let R=l.slice(1),L="";return"@"!=R.charAt(0)&&([R,L]=function yo(_){const a=_.indexOf(".");return[_.substring(0,a),_.slice(a+1)]}(R)),this.engine.listen(this.namespaceId,M,R,L,U=>{this.factory.scheduleListenerCallback(U._data||-1,y,U)})}return this.delegate.listen(a,l,y)}}const zo=[{provide:ce,useClass:P},{provide:bn,useFactory:function ns(){return new Ri}},{provide:pr,useClass:(()=>{class _ extends pr{constructor(l,y,M,R){super(l.body,y,M)}ngOnDestroy(){this.flush()}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(v.qQ),C.KVO(Xn),C.KVO(bn),C.KVO(C.o8S))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})()},{provide:C._9s,useFactory:function rs(_,a,l){return new Ci(_,a,l)},deps:[c.B7,pr,C.SKi]}],is=[{provide:Xn,useFactory:()=>new S},{provide:C.bc$,useValue:"BrowserAnimations"},...zo],Bi=[{provide:Xn,useClass:Kn},{provide:C.bc$,useValue:"NoopAnimations"},...zo];let _i=(()=>{class _{static withConfig(l){return{ngModule:_,providers:l.disableAnimations?Bi:is}}static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275mod=C.$C({type:_})}static{this.\u0275inj=C.G2t({providers:is,imports:[c.Bb]})}}return _})();var $i=O(1626),_r=O(4341),gr=O(2434),Jr=O(4978);const os=[{path:"",redirectTo:"/auth/login",pathMatch:"full"},{path:"auth",loadChildren:()=>O.e(395).then(O.bind(O,8395)).then(_=>_.AuthModule)},{path:"patient",canActivate:[Jr.q],data:{roles:["PATIENT"]},loadChildren:()=>Promise.all([O.e(572),O.e(713),O.e(76),O.e(340)]).then(O.bind(O,340)).then(_=>_.PatientModule)},{path:"doctor",canActivate:[Jr.q],data:{roles:["DOCTOR"]},loadChildren:()=>Promise.all([O.e(572),O.e(713),O.e(76),O.e(561)]).then(O.bind(O,9561)).then(_=>_.DoctorModule)},{path:"profile",canActivate:[Jr.q],loadChildren:()=>O.e(809).then(O.bind(O,5809)).then(_=>_.ProfileModule)},{path:"appointments",canActivate:[Jr.q],loadChildren:()=>Promise.all([O.e(572),O.e(76),O.e(402)]).then(O.bind(O,5402)).then(_=>_.AppointmentsModule)},{path:"chat",canActivate:[Jr.q],loadChildren:()=>Promise.all([O.e(572),O.e(713)]).then(O.bind(O,1713)).then(_=>_.ChatModule)},{path:"ai-health-bot",canActivate:[Jr.q],data:{roles:["PATIENT"]},loadChildren:()=>O.e(860).then(O.bind(O,6860)).then(_=>_.AiHealthBotModule)},{path:"telemedicine",canActivate:[Jr.q],loadChildren:()=>Promise.all([O.e(572),O.e(76),O.e(614)]).then(O.bind(O,614)).then(_=>_.TelemedicineModule)},{path:"debug",canActivate:[Jr.q],loadChildren:()=>Promise.all([O.e(572),O.e(453)]).then(O.bind(O,3453)).then(_=>_.DebugModule)},{path:"**",redirectTo:"/auth/login"}];let ss=(()=>{class _{static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275mod=C.$C({type:_})}static{this.\u0275inj=C.G2t({imports:[gr.iI.forRoot(os),gr.iI]})}}return _})();var xo=O(5964),uo=O(8010),Fo=O(5567);function co(_,a){if(1&_&&(C.j41(0,"span",16),C.EFF(1),C.k0s()),2&_){const l=C.XpG();C.R7$(1),C.SpI(" ",l.unreadCount>99?"99+":l.unreadCount," ")}}function Go(_,a){if(1&_){const l=C.RV6();C.j41(0,"button",17),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.markAllAsRead())}),C.EFF(1," Mark all read "),C.k0s()}}function as(_,a){if(1&_){const l=C.RV6();C.j41(0,"button",18),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.clearAll())}),C.EFF(1," Clear all "),C.k0s()}}function ls(_,a){1&_&&(C.j41(0,"div",19)(1,"div",20)(2,"span",21),C.EFF(3,"Loading..."),C.k0s()()())}function ko(_,a){1&_&&(C.j41(0,"div",22)(1,"div",23),C.nrm(2,"i",24),C.j41(3,"p",25),C.EFF(4,"No notifications"),C.k0s()()())}function vo(_,a){if(1&_&&C.nrm(0,"img",41),2&_){const l=C.XpG(2).$implicit;C.Y8G("src",l.fromUser.avatar,C.B4B)("alt",l.fromUser.name)}}function Ei(_,a){if(1&_&&(C.j41(0,"div",38),C.DNE(1,vo,1,2,"img",39),C.j41(2,"span",40),C.EFF(3),C.k0s()()),2&_){const l=C.XpG().$implicit;C.R7$(1),C.Y8G("ngIf",l.fromUser.avatar),C.R7$(2),C.JRh(l.fromUser.name)}}function us(_,a){1&_&&C.nrm(0,"div",42)}function pa(_,a){if(1&_){const l=C.RV6();C.j41(0,"div",26),C.bIt("click",function(){const R=C.eBV(l).$implicit,L=C.XpG();return C.Njj(L.handleNotificationClick(R))}),C.j41(1,"div",27)(2,"div",28),C.nrm(3,"i"),C.k0s(),C.j41(4,"div",29)(5,"div",30),C.EFF(6),C.k0s(),C.j41(7,"div",31),C.EFF(8),C.k0s(),C.DNE(9,Ei,4,2,"div",32),C.j41(10,"div",33),C.EFF(11),C.k0s()(),C.j41(12,"div",34)(13,"button",35),C.bIt("click",function(M){const L=C.eBV(l).$implicit,U=C.XpG();return C.Njj(U.removeNotification(L,M))}),C.nrm(14,"i",36),C.k0s()()(),C.DNE(15,us,1,0,"div",37),C.k0s()}if(2&_){const l=a.$implicit,y=C.XpG();C.HbH(y.getNotificationClass(l)),C.R7$(3),C.HbH(y.getNotificationIcon(l.type)),C.R7$(3),C.JRh(l.title),C.R7$(2),C.JRh(l.message),C.R7$(1),C.Y8G("ngIf",l.fromUser),C.R7$(2),C.JRh(y.formatTime(l.timestamp)),C.R7$(4),C.Y8G("ngIf",!l.read)}}function ga(_,a){1&_&&(C.j41(0,"div",43)(1,"a",44),C.EFF(2," View All Notifications "),C.k0s()())}let ma=(()=>{class _{constructor(l,y){this.notificationService=l,this.router=y,this.notifications=[],this.unreadCount=0,this.showDropdown=!1,this.loading=!1,this.subscriptions=[]}ngOnInit(){const l=this.notificationService.getNotifications().subscribe(M=>{this.notifications=M.slice(0,10)});this.subscriptions.push(l);const y=this.notificationService.getUnreadCount().subscribe(M=>this.unreadCount=M);this.subscriptions.push(y),this.notificationService.requestNotificationPermission()}ngOnDestroy(){this.subscriptions.forEach(l=>l.unsubscribe())}toggleDropdown(){this.showDropdown=!this.showDropdown}closeDropdown(){this.showDropdown=!1}markAsRead(l){l.read||this.notificationService.markAsRead(l.id)}markAllAsRead(){this.notificationService.markAllAsRead()}handleNotificationClick(l){this.markAsRead(l),l.actionUrl&&this.router.navigate([l.actionUrl]),this.closeDropdown()}removeNotification(l,y){y.stopPropagation(),this.notificationService.removeNotification(l.id)}clearAll(){this.notificationService.clearAll()}getNotificationIcon(l){switch(l){case"message":return"fas fa-comment";case"appointment":return"fas fa-calendar";case"urgent":return"fas fa-exclamation-triangle";case"system":return"fas fa-cog";default:return"fas fa-bell"}}getNotificationClass(l){const y=["notification-item"];return l.read||y.push("unread"),y.push(`priority-${l.priority}`),y.join(" ")}formatTime(l){const M=(new Date).getTime()-l.getTime(),R=Math.floor(M/6e4),L=Math.floor(R/60),U=Math.floor(L/24);return R<1?"Just now":R<60?`${R}m ago`:L<24?`${L}h ago`:U<7?`${U}d ago`:l.toLocaleDateString()}trackByNotificationId(l,y){return y.id}static{this.\u0275fac=function(y){return new(y||_)(C.rXU(Fo.J),C.rXU(gr.Ix))}}static{this.\u0275cmp=C.VBU({type:_,selectors:[["app-notification-bell"]],decls:17,vars:12,consts:[[1,"notification-bell",3,"clickOutside"],["type","button",1,"btn","btn-link","notification-trigger",3,"click"],[1,"fas","fa-bell"],["class","badge bg-danger notification-badge",4,"ngIf"],[1,"notification-dropdown",3,"click"],[1,"dropdown-header"],[1,"d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"dropdown-actions"],["type","button","class","btn btn-sm btn-link text-primary",3,"click",4,"ngIf"],["type","button","class","btn btn-sm btn-link text-danger",3,"click",4,"ngIf"],[1,"notifications-list"],["class","text-center py-3",4,"ngIf"],["class","no-notifications",4,"ngIf"],[3,"class","click",4,"ngFor","ngForOf","ngForTrackBy"],["class","dropdown-footer",4,"ngIf"],[1,"badge","bg-danger","notification-badge"],["type","button",1,"btn","btn-sm","btn-link","text-primary",3,"click"],["type","button",1,"btn","btn-sm","btn-link","text-danger",3,"click"],[1,"text-center","py-3"],["role","status",1,"spinner-border","spinner-border-sm"],[1,"visually-hidden"],[1,"no-notifications"],[1,"text-center","py-4"],[1,"fas","fa-bell-slash","fa-2x","text-muted","mb-2"],[1,"text-muted","mb-0"],[3,"click"],[1,"notification-content"],[1,"notification-icon"],[1,"notification-body"],[1,"notification-title"],[1,"notification-message"],["class","notification-from",4,"ngIf"],[1,"notification-time"],[1,"notification-actions"],["type","button","title","Remove notification",1,"btn","btn-sm","btn-link","text-muted",3,"click"],[1,"fas","fa-times"],["class","unread-indicator",4,"ngIf"],[1,"notification-from"],["class","from-avatar",3,"src","alt",4,"ngIf"],[1,"from-name"],[1,"from-avatar",3,"src","alt"],[1,"unread-indicator"],[1,"dropdown-footer"],["routerLink","/notifications",1,"btn","btn-sm","btn-outline-primary","w-100"]],template:function(y,M){1&y&&(C.j41(0,"div",0),C.bIt("clickOutside",function(){return M.closeDropdown()}),C.j41(1,"button",1),C.bIt("click",function(){return M.toggleDropdown()}),C.nrm(2,"i",2),C.DNE(3,co,2,1,"span",3),C.k0s(),C.j41(4,"div",4),C.bIt("click",function(L){return L.stopPropagation()}),C.j41(5,"div",5)(6,"div",6)(7,"h6",7),C.EFF(8,"Notifications"),C.k0s(),C.j41(9,"div",8),C.DNE(10,Go,2,0,"button",9),C.DNE(11,as,2,0,"button",10),C.k0s()()(),C.j41(12,"div",11),C.DNE(13,ls,4,0,"div",12),C.DNE(14,ko,5,0,"div",13),C.DNE(15,pa,16,9,"div",14),C.k0s(),C.DNE(16,ga,3,0,"div",15),C.k0s()()),2&y&&(C.R7$(1),C.AVh("has-notifications",M.unreadCount>0),C.R7$(2),C.Y8G("ngIf",M.unreadCount>0),C.R7$(1),C.AVh("show",M.showDropdown),C.R7$(6),C.Y8G("ngIf",M.unreadCount>0),C.R7$(1),C.Y8G("ngIf",M.notifications.length>0),C.R7$(2),C.Y8G("ngIf",M.loading),C.R7$(1),C.Y8G("ngIf",!M.loading&&0===M.notifications.length),C.R7$(1),C.Y8G("ngForOf",M.notifications)("ngForTrackBy",M.trackByNotificationId),C.R7$(1),C.Y8G("ngIf",M.notifications.length>0))},dependencies:[v.Sq,v.bT,gr.Wk],styles:[".notification-bell[_ngcontent-%COMP%]{position:relative;display:inline-block}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]{position:relative;padding:.5rem;color:#6c757d;border:none;background:none;font-size:1.25rem;transition:color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]:hover{color:#495057}.notification-bell[_ngcontent-%COMP%]   .notification-trigger.has-notifications[_ngcontent-%COMP%]{color:#007bff;animation:_ngcontent-%COMP%_bellShake 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-trigger[_ngcontent-%COMP%]   .notification-badge[_ngcontent-%COMP%]{position:absolute;top:0;right:0;font-size:.75rem;min-width:18px;height:18px;border-radius:9px;display:flex;align-items:center;justify-content:center;transform:translate(25%,-25%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{position:absolute;top:100%;right:0;width:380px;max-height:500px;background:white;border:1px solid #dee2e6;border-radius:8px;box-shadow:0 4px 12px #00000026;z-index:1050;opacity:0;visibility:hidden;transform:translateY(-10px);transition:all .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown.show[_ngcontent-%COMP%]{opacity:1;visibility:visible;transform:translateY(0)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]{padding:1rem;border-bottom:1px solid #dee2e6;background:#f8f9fa;border-radius:8px 8px 0 0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#495057;font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.875rem;text-decoration:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-header[_ngcontent-%COMP%]   .dropdown-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{text-decoration:underline}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]{max-height:350px;overflow-y:auto}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]{position:relative;padding:1rem;border-bottom:1px solid #f1f3f4;cursor:pointer;transition:background-color .2s ease}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]{background-color:#f0f8ff;border-left:3px solid #007bff}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.unread[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-weight:600}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]{border-left-color:#dc3545}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-urgent[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#dc3545;animation:_ngcontent-%COMP%_pulse 2s infinite}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item.priority-high[_ngcontent-%COMP%]{border-left-color:#fd7e14}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-content[_ngcontent-%COMP%]{display:flex;align-items:flex-start;gap:.75rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]{flex-shrink:0;width:32px;height:32px;border-radius:50%;background:#e9ecef;display:flex;align-items:center;justify-content:center}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]{flex:1;min-width:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-title[_ngcontent-%COMP%]{font-size:.875rem;color:#212529;margin-bottom:.25rem;line-height:1.4}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-message[_ngcontent-%COMP%]{font-size:.8125rem;color:#6c757d;line-height:1.4;margin-bottom:.5rem;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;margin-bottom:.25rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-avatar[_ngcontent-%COMP%]{width:20px;height:20px;border-radius:50%;object-fit:cover}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-from[_ngcontent-%COMP%]   .from-name[_ngcontent-%COMP%]{font-size:.75rem;color:#495057;font-weight:500}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-body[_ngcontent-%COMP%]   .notification-time[_ngcontent-%COMP%]{font-size:.75rem;color:#adb5bd}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]{flex-shrink:0}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{padding:.25rem;border:none;background:none}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .notification-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover{background:#e9ecef;border-radius:4px}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .notification-item[_ngcontent-%COMP%]   .unread-indicator[_ngcontent-%COMP%]{position:absolute;top:50%;right:.5rem;width:8px;height:8px;background:#007bff;border-radius:50%;transform:translateY(-50%)}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .notifications-list[_ngcontent-%COMP%]   .no-notifications[_ngcontent-%COMP%]{padding:2rem 1rem}.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]   .dropdown-footer[_ngcontent-%COMP%]{padding:.75rem 1rem;border-top:1px solid #dee2e6;background:#f8f9fa;border-radius:0 0 8px 8px}@keyframes _ngcontent-%COMP%_bellShake{0%,50%,to{transform:rotate(0)}10%,30%{transform:rotate(-10deg)}20%,40%{transform:rotate(10deg)}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}@media (max-width: 768px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:320px;right:-50px}}@media (max-width: 576px){.notification-bell[_ngcontent-%COMP%]   .notification-dropdown[_ngcontent-%COMP%]{width:280px;right:-100px}}"]})}}return _})();var Do=O(3794),bs=O(6977),h=O(7436);function T(_,a){if(1&_){const l=C.RV6();C.j41(0,"li")(1,"a",8),C.bIt("click",function(M){const L=C.eBV(l).$implicit;return C.XpG().onLanguageChange(L.code),C.Njj(M.preventDefault())}),C.j41(2,"span",3),C.EFF(3),C.k0s(),C.j41(4,"span",9),C.EFF(5),C.k0s(),C.j41(6,"span",10),C.EFF(7),C.k0s()()()}if(2&_){const l=a.$implicit,y=C.XpG();C.R7$(1),C.AVh("active",l.code===y.currentLanguage),C.R7$(2),C.JRh(l.flag),C.R7$(2),C.JRh(l.name),C.R7$(2),C.SpI("(",l.code.toUpperCase(),")")}}let D=(()=>{class _{constructor(l){this.i18nService=l,this.destroy$=new Do.B,this.currentLanguage="en",this.supportedLanguages=[],this.isDropdownOpen=!1}ngOnInit(){this.supportedLanguages=this.i18nService.getSupportedLanguages(),this.i18nService.getCurrentLanguage().pipe((0,bs.Q)(this.destroy$)).subscribe(l=>{this.currentLanguage=l})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}onLanguageChange(l){this.i18nService.setLanguage(l),this.isDropdownOpen=!1}toggleDropdown(){this.isDropdownOpen=!this.isDropdownOpen}getCurrentLanguageInfo(){return this.supportedLanguages.find(l=>l.code===this.currentLanguage)}onClickOutside(){this.isDropdownOpen=!1}static{this.\u0275fac=function(y){return new(y||_)(C.rXU(h.s))}}static{this.\u0275cmp=C.VBU({type:_,selectors:[["app-language-selector"]],decls:11,vars:7,consts:[[1,"language-selector",3,"clickOutside"],[1,"dropdown"],["type","button",1,"btn","btn-outline-secondary","dropdown-toggle","language-btn",3,"click"],[1,"flag"],[1,"language-name","d-none","d-md-inline"],[1,"language-code","d-md-none"],[1,"dropdown-menu"],[4,"ngFor","ngForOf"],["href","#",1,"dropdown-item",3,"click"],[1,"language-name"],[1,"language-code"]],template:function(y,M){if(1&y&&(C.j41(0,"div",0),C.bIt("clickOutside",function(){return M.onClickOutside()}),C.j41(1,"div",1)(2,"button",2),C.bIt("click",function(){return M.toggleDropdown()}),C.j41(3,"span",3),C.EFF(4),C.k0s(),C.j41(5,"span",4),C.EFF(6),C.k0s(),C.j41(7,"span",5),C.EFF(8),C.k0s()(),C.j41(9,"ul",6),C.DNE(10,T,8,5,"li",7),C.k0s()()()),2&y){let R,L;C.R7$(2),C.BMQ("aria-expanded",M.isDropdownOpen),C.R7$(2),C.JRh((null==(R=M.getCurrentLanguageInfo())?null:R.flag)||"\u{1f310}"),C.R7$(2),C.JRh((null==(L=M.getCurrentLanguageInfo())?null:L.name)||"Language"),C.R7$(2),C.JRh(M.currentLanguage.toUpperCase()),C.R7$(1),C.AVh("show",M.isDropdownOpen),C.R7$(1),C.Y8G("ngForOf",M.supportedLanguages)}},dependencies:[v.Sq],styles:[".language-selector[_ngcontent-%COMP%]{position:relative}.language-btn[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;border:1px solid #dee2e6;background:white;color:#495057;padding:.375rem .75rem;border-radius:.375rem;font-size:.875rem;transition:all .15s ease-in-out}.language-btn[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#adb5bd}.language-btn[_ngcontent-%COMP%]:focus{outline:0;box-shadow:0 0 0 .2rem #007bff40}.flag[_ngcontent-%COMP%]{font-size:1.1em;line-height:1}.language-name[_ngcontent-%COMP%]{font-weight:500}.language-code[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d}.dropdown-menu[_ngcontent-%COMP%]{min-width:200px;border:1px solid #dee2e6;border-radius:.375rem;box-shadow:0 .5rem 1rem #00000026;z-index:1050}.dropdown-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:.5rem;padding:.5rem 1rem;color:#212529;text-decoration:none;transition:background-color .15s ease-in-out}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;color:#16181b}.dropdown-item.active[_ngcontent-%COMP%]{background-color:#007bff;color:#fff}.dropdown-item.active[_ngcontent-%COMP%]   .language-code[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]   .language-name[_ngcontent-%COMP%]{flex:1;font-weight:500}.dropdown-item[_ngcontent-%COMP%]   .language-code[_ngcontent-%COMP%]{font-size:.75rem;color:#6c757d}@media (max-width: 768px){.language-btn[_ngcontent-%COMP%]{padding:.25rem .5rem;font-size:.8rem}.dropdown-menu[_ngcontent-%COMP%]{min-width:150px}.dropdown-item[_ngcontent-%COMP%]{padding:.4rem .8rem;font-size:.85rem}}.dropdown-menu[_ngcontent-%COMP%]{opacity:0;transform:translateY(-10px);transition:opacity .15s ease,transform .15s ease;pointer-events:none}.dropdown-menu.show[_ngcontent-%COMP%]{opacity:1;transform:translateY(0);pointer-events:auto}"]})}}return _})();function I(_,a){1&_&&(C.qex(0),C.j41(1,"li",11)(2,"a",37),C.nrm(3,"i",38),C.EFF(4,"Appointments "),C.k0s()(),C.j41(5,"li",11)(6,"a",39),C.nrm(7,"i",40),C.EFF(8,"Find Doctors "),C.k0s()(),C.j41(9,"li",11)(10,"a",41),C.nrm(11,"i",42),C.EFF(12,"Health Assistant "),C.k0s()(),C.bVm())}function k(_,a){1&_&&(C.qex(0),C.j41(1,"li",11)(2,"a",43),C.nrm(3,"i",44),C.EFF(4,"Patients "),C.k0s()(),C.j41(5,"li",11)(6,"a",37),C.nrm(7,"i",38),C.EFF(8,"Schedule "),C.k0s()(),C.bVm())}function oe(_,a){if(1&_){const l=C.RV6();C.j41(0,"nav",3)(1,"div",4)(2,"a",5),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.navigateToDashboard())}),C.nrm(3,"i",6),C.EFF(4,"HealthConnect "),C.k0s(),C.j41(5,"button",7),C.nrm(6,"span",8),C.k0s(),C.j41(7,"div",9)(8,"ul",10)(9,"li",11)(10,"a",12),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.navigateToDashboard())}),C.nrm(11,"i",13),C.EFF(12,"Dashboard "),C.k0s()(),C.DNE(13,I,13,0,"ng-container",14),C.DNE(14,k,9,0,"ng-container",14),C.j41(15,"li",11)(16,"a",15),C.nrm(17,"i",16),C.EFF(18,"Messages "),C.k0s()(),C.j41(19,"li",11)(20,"a",17),C.nrm(21,"i",18),C.EFF(22,"Video Consultations "),C.k0s()()(),C.j41(23,"ul",19)(24,"li",20),C.nrm(25,"app-language-selector"),C.k0s(),C.j41(26,"li",11),C.nrm(27,"app-notification-bell"),C.k0s(),C.j41(28,"li",21)(29,"a",22)(30,"div",23),C.nrm(31,"i",24),C.k0s(),C.j41(32,"span",25),C.EFF(33),C.k0s()(),C.j41(34,"ul",26)(35,"li")(36,"h6",27),C.EFF(37),C.nrm(38,"br"),C.j41(39,"small",28),C.EFF(40),C.nI1(41,"titlecase"),C.k0s()()(),C.j41(42,"li"),C.nrm(43,"hr",29),C.k0s(),C.j41(44,"li")(45,"a",30),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.navigateToProfile())}),C.nrm(46,"i",31),C.EFF(47,"Profile Settings "),C.k0s()(),C.j41(48,"li")(49,"a",32),C.nrm(50,"i",33),C.EFF(51,"Notifications "),C.k0s()(),C.j41(52,"li")(53,"a",32),C.nrm(54,"i",34),C.EFF(55,"Help & Support "),C.k0s()(),C.j41(56,"li"),C.nrm(57,"hr",29),C.k0s(),C.j41(58,"li")(59,"a",35),C.bIt("click",function(){C.eBV(l);const M=C.XpG();return C.Njj(M.logout())}),C.nrm(60,"i",36),C.EFF(61,"Sign Out "),C.k0s()()()()()()()()}if(2&_){const l=C.XpG();C.R7$(13),C.Y8G("ngIf","PATIENT"===l.currentUser.role),C.R7$(1),C.Y8G("ngIf","DOCTOR"===l.currentUser.role),C.R7$(19),C.JRh(l.currentUser.fullName),C.R7$(4),C.SpI(" ",l.currentUser.fullName," "),C.R7$(3),C.JRh(C.bMT(41,5,l.currentUser.role))}}function De(_,a){1&_&&(C.j41(0,"footer",45)(1,"div",46)(2,"small",28),C.EFF(3," \xa9 2024 HealthConnect. All rights reserved. | "),C.j41(4,"a",47),C.EFF(5,"Privacy Policy"),C.k0s(),C.EFF(6," | "),C.j41(7,"a",47),C.EFF(8,"Terms of Service"),C.k0s()()()())}let Qe=(()=>{class _{constructor(l,y){this.authService=l,this.router=y,this.title="HealthConnect",this.currentUser=null,this.showNavigation=!1}ngOnInit(){this.authService.currentUser$.subscribe(l=>{this.currentUser=l,this.updateNavigationVisibility()}),this.router.events.pipe((0,xo.p)(l=>l instanceof gr.wF)).subscribe(l=>{this.updateNavigationVisibility()})}updateNavigationVisibility(){const l=this.router.url.includes("/auth"),y=this.authService.isAuthenticated();this.showNavigation=!l&&y&&!!this.currentUser}logout(){this.authService.logout()}navigateToProfile(){this.router.navigate(["/profile"])}navigateToDashboard(){"DOCTOR"===this.currentUser?.role?this.router.navigate(["/doctor/dashboard"]):"PATIENT"===this.currentUser?.role&&this.router.navigate(["/patient/dashboard"])}static{this.\u0275fac=function(y){return new(y||_)(C.rXU(uo.u),C.rXU(gr.Ix))}}static{this.\u0275cmp=C.VBU({type:_,selectors:[["app-root"]],decls:4,vars:4,consts:[["class","navbar navbar-expand-lg navbar-dark bg-primary",4,"ngIf"],[1,"main-content"],["class","bg-light text-center py-3 mt-auto",4,"ngIf"],[1,"navbar","navbar-expand-lg","navbar-dark","bg-primary"],[1,"container-fluid"],[1,"navbar-brand","fw-bold",2,"cursor","pointer",3,"click"],[1,"bi","bi-heart-pulse","me-2"],["type","button","data-bs-toggle","collapse","data-bs-target","#navbarNav",1,"navbar-toggler"],[1,"navbar-toggler-icon"],["id","navbarNav",1,"collapse","navbar-collapse"],[1,"navbar-nav","me-auto"],[1,"nav-item"],[1,"nav-link",2,"cursor","pointer",3,"click"],[1,"bi","bi-house","me-1"],[4,"ngIf"],["routerLink","/chat","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-chat-dots","me-1"],["routerLink","/telemedicine","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-camera-video","me-1"],[1,"navbar-nav"],[1,"nav-item","me-2"],[1,"nav-item","dropdown"],["href","#","id","navbarDropdown","role","button","data-bs-toggle","dropdown",1,"nav-link","dropdown-toggle","d-flex","align-items-center"],[1,"rounded-circle","bg-light","text-primary","d-flex","align-items-center","justify-content-center","me-2",2,"width","32px","height","32px"],[1,"bi","bi-person"],[1,"d-none","d-md-inline"],[1,"dropdown-menu","dropdown-menu-end"],[1,"dropdown-header"],[1,"text-muted"],[1,"dropdown-divider"],[1,"dropdown-item",2,"cursor","pointer",3,"click"],[1,"bi","bi-person-gear","me-2"],["href","#",1,"dropdown-item",2,"cursor","pointer"],[1,"bi","bi-bell","me-2"],[1,"bi","bi-question-circle","me-2"],[1,"dropdown-item","text-danger",2,"cursor","pointer",3,"click"],[1,"bi","bi-box-arrow-right","me-2"],["routerLink","/appointments","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-calendar","me-1"],["routerLink","/doctors","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-search","me-1"],["routerLink","/ai-health-bot","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-robot","me-1"],["routerLink","/patients","routerLinkActive","active",1,"nav-link"],[1,"bi","bi-people","me-1"],[1,"bg-light","text-center","py-3","mt-auto"],[1,"container"],["href","#",1,"text-decoration-none"]],template:function(y,M){1&y&&(C.DNE(0,oe,62,7,"nav",0),C.j41(1,"main",1),C.nrm(2,"router-outlet"),C.k0s(),C.DNE(3,De,9,0,"footer",2)),2&y&&(C.Y8G("ngIf",M.showNavigation&&M.currentUser),C.R7$(1),C.AVh("with-navbar",M.showNavigation&&M.currentUser),C.R7$(2),C.Y8G("ngIf",M.showNavigation))},dependencies:[v.bT,gr.n3,gr.Wk,gr.wQ,ma,D,v.PV],styles:[".navbar[_ngcontent-%COMP%]{box-shadow:0 2px 4px #0000001a;z-index:1030}.navbar-brand[_ngcontent-%COMP%]{font-size:1.5rem;font-weight:700}.nav-link[_ngcontent-%COMP%]{font-weight:500;transition:all .3s ease}.nav-link[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;border-radius:.375rem}.nav-link.active[_ngcontent-%COMP%]{background-color:#fff3;border-radius:.375rem}.dropdown-menu[_ngcontent-%COMP%]{border:none;box-shadow:0 4px 6px #0000001a;border-radius:.5rem;min-width:200px}.dropdown-item[_ngcontent-%COMP%]{padding:.5rem 1rem;transition:all .3s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa}.dropdown-header[_ngcontent-%COMP%]{font-weight:600;color:#495057}.main-content[_ngcontent-%COMP%]{min-height:calc(100vh - 60px);display:flex;flex-direction:column}.main-content.with-navbar[_ngcontent-%COMP%]{min-height:calc(100vh - 116px)}footer[_ngcontent-%COMP%]{margin-top:auto;border-top:1px solid #e9ecef}@media (max-width: 991px){.navbar-nav[_ngcontent-%COMP%]{padding-top:1rem}.navbar-nav[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]{padding:.5rem 1rem}.dropdown-menu[_ngcontent-%COMP%]{position:static!important;transform:none!important;border:none;box-shadow:none;background-color:#ffffff1a;margin-top:.5rem}.dropdown-item[_ngcontent-%COMP%]{color:#fffc}.dropdown-item[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;color:#fff}.dropdown-header[_ngcontent-%COMP%]{color:#ffffffe6}.dropdown-divider[_ngcontent-%COMP%]{border-color:#fff3}}"]})}}return _})();var Zt=O(3443);let Jt=(()=>{class _{constructor(l){this.authService=l}intercept(l,y){const M=this.authService.getToken();if(M){const R=l.clone({headers:l.headers.set("Authorization",`Bearer ${M}`)});return y.handle(R)}return y.handle(l)}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(uo.u))}}static{this.\u0275prov=C.jDH({token:_,factory:_.\u0275fac})}}return _})(),Ht=(()=>{class _{constructor(l){if(l)throw new Error("CoreModule is already loaded. Import it in the AppModule only")}static{this.\u0275fac=function(y){return new(y||_)(C.KVO(_,12))}}static{this.\u0275mod=C.$C({type:_})}static{this.\u0275inj=C.G2t({providers:[uo.u,Zt.D,Jr.q,{provide:$i.a7,useClass:Jt,multi:!0}],imports:[v.MD]})}}return _})();var Tt=O(3887);let on=(()=>{class _{static{this.\u0275fac=function(y){return new(y||_)}}static{this.\u0275mod=C.$C({type:_,bootstrap:[Qe]})}static{this.\u0275inj=C.G2t({imports:[c.Bb,_i,$i.q1,_r.X1,_r.YN,ss,Ht,Tt.G]})}}return _})();window.global=window,c.sG().bootstrapModule(on).catch(_=>console.error(_))},4412:(qe,ye,O)=>{O.d(ye,{t:()=>C});var c=O(3794);class C extends c.B{constructor(se){super(),this._value=se}get value(){return this.getValue()}_subscribe(se){const X=super._subscribe(se);return!X.closed&&se.next(this._value),X}getValue(){const{hasError:se,thrownError:X,_value:ne}=this;if(se)throw X;return this._throwIfClosed(),ne}next(se){super.next(this._value=se)}}},1985:(qe,ye,O)=>{O.d(ye,{c:()=>de});var c=O(7707),C=O(8359),ce=O(3494),se=O(1203),X=O(1026),ne=O(8071),ee=O(9786);let de=(()=>{class G{constructor(_e){_e&&(this._subscribe=_e)}lift(_e){const Ve=new G;return Ve.source=this,Ve.operator=_e,Ve}subscribe(_e,Ve,Vt){const bt=function H(G){return G&&G instanceof c.vU||function fe(G){return G&&(0,ne.T)(G.next)&&(0,ne.T)(G.error)&&(0,ne.T)(G.complete)}(G)&&(0,C.Uv)(G)}(_e)?_e:new c.Ms(_e,Ve,Vt);return(0,ee.Y)(()=>{const{operator:Ze,source:Rt}=this;bt.add(Ze?Ze.call(bt,Rt):Rt?this._subscribe(bt):this._trySubscribe(bt))}),bt}_trySubscribe(_e){try{return this._subscribe(_e)}catch(Ve){_e.error(Ve)}}forEach(_e,Ve){return new(Ve=le(Ve))((Vt,bt)=>{const Ze=new c.Ms({next:Rt=>{try{_e(Rt)}catch(yt){bt(yt),Ze.unsubscribe()}},error:bt,complete:Vt});this.subscribe(Ze)})}_subscribe(_e){var Ve;return null===(Ve=this.source)||void 0===Ve?void 0:Ve.subscribe(_e)}[ce.s](){return this}pipe(..._e){return(0,se.m)(_e)(this)}toPromise(_e){return new(_e=le(_e))((Ve,Vt)=>{let bt;this.subscribe(Ze=>bt=Ze,Ze=>Vt(Ze),()=>Ve(bt))})}}return G.create=ve=>new G(ve),G})();function le(G){var ve;return null!==(ve=G??X.$.Promise)&&void 0!==ve?ve:Promise}},3794:(qe,ye,O)=>{O.d(ye,{B:()=>ee});var c=O(1985),C=O(8359);const se=(0,O(1853).L)(le=>function(){le(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var X=O(7908),ne=O(9786);let ee=(()=>{class le extends c.c{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(H){const G=new de(this,this);return G.operator=H,G}_throwIfClosed(){if(this.closed)throw new se}next(H){(0,ne.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const G of this.currentObservers)G.next(H)}})}error(H){(0,ne.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=H;const{observers:G}=this;for(;G.length;)G.shift().error(H)}})}complete(){(0,ne.Y)(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:H}=this;for(;H.length;)H.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var H;return(null===(H=this.observers)||void 0===H?void 0:H.length)>0}_trySubscribe(H){return this._throwIfClosed(),super._trySubscribe(H)}_subscribe(H){return this._throwIfClosed(),this._checkFinalizedStatuses(H),this._innerSubscribe(H)}_innerSubscribe(H){const{hasError:G,isStopped:ve,observers:_e}=this;return G||ve?C.Kn:(this.currentObservers=null,_e.push(H),new C.yU(()=>{this.currentObservers=null,(0,X.o)(_e,H)}))}_checkFinalizedStatuses(H){const{hasError:G,thrownError:ve,isStopped:_e}=this;G?H.error(ve):_e&&H.complete()}asObservable(){const H=new c.c;return H.source=this,H}}return le.create=(fe,H)=>new de(fe,H),le})();class de extends ee{constructor(fe,H){super(),this.destination=fe,this.source=H}next(fe){var H,G;null===(G=null===(H=this.destination)||void 0===H?void 0:H.next)||void 0===G||G.call(H,fe)}error(fe){var H,G;null===(G=null===(H=this.destination)||void 0===H?void 0:H.error)||void 0===G||G.call(H,fe)}complete(){var fe,H;null===(H=null===(fe=this.destination)||void 0===fe?void 0:fe.complete)||void 0===H||H.call(fe)}_subscribe(fe){var H,G;return null!==(G=null===(H=this.source)||void 0===H?void 0:H.subscribe(fe))&&void 0!==G?G:C.Kn}}},7707:(qe,ye,O)=>{O.d(ye,{Ms:()=>Vt,vU:()=>G});var c=O(8071),C=O(8359),ce=O(1026),se=O(5334),X=O(5343);const ne=le("C",void 0,void 0);function le(xe,Ne,Ke){return{kind:xe,value:Ne,error:Ke}}var fe=O(9270),H=O(9786);class G extends C.yU{constructor(Ne){super(),this.isStopped=!1,Ne?(this.destination=Ne,(0,C.Uv)(Ne)&&Ne.add(this)):this.destination=yt}static create(Ne,Ke,we){return new Vt(Ne,Ke,we)}next(Ne){this.isStopped?Rt(function de(xe){return le("N",xe,void 0)}(Ne),this):this._next(Ne)}error(Ne){this.isStopped?Rt(function ee(xe){return le("E",void 0,xe)}(Ne),this):(this.isStopped=!0,this._error(Ne))}complete(){this.isStopped?Rt(ne,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(Ne){this.destination.next(Ne)}_error(Ne){try{this.destination.error(Ne)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const ve=Function.prototype.bind;function _e(xe,Ne){return ve.call(xe,Ne)}class Ve{constructor(Ne){this.partialObserver=Ne}next(Ne){const{partialObserver:Ke}=this;if(Ke.next)try{Ke.next(Ne)}catch(we){bt(we)}}error(Ne){const{partialObserver:Ke}=this;if(Ke.error)try{Ke.error(Ne)}catch(we){bt(we)}else bt(Ne)}complete(){const{partialObserver:Ne}=this;if(Ne.complete)try{Ne.complete()}catch(Ke){bt(Ke)}}}class Vt extends G{constructor(Ne,Ke,we){let ke;if(super(),(0,c.T)(Ne)||!Ne)ke={next:Ne??void 0,error:Ke??void 0,complete:we??void 0};else{let Te;this&&ce.$.useDeprecatedNextContext?(Te=Object.create(Ne),Te.unsubscribe=()=>this.unsubscribe(),ke={next:Ne.next&&_e(Ne.next,Te),error:Ne.error&&_e(Ne.error,Te),complete:Ne.complete&&_e(Ne.complete,Te)}):ke=Ne}this.destination=new Ve(ke)}}function bt(xe){ce.$.useDeprecatedSynchronousErrorHandling?(0,H.l)(xe):(0,se.m)(xe)}function Rt(xe,Ne){const{onStoppedNotification:Ke}=ce.$;Ke&&fe.f.setTimeout(()=>Ke(xe,Ne))}const yt={closed:!0,next:X.l,error:function Ze(xe){throw xe},complete:X.l}},8359:(qe,ye,O)=>{O.d(ye,{Kn:()=>ne,yU:()=>X,Uv:()=>ee});var c=O(8071);const ce=(0,O(1853).L)(le=>function(H){le(this),this.message=H?`${H.length} errors occurred during unsubscription:\n${H.map((G,ve)=>`${ve+1}) ${G.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=H});var se=O(7908);class X{constructor(fe){this.initialTeardown=fe,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let fe;if(!this.closed){this.closed=!0;const{_parentage:H}=this;if(H)if(this._parentage=null,Array.isArray(H))for(const _e of H)_e.remove(this);else H.remove(this);const{initialTeardown:G}=this;if((0,c.T)(G))try{G()}catch(_e){fe=_e instanceof ce?_e.errors:[_e]}const{_finalizers:ve}=this;if(ve){this._finalizers=null;for(const _e of ve)try{de(_e)}catch(Ve){fe=fe??[],Ve instanceof ce?fe=[...fe,...Ve.errors]:fe.push(Ve)}}if(fe)throw new ce(fe)}}add(fe){var H;if(fe&&fe!==this)if(this.closed)de(fe);else{if(fe instanceof X){if(fe.closed||fe._hasParent(this))return;fe._addParent(this)}(this._finalizers=null!==(H=this._finalizers)&&void 0!==H?H:[]).push(fe)}}_hasParent(fe){const{_parentage:H}=this;return H===fe||Array.isArray(H)&&H.includes(fe)}_addParent(fe){const{_parentage:H}=this;this._parentage=Array.isArray(H)?(H.push(fe),H):H?[H,fe]:fe}_removeParent(fe){const{_parentage:H}=this;H===fe?this._parentage=null:Array.isArray(H)&&(0,se.o)(H,fe)}remove(fe){const{_finalizers:H}=this;H&&(0,se.o)(H,fe),fe instanceof X&&fe._removeParent(this)}}X.EMPTY=(()=>{const le=new X;return le.closed=!0,le})();const ne=X.EMPTY;function ee(le){return le instanceof X||le&&"closed"in le&&(0,c.T)(le.remove)&&(0,c.T)(le.add)&&(0,c.T)(le.unsubscribe)}function de(le){(0,c.T)(le)?le():le.unsubscribe()}},1026:(qe,ye,O)=>{O.d(ye,{$:()=>c});const c={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},983:(qe,ye,O)=>{O.d(ye,{w:()=>C});const C=new(O(1985).c)(X=>X.complete())},6648:(qe,ye,O)=>{O.d(ye,{H:()=>we});var c=O(8750),C=O(5225),ce=O(9974),se=O(4360);function X(ke,Te=0){return(0,ce.N)((ot,rt)=>{ot.subscribe((0,se._)(rt,Pe=>(0,C.N)(rt,ke,()=>rt.next(Pe),Te),()=>(0,C.N)(rt,ke,()=>rt.complete(),Te),Pe=>(0,C.N)(rt,ke,()=>rt.error(Pe),Te)))})}function ne(ke,Te=0){return(0,ce.N)((ot,rt)=>{rt.add(ke.schedule(()=>ot.subscribe(rt),Te))})}var le=O(1985),H=O(4761),G=O(8071);function _e(ke,Te){if(!ke)throw new Error("Iterable cannot be null");return new le.c(ot=>{(0,C.N)(ot,Te,()=>{const rt=ke[Symbol.asyncIterator]();(0,C.N)(ot,Te,()=>{rt.next().then(Pe=>{Pe.done?ot.complete():ot.next(Pe.value)})},0,!0)})})}var Ve=O(5055),Vt=O(9858),bt=O(7441),Ze=O(5397),Rt=O(7953),yt=O(591),xe=O(5196);function we(ke,Te){return Te?function Ke(ke,Te){if(null!=ke){if((0,Ve.l)(ke))return function ee(ke,Te){return(0,c.Tg)(ke).pipe(ne(Te),X(Te))}(ke,Te);if((0,bt.X)(ke))return function fe(ke,Te){return new le.c(ot=>{let rt=0;return Te.schedule(function(){rt===ke.length?ot.complete():(ot.next(ke[rt++]),ot.closed||this.schedule())})})}(ke,Te);if((0,Vt.y)(ke))return function de(ke,Te){return(0,c.Tg)(ke).pipe(ne(Te),X(Te))}(ke,Te);if((0,Rt.T)(ke))return _e(ke,Te);if((0,Ze.x)(ke))return function ve(ke,Te){return new le.c(ot=>{let rt;return(0,C.N)(ot,Te,()=>{rt=ke[H.l](),(0,C.N)(ot,Te,()=>{let Pe,cn;try{({value:Pe,done:cn}=rt.next())}catch(et){return void ot.error(et)}cn?ot.complete():ot.next(Pe)},0,!0)}),()=>(0,G.T)(rt?.return)&&rt.return()})}(ke,Te);if((0,xe.U)(ke))return function Ne(ke,Te){return _e((0,xe.C)(ke),Te)}(ke,Te)}throw(0,yt.L)(ke)}(ke,Te):(0,c.Tg)(ke)}},8750:(qe,ye,O)=>{O.d(ye,{Tg:()=>ve});var c=O(1635),C=O(7441),ce=O(9858),se=O(1985),X=O(5055),ne=O(7953),ee=O(591),de=O(5397),le=O(5196),fe=O(8071),H=O(5334),G=O(3494);function ve(xe){if(xe instanceof se.c)return xe;if(null!=xe){if((0,X.l)(xe))return function _e(xe){return new se.c(Ne=>{const Ke=xe[G.s]();if((0,fe.T)(Ke.subscribe))return Ke.subscribe(Ne);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(xe);if((0,C.X)(xe))return function Ve(xe){return new se.c(Ne=>{for(let Ke=0;Ke<xe.length&&!Ne.closed;Ke++)Ne.next(xe[Ke]);Ne.complete()})}(xe);if((0,ce.y)(xe))return function Vt(xe){return new se.c(Ne=>{xe.then(Ke=>{Ne.closed||(Ne.next(Ke),Ne.complete())},Ke=>Ne.error(Ke)).then(null,H.m)})}(xe);if((0,ne.T)(xe))return Ze(xe);if((0,de.x)(xe))return function bt(xe){return new se.c(Ne=>{for(const Ke of xe)if(Ne.next(Ke),Ne.closed)return;Ne.complete()})}(xe);if((0,le.U)(xe))return function Rt(xe){return Ze((0,le.C)(xe))}(xe)}throw(0,ee.L)(xe)}function Ze(xe){return new se.c(Ne=>{(function yt(xe,Ne){var Ke,we,ke,Te;return(0,c.sH)(this,void 0,void 0,function*(){try{for(Ke=(0,c.xN)(xe);!(we=yield Ke.next()).done;)if(Ne.next(we.value),Ne.closed)return}catch(ot){ke={error:ot}}finally{try{we&&!we.done&&(Te=Ke.return)&&(yield Te.call(Ke))}finally{if(ke)throw ke.error}}Ne.complete()})})(xe,Ne).catch(Ke=>Ne.error(Ke))})}},7673:(qe,ye,O)=>{O.d(ye,{of:()=>ce});var c=O(9326),C=O(6648);function ce(...se){const X=(0,c.lI)(se);return(0,C.H)(se,X)}},8810:(qe,ye,O)=>{O.d(ye,{$:()=>ce});var c=O(1985),C=O(8071);function ce(se,X){const ne=(0,C.T)(se)?se:()=>se,ee=de=>de.error(ne());return new c.c(X?de=>X.schedule(ee,0,de):ee)}},4360:(qe,ye,O)=>{O.d(ye,{_:()=>C});var c=O(7707);function C(se,X,ne,ee,de){return new ce(se,X,ne,ee,de)}class ce extends c.vU{constructor(X,ne,ee,de,le,fe){super(X),this.onFinalize=le,this.shouldUnsubscribe=fe,this._next=ne?function(H){try{ne(H)}catch(G){X.error(G)}}:super._next,this._error=de?function(H){try{de(H)}catch(G){X.error(G)}finally{this.unsubscribe()}}:super._error,this._complete=ee?function(){try{ee()}catch(H){X.error(H)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var X;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:ne}=this;super.unsubscribe(),!ne&&(null===(X=this.onFinalize)||void 0===X||X.call(this))}}}},9437:(qe,ye,O)=>{O.d(ye,{W:()=>se});var c=O(8750),C=O(4360),ce=O(9974);function se(X){return(0,ce.N)((ne,ee)=>{let fe,de=null,le=!1;de=ne.subscribe((0,C._)(ee,void 0,void 0,H=>{fe=(0,c.Tg)(X(H,se(X)(ne))),de?(de.unsubscribe(),de=null,fe.subscribe(ee)):le=!0})),le&&(de.unsubscribe(),de=null,fe.subscribe(ee))})}},274:(qe,ye,O)=>{O.d(ye,{H:()=>ce});var c=O(1397),C=O(8071);function ce(se,X){return(0,C.T)(X)?(0,c.Z)(se,X,1):(0,c.Z)(se,1)}},3294:(qe,ye,O)=>{O.d(ye,{F:()=>se});var c=O(3669),C=O(9974),ce=O(4360);function se(ne,ee=c.D){return ne=ne??X,(0,C.N)((de,le)=>{let fe,H=!0;de.subscribe((0,ce._)(le,G=>{const ve=ee(G);(H||!ne(fe,ve))&&(H=!1,fe=ve,le.next(G))}))})}function X(ne,ee){return ne===ee}},5964:(qe,ye,O)=>{O.d(ye,{p:()=>ce});var c=O(9974),C=O(4360);function ce(se,X){return(0,c.N)((ne,ee)=>{let de=0;ne.subscribe((0,C._)(ee,le=>se.call(X,le,de++)&&ee.next(le)))})}},980:(qe,ye,O)=>{O.d(ye,{j:()=>C});var c=O(9974);function C(ce){return(0,c.N)((se,X)=>{try{se.subscribe(X)}finally{X.add(ce)}})}},6354:(qe,ye,O)=>{O.d(ye,{T:()=>ce});var c=O(9974),C=O(4360);function ce(se,X){return(0,c.N)((ne,ee)=>{let de=0;ne.subscribe((0,C._)(ee,le=>{ee.next(se.call(X,le,de++))}))})}},6365:(qe,ye,O)=>{O.d(ye,{U:()=>ce});var c=O(1397),C=O(3669);function ce(se=1/0){return(0,c.Z)(C.D,se)}},1397:(qe,ye,O)=>{O.d(ye,{Z:()=>de});var c=O(6354),C=O(8750),ce=O(9974),se=O(5225),X=O(4360),ee=O(8071);function de(le,fe,H=1/0){return(0,ee.T)(fe)?de((G,ve)=>(0,c.T)((_e,Ve)=>fe(G,_e,ve,Ve))((0,C.Tg)(le(G,ve))),H):("number"==typeof fe&&(H=fe),(0,ce.N)((G,ve)=>function ne(le,fe,H,G,ve,_e,Ve,Vt){const bt=[];let Ze=0,Rt=0,yt=!1;const xe=()=>{yt&&!bt.length&&!Ze&&fe.complete()},Ne=we=>Ze<G?Ke(we):bt.push(we),Ke=we=>{_e&&fe.next(we),Ze++;let ke=!1;(0,C.Tg)(H(we,Rt++)).subscribe((0,X._)(fe,Te=>{ve?.(Te),_e?Ne(Te):fe.next(Te)},()=>{ke=!0},void 0,()=>{if(ke)try{for(Ze--;bt.length&&Ze<G;){const Te=bt.shift();Ve?(0,se.N)(fe,Ve,()=>Ke(Te)):Ke(Te)}xe()}catch(Te){fe.error(Te)}}))};return le.subscribe((0,X._)(fe,Ne,()=>{yt=!0,xe()})),()=>{Vt?.()}}(G,ve,le,H)))}},5558:(qe,ye,O)=>{O.d(ye,{n:()=>se});var c=O(8750),C=O(9974),ce=O(4360);function se(X,ne){return(0,C.N)((ee,de)=>{let le=null,fe=0,H=!1;const G=()=>H&&!le&&de.complete();ee.subscribe((0,ce._)(de,ve=>{le?.unsubscribe();let _e=0;const Ve=fe++;(0,c.Tg)(X(ve,Ve)).subscribe(le=(0,ce._)(de,Vt=>de.next(ne?ne(ve,Vt,Ve,_e++):Vt),()=>{le=null,G()}))},()=>{H=!0,G()}))})}},6977:(qe,ye,O)=>{O.d(ye,{Q:()=>X});var c=O(9974),C=O(4360),ce=O(8750),se=O(5343);function X(ne){return(0,c.N)((ee,de)=>{(0,ce.Tg)(ne).subscribe((0,C._)(de,()=>de.complete(),se.l)),!de.closed&&ee.subscribe(de)})}},8141:(qe,ye,O)=>{O.d(ye,{M:()=>X});var c=O(8071),C=O(9974),ce=O(4360),se=O(3669);function X(ne,ee,de){const le=(0,c.T)(ne)||ee||de?{next:ne,error:ee,complete:de}:ne;return le?(0,C.N)((fe,H)=>{var G;null===(G=le.subscribe)||void 0===G||G.call(le);let ve=!0;fe.subscribe((0,ce._)(H,_e=>{var Ve;null===(Ve=le.next)||void 0===Ve||Ve.call(le,_e),H.next(_e)},()=>{var _e;ve=!1,null===(_e=le.complete)||void 0===_e||_e.call(le),H.complete()},_e=>{var Ve;ve=!1,null===(Ve=le.error)||void 0===Ve||Ve.call(le,_e),H.error(_e)},()=>{var _e,Ve;ve&&(null===(_e=le.unsubscribe)||void 0===_e||_e.call(le)),null===(Ve=le.finalize)||void 0===Ve||Ve.call(le)}))}):se.D}},9270:(qe,ye,O)=>{O.d(ye,{f:()=>c});const c={setTimeout(C,ce,...se){const{delegate:X}=c;return X?.setTimeout?X.setTimeout(C,ce,...se):setTimeout(C,ce,...se)},clearTimeout(C){const{delegate:ce}=c;return(ce?.clearTimeout||clearTimeout)(C)},delegate:void 0}},4761:(qe,ye,O)=>{O.d(ye,{l:()=>C});const C=function c(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}()},3494:(qe,ye,O)=>{O.d(ye,{s:()=>c});const c="function"==typeof Symbol&&Symbol.observable||"@@observable"},9326:(qe,ye,O)=>{O.d(ye,{R0:()=>ne,lI:()=>X,ms:()=>se});var c=O(8071),C=O(9470);function ce(ee){return ee[ee.length-1]}function se(ee){return(0,c.T)(ce(ee))?ee.pop():void 0}function X(ee){return(0,C.m)(ce(ee))?ee.pop():void 0}function ne(ee,de){return"number"==typeof ce(ee)?ee.pop():de}},3073:(qe,ye,O)=>{O.d(ye,{D:()=>X});const{isArray:c}=Array,{getPrototypeOf:C,prototype:ce,keys:se}=Object;function X(ee){if(1===ee.length){const de=ee[0];if(c(de))return{args:de,keys:null};if(function ne(ee){return ee&&"object"==typeof ee&&C(ee)===ce}(de)){const le=se(de);return{args:le.map(fe=>de[fe]),keys:le}}}return{args:ee,keys:null}}},7908:(qe,ye,O)=>{function c(C,ce){if(C){const se=C.indexOf(ce);0<=se&&C.splice(se,1)}}O.d(ye,{o:()=>c})},1853:(qe,ye,O)=>{function c(C){const se=C(X=>{Error.call(X),X.stack=(new Error).stack});return se.prototype=Object.create(Error.prototype),se.prototype.constructor=se,se}O.d(ye,{L:()=>c})},8496:(qe,ye,O)=>{function c(C,ce){return C.reduce((se,X,ne)=>(se[X]=ce[ne],se),{})}O.d(ye,{e:()=>c})},9786:(qe,ye,O)=>{O.d(ye,{Y:()=>ce,l:()=>se});var c=O(1026);let C=null;function ce(X){if(c.$.useDeprecatedSynchronousErrorHandling){const ne=!C;if(ne&&(C={errorThrown:!1,error:null}),X(),ne){const{errorThrown:ee,error:de}=C;if(C=null,ee)throw de}}else X()}function se(X){c.$.useDeprecatedSynchronousErrorHandling&&C&&(C.errorThrown=!0,C.error=X)}},5225:(qe,ye,O)=>{function c(C,ce,se,X=0,ne=!1){const ee=ce.schedule(function(){se(),ne?C.add(this.schedule(null,X)):this.unsubscribe()},X);if(C.add(ee),!ne)return ee}O.d(ye,{N:()=>c})},3669:(qe,ye,O)=>{function c(C){return C}O.d(ye,{D:()=>c})},7441:(qe,ye,O)=>{O.d(ye,{X:()=>c});const c=C=>C&&"number"==typeof C.length&&"function"!=typeof C},7953:(qe,ye,O)=>{O.d(ye,{T:()=>C});var c=O(8071);function C(ce){return Symbol.asyncIterator&&(0,c.T)(ce?.[Symbol.asyncIterator])}},8071:(qe,ye,O)=>{function c(C){return"function"==typeof C}O.d(ye,{T:()=>c})},5055:(qe,ye,O)=>{O.d(ye,{l:()=>ce});var c=O(3494),C=O(8071);function ce(se){return(0,C.T)(se[c.s])}},5397:(qe,ye,O)=>{O.d(ye,{x:()=>ce});var c=O(4761),C=O(8071);function ce(se){return(0,C.T)(se?.[c.l])}},9858:(qe,ye,O)=>{O.d(ye,{y:()=>C});var c=O(8071);function C(ce){return(0,c.T)(ce?.then)}},5196:(qe,ye,O)=>{O.d(ye,{C:()=>ce,U:()=>se});var c=O(1635),C=O(8071);function ce(X){return(0,c.AQ)(this,arguments,function*(){const ee=X.getReader();try{for(;;){const{value:de,done:le}=yield(0,c.N3)(ee.read());if(le)return yield(0,c.N3)(void 0);yield yield(0,c.N3)(de)}}finally{ee.releaseLock()}})}function se(X){return(0,C.T)(X?.getReader)}},9470:(qe,ye,O)=>{O.d(ye,{m:()=>C});var c=O(8071);function C(ce){return ce&&(0,c.T)(ce.schedule)}},9974:(qe,ye,O)=>{O.d(ye,{N:()=>ce,S:()=>C});var c=O(8071);function C(se){return(0,c.T)(se?.lift)}function ce(se){return X=>{if(C(X))return X.lift(function(ne){try{return se(ne,this)}catch(ee){this.error(ee)}});throw new TypeError("Unable to lift unknown Observable type")}}},6450:(qe,ye,O)=>{O.d(ye,{I:()=>se});var c=O(6354);const{isArray:C}=Array;function se(X){return(0,c.T)(ne=>function ce(X,ne){return C(ne)?X(...ne):X(ne)}(X,ne))}},5343:(qe,ye,O)=>{function c(){}O.d(ye,{l:()=>c})},1203:(qe,ye,O)=>{O.d(ye,{F:()=>C,m:()=>ce});var c=O(3669);function C(...se){return ce(se)}function ce(se){return 0===se.length?c.D:1===se.length?se[0]:function(ne){return se.reduce((ee,de)=>de(ee),ne)}}},5334:(qe,ye,O)=>{O.d(ye,{m:()=>ce});var c=O(1026),C=O(9270);function ce(se){C.f.setTimeout(()=>{const{onUnhandledError:X}=c.$;if(!X)throw se;X(se)})}},591:(qe,ye,O)=>{function c(C){return new TypeError(`You provided ${null!==C&&"object"==typeof C?"an invalid object":`'${C}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}O.d(ye,{L:()=>c})},177:(qe,ye,O)=>{O.d(ye,{AJ:()=>rn,GH:()=>gi,MD:()=>ao,N0:()=>lo,P9:()=>Xr,PV:()=>oo,QT:()=>ce,Sm:()=>Ve,Sq:()=>dr,VF:()=>X,Vy:()=>Qr,Xr:()=>Or,YU:()=>Je,ZD:()=>se,_b:()=>Tr,aZ:()=>bt,bT:()=>Vr,e1:()=>mr,fw:()=>Vt,hb:()=>ve,hj:()=>de,qQ:()=>ne,ux:()=>qn,vh:()=>yi});var c=O(6276);let C=null;function ce(){return C}function se(h){C||(C=h)}class X{}const ne=new c.nKC("DocumentToken");let ee=(()=>{class h{historyGo(D){throw new Error("Not implemented")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return(0,c.WQX)(le)},providedIn:"platform"})}}return h})();const de=new c.nKC("Location Initialized");let le=(()=>{class h extends ee{constructor(){super(),this._doc=(0,c.WQX)(ne),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return ce().getBaseHref(this._doc)}onPopState(D){const I=ce().getGlobalEventTarget(this._doc,"window");return I.addEventListener("popstate",D,!1),()=>I.removeEventListener("popstate",D)}onHashChange(D){const I=ce().getGlobalEventTarget(this._doc,"window");return I.addEventListener("hashchange",D,!1),()=>I.removeEventListener("hashchange",D)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(D){this._location.pathname=D}pushState(D,I,k){this._history.pushState(D,I,k)}replaceState(D,I,k){this._history.replaceState(D,I,k)}forward(){this._history.forward()}back(){this._history.back()}historyGo(D=0){this._history.go(D)}getState(){return this._history.state}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return new h},providedIn:"platform"})}}return h})();function fe(h,T){if(0==h.length)return T;if(0==T.length)return h;let D=0;return h.endsWith("/")&&D++,T.startsWith("/")&&D++,2==D?h+T.substring(1):1==D?h+T:h+"/"+T}function H(h){const T=h.match(/#|\?|$/),D=T&&T.index||h.length;return h.slice(0,D-("/"===h[D-1]?1:0))+h.slice(D)}function G(h){return h&&"?"!==h[0]?"?"+h:h}let ve=(()=>{class h{historyGo(D){throw new Error("Not implemented")}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return(0,c.WQX)(Ve)},providedIn:"root"})}}return h})();const _e=new c.nKC("appBaseHref");let Ve=(()=>{class h extends ve{constructor(D,I){super(),this._platformLocation=D,this._removeListenerFns=[],this._baseHref=I??this._platformLocation.getBaseHrefFromDOM()??(0,c.WQX)(ne).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(D){this._removeListenerFns.push(this._platformLocation.onPopState(D),this._platformLocation.onHashChange(D))}getBaseHref(){return this._baseHref}prepareExternalUrl(D){return fe(this._baseHref,D)}path(D=!1){const I=this._platformLocation.pathname+G(this._platformLocation.search),k=this._platformLocation.hash;return k&&D?`${I}${k}`:I}pushState(D,I,k,oe){const De=this.prepareExternalUrl(k+G(oe));this._platformLocation.pushState(D,I,De)}replaceState(D,I,k,oe){const De=this.prepareExternalUrl(k+G(oe));this._platformLocation.replaceState(D,I,De)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(D=0){this._platformLocation.historyGo?.(D)}static{this.\u0275fac=function(I){return new(I||h)(c.KVO(ee),c.KVO(_e,8))}}static{this.\u0275prov=c.jDH({token:h,factory:h.\u0275fac,providedIn:"root"})}}return h})(),Vt=(()=>{class h extends ve{constructor(D,I){super(),this._platformLocation=D,this._baseHref="",this._removeListenerFns=[],null!=I&&(this._baseHref=I)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(D){this._removeListenerFns.push(this._platformLocation.onPopState(D),this._platformLocation.onHashChange(D))}getBaseHref(){return this._baseHref}path(D=!1){let I=this._platformLocation.hash;return null==I&&(I="#"),I.length>0?I.substring(1):I}prepareExternalUrl(D){const I=fe(this._baseHref,D);return I.length>0?"#"+I:I}pushState(D,I,k,oe){let De=this.prepareExternalUrl(k+G(oe));0==De.length&&(De=this._platformLocation.pathname),this._platformLocation.pushState(D,I,De)}replaceState(D,I,k,oe){let De=this.prepareExternalUrl(k+G(oe));0==De.length&&(De=this._platformLocation.pathname),this._platformLocation.replaceState(D,I,De)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(D=0){this._platformLocation.historyGo?.(D)}static{this.\u0275fac=function(I){return new(I||h)(c.KVO(ee),c.KVO(_e,8))}}static{this.\u0275prov=c.jDH({token:h,factory:h.\u0275fac})}}return h})(),bt=(()=>{class h{constructor(D){this._subject=new c.bkB,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=D;const I=this._locationStrategy.getBaseHref();this._basePath=function xe(h){if(new RegExp("^(https?:)?//").test(h)){const[,D]=h.split(/\/\/[^\/]+/);return D}return h}(H(yt(I))),this._locationStrategy.onPopState(k=>{this._subject.emit({url:this.path(!0),pop:!0,state:k.state,type:k.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(D=!1){return this.normalize(this._locationStrategy.path(D))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(D,I=""){return this.path()==this.normalize(D+G(I))}normalize(D){return h.stripTrailingSlash(function Rt(h,T){if(!h||!T.startsWith(h))return T;const D=T.substring(h.length);return""===D||["/",";","?","#"].includes(D[0])?D:T}(this._basePath,yt(D)))}prepareExternalUrl(D){return D&&"/"!==D[0]&&(D="/"+D),this._locationStrategy.prepareExternalUrl(D)}go(D,I="",k=null){this._locationStrategy.pushState(k,"",D,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(D+G(I)),k)}replaceState(D,I="",k=null){this._locationStrategy.replaceState(k,"",D,I),this._notifyUrlChangeListeners(this.prepareExternalUrl(D+G(I)),k)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(D=0){this._locationStrategy.historyGo?.(D)}onUrlChange(D){return this._urlChangeListeners.push(D),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(I=>{this._notifyUrlChangeListeners(I.url,I.state)})),()=>{const I=this._urlChangeListeners.indexOf(D);this._urlChangeListeners.splice(I,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(D="",I){this._urlChangeListeners.forEach(k=>k(D,I))}subscribe(D,I,k){return this._subject.subscribe({next:D,error:I,complete:k})}static{this.normalizeQueryParams=G}static{this.joinWithSlash=fe}static{this.stripTrailingSlash=H}static{this.\u0275fac=function(I){return new(I||h)(c.KVO(ve))}}static{this.\u0275prov=c.jDH({token:h,factory:function(){return function Ze(){return new bt((0,c.KVO)(ve))}()},providedIn:"root"})}}return h})();function yt(h){return h.replace(/\/index.html$/,"")}var ke=function(h){return h[h.Format=0]="Format",h[h.Standalone=1]="Standalone",h}(ke||{}),Te=function(h){return h[h.Narrow=0]="Narrow",h[h.Abbreviated=1]="Abbreviated",h[h.Wide=2]="Wide",h[h.Short=3]="Short",h}(Te||{}),ot=function(h){return h[h.Short=0]="Short",h[h.Medium=1]="Medium",h[h.Long=2]="Long",h[h.Full=3]="Full",h}(ot||{}),rt=function(h){return h[h.Decimal=0]="Decimal",h[h.Group=1]="Group",h[h.List=2]="List",h[h.PercentSign=3]="PercentSign",h[h.PlusSign=4]="PlusSign",h[h.MinusSign=5]="MinusSign",h[h.Exponential=6]="Exponential",h[h.SuperscriptingExponent=7]="SuperscriptingExponent",h[h.PerMille=8]="PerMille",h[h.Infinity=9]="Infinity",h[h.NaN=10]="NaN",h[h.TimeSeparator=11]="TimeSeparator",h[h.CurrencyDecimal=12]="CurrencyDecimal",h[h.CurrencyGroup=13]="CurrencyGroup",h}(rt||{});function fn(h,T){return Ae((0,c.H5H)(h)[c.KH2.DateFormat],T)}function Z(h,T){return Ae((0,c.H5H)(h)[c.KH2.TimeFormat],T)}function te(h,T){return Ae((0,c.H5H)(h)[c.KH2.DateTimeFormat],T)}function ie(h,T){const D=(0,c.H5H)(h),I=D[c.KH2.NumberSymbols][T];if(typeof I>"u"){if(T===rt.CurrencyDecimal)return D[c.KH2.NumberSymbols][rt.Decimal];if(T===rt.CurrencyGroup)return D[c.KH2.NumberSymbols][rt.Group]}return I}function ut(h){if(!h[c.KH2.ExtraData])throw new Error(`Missing extra locale data for the locale "${h[c.KH2.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function Ae(h,T){for(let D=T;D>-1;D--)if(typeof h[D]<"u")return h[D];throw new Error("Locale data API: locale data undefined")}function hn(h){const[T,D]=h.split(":");return{hours:+T,minutes:+D}}const Un=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,lr={},_n=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var ur=function(h){return h[h.Short=0]="Short",h[h.ShortGMT=1]="ShortGMT",h[h.Long=2]="Long",h[h.Extended=3]="Extended",h}(ur||{}),vt=function(h){return h[h.FullYear=0]="FullYear",h[h.Month=1]="Month",h[h.Date=2]="Date",h[h.Hours=3]="Hours",h[h.Minutes=4]="Minutes",h[h.Seconds=5]="Seconds",h[h.FractionalSeconds=6]="FractionalSeconds",h[h.Day=7]="Day",h}(vt||{}),ht=function(h){return h[h.DayPeriods=0]="DayPeriods",h[h.Days=1]="Days",h[h.Months=2]="Months",h[h.Eras=3]="Eras",h}(ht||{});function nr(h,T,D,I){let k=function he(h){if(V(h))return h;if("number"==typeof h&&!isNaN(h))return new Date(h);if("string"==typeof h){if(h=h.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(h)){const[k,oe=1,De=1]=h.split("-").map(Qe=>+Qe);return Fn(k,oe-1,De)}const D=parseFloat(h);if(!isNaN(h-D))return new Date(D);let I;if(I=h.match(Un))return function q(h){const T=new Date(0);let D=0,I=0;const k=h[8]?T.setUTCFullYear:T.setFullYear,oe=h[8]?T.setUTCHours:T.setHours;h[9]&&(D=Number(h[9]+h[10]),I=Number(h[9]+h[11])),k.call(T,Number(h[1]),Number(h[2])-1,Number(h[3]));const De=Number(h[4]||0)-D,Qe=Number(h[5]||0)-I,Zt=Number(h[6]||0),Jt=Math.floor(1e3*parseFloat("0."+(h[7]||0)));return oe.call(T,De,Qe,Zt,Jt),T}(I)}const T=new Date(h);if(!V(T))throw new Error(`Unable to convert "${h}" into a date`);return T}(h);T=sn(D,T)||T;let Qe,De=[];for(;T;){if(Qe=_n.exec(T),!Qe){De.push(T);break}{De=De.concat(Qe.slice(1));const Ht=De.pop();if(!Ht)break;T=Ht}}let Zt=k.getTimezoneOffset();I&&(Zt=cr(I,Zt),k=function Lr(h,T,D){const I=D?-1:1,k=h.getTimezoneOffset();return function kr(h,T){return(h=new Date(h.getTime())).setMinutes(h.getMinutes()+T),h}(h,I*(cr(T,k)-k))}(k,I,!0));let Jt="";return De.forEach(Ht=>{const Tt=function rr(h){if(Xn[h])return Xn[h];let T;switch(h){case"G":case"GG":case"GGG":T=pt(ht.Eras,Te.Abbreviated);break;case"GGGG":T=pt(ht.Eras,Te.Wide);break;case"GGGGG":T=pt(ht.Eras,Te.Narrow);break;case"y":T=Xt(vt.FullYear,1,0,!1,!0);break;case"yy":T=Xt(vt.FullYear,2,0,!0,!0);break;case"yyy":T=Xt(vt.FullYear,3,0,!1,!0);break;case"yyyy":T=Xt(vt.FullYear,4,0,!1,!0);break;case"Y":T=Kn(1);break;case"YY":T=Kn(2,!0);break;case"YYY":T=Kn(3);break;case"YYYY":T=Kn(4);break;case"M":case"L":T=Xt(vt.Month,1,1);break;case"MM":case"LL":T=Xt(vt.Month,2,1);break;case"MMM":T=pt(ht.Months,Te.Abbreviated);break;case"MMMM":T=pt(ht.Months,Te.Wide);break;case"MMMMM":T=pt(ht.Months,Te.Narrow);break;case"LLL":T=pt(ht.Months,Te.Abbreviated,ke.Standalone);break;case"LLLL":T=pt(ht.Months,Te.Wide,ke.Standalone);break;case"LLLLL":T=pt(ht.Months,Te.Narrow,ke.Standalone);break;case"w":T=pi(1);break;case"ww":T=pi(2);break;case"W":T=pi(1,!0);break;case"d":T=Xt(vt.Date,1);break;case"dd":T=Xt(vt.Date,2);break;case"c":case"cc":T=Xt(vt.Day,1);break;case"ccc":T=pt(ht.Days,Te.Abbreviated,ke.Standalone);break;case"cccc":T=pt(ht.Days,Te.Wide,ke.Standalone);break;case"ccccc":T=pt(ht.Days,Te.Narrow,ke.Standalone);break;case"cccccc":T=pt(ht.Days,Te.Short,ke.Standalone);break;case"E":case"EE":case"EEE":T=pt(ht.Days,Te.Abbreviated);break;case"EEEE":T=pt(ht.Days,Te.Wide);break;case"EEEEE":T=pt(ht.Days,Te.Narrow);break;case"EEEEEE":T=pt(ht.Days,Te.Short);break;case"a":case"aa":case"aaa":T=pt(ht.DayPeriods,Te.Abbreviated);break;case"aaaa":T=pt(ht.DayPeriods,Te.Wide);break;case"aaaaa":T=pt(ht.DayPeriods,Te.Narrow);break;case"b":case"bb":case"bbb":T=pt(ht.DayPeriods,Te.Abbreviated,ke.Standalone,!0);break;case"bbbb":T=pt(ht.DayPeriods,Te.Wide,ke.Standalone,!0);break;case"bbbbb":T=pt(ht.DayPeriods,Te.Narrow,ke.Standalone,!0);break;case"B":case"BB":case"BBB":T=pt(ht.DayPeriods,Te.Abbreviated,ke.Format,!0);break;case"BBBB":T=pt(ht.DayPeriods,Te.Wide,ke.Format,!0);break;case"BBBBB":T=pt(ht.DayPeriods,Te.Narrow,ke.Format,!0);break;case"h":T=Xt(vt.Hours,1,-12);break;case"hh":T=Xt(vt.Hours,2,-12);break;case"H":T=Xt(vt.Hours,1);break;case"HH":T=Xt(vt.Hours,2);break;case"m":T=Xt(vt.Minutes,1);break;case"mm":T=Xt(vt.Minutes,2);break;case"s":T=Xt(vt.Seconds,1);break;case"ss":T=Xt(vt.Seconds,2);break;case"S":T=Xt(vt.FractionalSeconds,1);break;case"SS":T=Xt(vt.FractionalSeconds,2);break;case"SSS":T=Xt(vt.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":T=hi(ur.Short);break;case"ZZZZZ":T=hi(ur.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":T=hi(ur.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":T=hi(ur.Long);break;default:return null}return Xn[h]=T,T}(Ht);Jt+=Tt?Tt(k,D,Zt):"''"===Ht?"'":Ht.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),Jt}function Fn(h,T,D){const I=new Date(0);return I.setFullYear(h,T,D),I.setHours(0,0,0),I}function sn(h,T){const D=function cn(h){return(0,c.H5H)(h)[c.KH2.LocaleId]}(h);if(lr[D]=lr[D]||{},lr[D][T])return lr[D][T];let I="";switch(T){case"shortDate":I=fn(h,ot.Short);break;case"mediumDate":I=fn(h,ot.Medium);break;case"longDate":I=fn(h,ot.Long);break;case"fullDate":I=fn(h,ot.Full);break;case"shortTime":I=Z(h,ot.Short);break;case"mediumTime":I=Z(h,ot.Medium);break;case"longTime":I=Z(h,ot.Long);break;case"fullTime":I=Z(h,ot.Full);break;case"short":const k=sn(h,"shortTime"),oe=sn(h,"shortDate");I=an(te(h,ot.Short),[k,oe]);break;case"medium":const De=sn(h,"mediumTime"),Qe=sn(h,"mediumDate");I=an(te(h,ot.Medium),[De,Qe]);break;case"long":const Zt=sn(h,"longTime"),Jt=sn(h,"longDate");I=an(te(h,ot.Long),[Zt,Jt]);break;case"full":const Ht=sn(h,"fullTime"),Tt=sn(h,"fullDate");I=an(te(h,ot.Full),[Ht,Tt])}return I&&(lr[D][T]=I),I}function an(h,T){return T&&(h=h.replace(/\{([^}]+)}/g,function(D,I){return null!=T&&I in T?T[I]:D})),h}function En(h,T,D="-",I,k){let oe="";(h<0||k&&h<=0)&&(k?h=1-h:(h=-h,oe=D));let De=String(h);for(;De.length<T;)De="0"+De;return I&&(De=De.slice(De.length-T)),oe+De}function Xt(h,T,D=0,I=!1,k=!1){return function(oe,De){let Qe=function Oi(h,T){switch(h){case vt.FullYear:return T.getFullYear();case vt.Month:return T.getMonth();case vt.Date:return T.getDate();case vt.Hours:return T.getHours();case vt.Minutes:return T.getMinutes();case vt.Seconds:return T.getSeconds();case vt.FractionalSeconds:return T.getMilliseconds();case vt.Day:return T.getDay();default:throw new Error(`Unknown DateType value "${h}".`)}}(h,oe);if((D>0||Qe>-D)&&(Qe+=D),h===vt.Hours)0===Qe&&-12===D&&(Qe=12);else if(h===vt.FractionalSeconds)return function Mt(h,T){return En(h,3).substring(0,T)}(Qe,T);const Zt=ie(De,rt.MinusSign);return En(Qe,T,Zt,I,k)}}function pt(h,T,D=ke.Format,I=!1){return function(k,oe){return function ti(h,T,D,I,k,oe){switch(D){case ht.Months:return function dn(h,T,D){const I=(0,c.H5H)(h),oe=Ae([I[c.KH2.MonthsFormat],I[c.KH2.MonthsStandalone]],T);return Ae(oe,D)}(T,k,I)[h.getMonth()];case ht.Days:return function gt(h,T,D){const I=(0,c.H5H)(h),oe=Ae([I[c.KH2.DaysFormat],I[c.KH2.DaysStandalone]],T);return Ae(oe,D)}(T,k,I)[h.getDay()];case ht.DayPeriods:const De=h.getHours(),Qe=h.getMinutes();if(oe){const Jt=function xt(h){const T=(0,c.H5H)(h);return ut(T),(T[c.KH2.ExtraData][2]||[]).map(I=>"string"==typeof I?hn(I):[hn(I[0]),hn(I[1])])}(T),Ht=function Qt(h,T,D){const I=(0,c.H5H)(h);ut(I);const oe=Ae([I[c.KH2.ExtraData][0],I[c.KH2.ExtraData][1]],T)||[];return Ae(oe,D)||[]}(T,k,I),Tt=Jt.findIndex(on=>{if(Array.isArray(on)){const[_,a]=on,l=De>=_.hours&&Qe>=_.minutes,y=De<a.hours||De===a.hours&&Qe<a.minutes;if(_.hours<a.hours){if(l&&y)return!0}else if(l||y)return!0}else if(on.hours===De&&on.minutes===Qe)return!0;return!1});if(-1!==Tt)return Ht[Tt]}return function et(h,T,D){const I=(0,c.H5H)(h),oe=Ae([I[c.KH2.DayPeriodsFormat],I[c.KH2.DayPeriodsStandalone]],T);return Ae(oe,D)}(T,k,I)[De<12?0:1];case ht.Eras:return function jt(h,T){return Ae((0,c.H5H)(h)[c.KH2.Eras],T)}(T,I)[h.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${D}`)}}(k,oe,h,T,D,I)}}function hi(h){return function(T,D,I){const k=-1*I,oe=ie(D,rt.MinusSign),De=k>0?Math.floor(k/60):Math.ceil(k/60);switch(h){case ur.Short:return(k>=0?"+":"")+En(De,2,oe)+En(Math.abs(k%60),2,oe);case ur.ShortGMT:return"GMT"+(k>=0?"+":"")+En(De,1,oe);case ur.Long:return"GMT"+(k>=0?"+":"")+En(De,2,oe)+":"+En(Math.abs(k%60),2,oe);case ur.Extended:return 0===I?"Z":(k>=0?"+":"")+En(De,2,oe)+":"+En(Math.abs(k%60),2,oe);default:throw new Error(`Unknown zone width "${h}"`)}}}const nn=0,Ir=4;function dt(h){return Fn(h.getFullYear(),h.getMonth(),h.getDate()+(Ir-h.getDay()))}function pi(h,T=!1){return function(D,I){let k;if(T){const oe=new Date(D.getFullYear(),D.getMonth(),1).getDay()-1,De=D.getDate();k=1+Math.floor((De+oe)/7)}else{const oe=dt(D),De=function _t(h){const T=Fn(h,nn,1).getDay();return Fn(h,0,1+(T<=Ir?Ir:Ir+7)-T)}(oe.getFullYear()),Qe=oe.getTime()-De.getTime();k=1+Math.round(Qe/6048e5)}return En(k,h,ie(I,rt.MinusSign))}}function Kn(h,T=!1){return function(D,I){return En(dt(D).getFullYear(),h,ie(I,rt.MinusSign),T)}}const Xn={};function cr(h,T){h=h.replace(/:/g,"");const D=Date.parse("Jan 01, 1970 00:00:00 "+h)/6e4;return isNaN(D)?T:D}function V(h){return h instanceof Date&&!isNaN(h.valueOf())}function Tr(h,T){T=encodeURIComponent(T);for(const D of h.split(";")){const I=D.indexOf("="),[k,oe]=-1==I?[D,""]:[D.slice(0,I),D.slice(I+1)];if(k.trim()===T)return decodeURIComponent(oe)}return null}const wn=/\s+/,ct=[];let Je=(()=>{class h{constructor(D,I,k,oe){this._iterableDiffers=D,this._keyValueDiffers=I,this._ngEl=k,this._renderer=oe,this.initialClasses=ct,this.stateMap=new Map}set klass(D){this.initialClasses=null!=D?D.trim().split(wn):ct}set ngClass(D){this.rawClass="string"==typeof D?D.trim().split(wn):D}ngDoCheck(){for(const I of this.initialClasses)this._updateState(I,!0);const D=this.rawClass;if(Array.isArray(D)||D instanceof Set)for(const I of D)this._updateState(I,!0);else if(null!=D)for(const I of Object.keys(D))this._updateState(I,!!D[I]);this._applyStateDiff()}_updateState(D,I){const k=this.stateMap.get(D);void 0!==k?(k.enabled!==I&&(k.changed=!0,k.enabled=I),k.touched=!0):this.stateMap.set(D,{enabled:I,changed:!0,touched:!0})}_applyStateDiff(){for(const D of this.stateMap){const I=D[0],k=D[1];k.changed?(this._toggleClass(I,k.enabled),k.changed=!1):k.touched||(k.enabled&&this._toggleClass(I,!1),this.stateMap.delete(I)),k.touched=!1}}_toggleClass(D,I){(D=D.trim()).length>0&&D.split(wn).forEach(k=>{I?this._renderer.addClass(this._ngEl.nativeElement,k):this._renderer.removeClass(this._ngEl.nativeElement,k)})}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c._q3),c.rXU(c.MKu),c.rXU(c.aKT),c.rXU(c.sFG))}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngClass",""]],inputs:{klass:["class","klass"],ngClass:"ngClass"},standalone:!0})}}return h})();class Tn{constructor(T,D,I,k){this.$implicit=T,this.ngForOf=D,this.index=I,this.count=k}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let dr=(()=>{class h{set ngForOf(D){this._ngForOf=D,this._ngForOfDirty=!0}set ngForTrackBy(D){this._trackByFn=D}get ngForTrackBy(){return this._trackByFn}constructor(D,I,k){this._viewContainer=D,this._template=I,this._differs=k,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(D){D&&(this._template=D)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const D=this._ngForOf;!this._differ&&D&&(this._differ=this._differs.find(D).create(this.ngForTrackBy))}if(this._differ){const D=this._differ.diff(this._ngForOf);D&&this._applyChanges(D)}}_applyChanges(D){const I=this._viewContainer;D.forEachOperation((k,oe,De)=>{if(null==k.previousIndex)I.createEmbeddedView(this._template,new Tn(k.item,this._ngForOf,-1,-1),null===De?void 0:De);else if(null==De)I.remove(null===oe?void 0:oe);else if(null!==oe){const Qe=I.get(oe);I.move(Qe,De),fr(Qe,k)}});for(let k=0,oe=I.length;k<oe;k++){const Qe=I.get(k).context;Qe.index=k,Qe.count=oe,Qe.ngForOf=this._ngForOf}D.forEachIdentityChange(k=>{fr(I.get(k.currentIndex),k)})}static ngTemplateContextGuard(D,I){return!0}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c.c1b),c.rXU(c.C4Q),c.rXU(c._q3))}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return h})();function fr(h,T){h.context.$implicit=T.item}let Vr=(()=>{class h{constructor(D,I){this._viewContainer=D,this._context=new Ki,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=I}set ngIf(D){this._context.$implicit=this._context.ngIf=D,this._updateView()}set ngIfThen(D){ri("ngIfThen",D),this._thenTemplateRef=D,this._thenViewRef=null,this._updateView()}set ngIfElse(D){ri("ngIfElse",D),this._elseTemplateRef=D,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(D,I){return!0}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c.c1b),c.rXU(c.C4Q))}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return h})();class Ki{constructor(){this.$implicit=null,this.ngIf=null}}function ri(h,T){if(T&&!T.createEmbeddedView)throw new Error(`${h} must be a TemplateRef, but received '${(0,c.Tbb)(T)}'.`)}class Xi{constructor(T,D){this._viewContainerRef=T,this._templateRef=D,this._created=!1}create(){this._created=!0,this._viewContainerRef.createEmbeddedView(this._templateRef)}destroy(){this._created=!1,this._viewContainerRef.clear()}enforceState(T){T&&!this._created?this.create():!T&&this._created&&this.destroy()}}let qn=(()=>{class h{constructor(){this._defaultViews=[],this._defaultUsed=!1,this._caseCount=0,this._lastCaseCheckIndex=0,this._lastCasesMatched=!1}set ngSwitch(D){this._ngSwitch=D,0===this._caseCount&&this._updateDefaultCases(!0)}_addCase(){return this._caseCount++}_addDefault(D){this._defaultViews.push(D)}_matchCase(D){const I=D==this._ngSwitch;return this._lastCasesMatched=this._lastCasesMatched||I,this._lastCaseCheckIndex++,this._lastCaseCheckIndex===this._caseCount&&(this._updateDefaultCases(!this._lastCasesMatched),this._lastCaseCheckIndex=0,this._lastCasesMatched=!1),I}_updateDefaultCases(D){if(this._defaultViews.length>0&&D!==this._defaultUsed){this._defaultUsed=D;for(const I of this._defaultViews)I.enforceState(D)}}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngSwitch",""]],inputs:{ngSwitch:"ngSwitch"},standalone:!0})}}return h})(),mr=(()=>{class h{constructor(D,I,k){this.ngSwitch=k,k._addCase(),this._view=new Xi(D,I)}ngDoCheck(){this._view.enforceState(this.ngSwitch._matchCase(this.ngSwitchCase))}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c.c1b),c.rXU(c.C4Q),c.rXU(qn,9))}}static{this.\u0275dir=c.FsC({type:h,selectors:[["","ngSwitchCase",""]],inputs:{ngSwitchCase:"ngSwitchCase"},standalone:!0})}}return h})();function Hn(h,T){return new c.wOt(2100,!1)}let gi=(()=>{class h{transform(D){if(null==D)return null;if("string"!=typeof D)throw Hn();return D.toLowerCase()}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275pipe=c.EJ8({name:"lowercase",type:h,pure:!0,standalone:!0})}}return h})();const mi=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g;let oo=(()=>{class h{transform(D){if(null==D)return null;if("string"!=typeof D)throw Hn();return D.replace(mi,I=>I[0].toUpperCase()+I.slice(1).toLowerCase())}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275pipe=c.EJ8({name:"titlecase",type:h,pure:!0,standalone:!0})}}return h})();const Ro=new c.nKC("DATE_PIPE_DEFAULT_TIMEZONE"),$o=new c.nKC("DATE_PIPE_DEFAULT_OPTIONS");let yi=(()=>{class h{constructor(D,I,k){this.locale=D,this.defaultTimezone=I,this.defaultOptions=k}transform(D,I,k,oe){if(null==D||""===D||D!=D)return null;try{return nr(D,I??this.defaultOptions?.dateFormat??"mediumDate",oe||this.locale,k??this.defaultOptions?.timezone??this.defaultTimezone??void 0)}catch(De){throw Hn()}}static{this.\u0275fac=function(I){return new(I||h)(c.rXU(c.xe9,16),c.rXU(Ro,24),c.rXU($o,24))}}static{this.\u0275pipe=c.EJ8({name:"date",type:h,pure:!0,standalone:!0})}}return h})(),Xr=(()=>{class h{transform(D,I,k){if(null==D)return null;if(!this.supports(D))throw Hn();return D.slice(I,k)}supports(D){return"string"==typeof D||Array.isArray(D)}static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275pipe=c.EJ8({name:"slice",type:h,pure:!1,standalone:!0})}}return h})(),ao=(()=>{class h{static{this.\u0275fac=function(I){return new(I||h)}}static{this.\u0275mod=c.$C({type:h})}static{this.\u0275inj=c.G2t({})}}return h})();const rn="browser",je="server";function Qr(h){return h===je}let Or=(()=>{class h{static{this.\u0275prov=(0,c.jDH)({token:h,providedIn:"root",factory:()=>new Nr((0,c.KVO)(ne),window)})}}return h})();class Nr{constructor(T,D){this.document=T,this.window=D,this.offset=()=>[0,0]}setOffset(T){this.offset=Array.isArray(T)?()=>T:T}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(T){this.supportsScrolling()&&this.window.scrollTo(T[0],T[1])}scrollToAnchor(T){if(!this.supportsScrolling())return;const D=function mt(h,T){const D=h.getElementById(T)||h.getElementsByName(T)[0];if(D)return D;if("function"==typeof h.createTreeWalker&&h.body&&"function"==typeof h.body.attachShadow){const I=h.createTreeWalker(h.body,NodeFilter.SHOW_ELEMENT);let k=I.currentNode;for(;k;){const oe=k.shadowRoot;if(oe){const De=oe.getElementById(T)||oe.querySelector(`[name="${T}"]`);if(De)return De}k=I.nextNode()}}return null}(this.document,T);D&&(this.scrollToElement(D),D.focus())}setHistoryScrollRestoration(T){this.supportsScrolling()&&(this.window.history.scrollRestoration=T)}scrollToElement(T){const D=T.getBoundingClientRect(),I=D.left+this.window.pageXOffset,k=D.top+this.window.pageYOffset,oe=this.offset();this.window.scrollTo(I-oe[0],k-oe[1])}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch{return!1}}}class lo{}},1626:(qe,ye,O)=>{O.d(ye,{Lr:()=>ve,Nl:()=>xe,Qq:()=>Sn,a7:()=>ut,q1:()=>W});var c=O(6276),C=O(7673),ce=O(6648),se=O(1985),X=O(274),ne=O(5964),ee=O(6354),de=O(980),le=O(5558),fe=O(177);class H{}class G{}class ve{constructor(j){this.normalizedNames=new Map,this.lazyUpdate=null,j?"string"==typeof j?this.lazyInit=()=>{this.headers=new Map,j.split("\n").forEach($=>{const ue=$.indexOf(":");if(ue>0){const Oe=$.slice(0,ue),Me=Oe.toLowerCase(),Le=$.slice(ue+1).trim();this.maybeSetNormalizedName(Oe,Me),this.headers.has(Me)?this.headers.get(Me).push(Le):this.headers.set(Me,[Le])}})}:typeof Headers<"u"&&j instanceof Headers?(this.headers=new Map,j.forEach(($,ue)=>{this.setHeaderEntries(ue,$)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(j).forEach(([$,ue])=>{this.setHeaderEntries($,ue)})}:this.headers=new Map}has(j){return this.init(),this.headers.has(j.toLowerCase())}get(j){this.init();const $=this.headers.get(j.toLowerCase());return $&&$.length>0?$[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(j){return this.init(),this.headers.get(j.toLowerCase())||null}append(j,$){return this.clone({name:j,value:$,op:"a"})}set(j,$){return this.clone({name:j,value:$,op:"s"})}delete(j,$){return this.clone({name:j,value:$,op:"d"})}maybeSetNormalizedName(j,$){this.normalizedNames.has($)||this.normalizedNames.set($,j)}init(){this.lazyInit&&(this.lazyInit instanceof ve?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(j=>this.applyUpdate(j)),this.lazyUpdate=null))}copyFrom(j){j.init(),Array.from(j.headers.keys()).forEach($=>{this.headers.set($,j.headers.get($)),this.normalizedNames.set($,j.normalizedNames.get($))})}clone(j){const $=new ve;return $.lazyInit=this.lazyInit&&this.lazyInit instanceof ve?this.lazyInit:this,$.lazyUpdate=(this.lazyUpdate||[]).concat([j]),$}applyUpdate(j){const $=j.name.toLowerCase();switch(j.op){case"a":case"s":let ue=j.value;if("string"==typeof ue&&(ue=[ue]),0===ue.length)return;this.maybeSetNormalizedName(j.name,$);const Oe=("a"===j.op?this.headers.get($):void 0)||[];Oe.push(...ue),this.headers.set($,Oe);break;case"d":const Me=j.value;if(Me){let Le=this.headers.get($);if(!Le)return;Le=Le.filter(Ut=>-1===Me.indexOf(Ut)),0===Le.length?(this.headers.delete($),this.normalizedNames.delete($)):this.headers.set($,Le)}else this.headers.delete($),this.normalizedNames.delete($)}}setHeaderEntries(j,$){const ue=(Array.isArray($)?$:[$]).map(Me=>Me.toString()),Oe=j.toLowerCase();this.headers.set(Oe,ue),this.maybeSetNormalizedName(j,Oe)}forEach(j){this.init(),Array.from(this.normalizedNames.keys()).forEach($=>j(this.normalizedNames.get($),this.headers.get($)))}}class Ve{encodeKey(j){return Rt(j)}encodeValue(j){return Rt(j)}decodeKey(j){return decodeURIComponent(j)}decodeValue(j){return decodeURIComponent(j)}}const bt=/%(\d[a-f0-9])/gi,Ze={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Rt(Q){return encodeURIComponent(Q).replace(bt,(j,$)=>Ze[$]??j)}function yt(Q){return`${Q}`}class xe{constructor(j={}){if(this.updates=null,this.cloneFrom=null,this.encoder=j.encoder||new Ve,j.fromString){if(j.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function Vt(Q,j){const $=new Map;return Q.length>0&&Q.replace(/^\?/,"").split("&").forEach(Oe=>{const Me=Oe.indexOf("="),[Le,Ut]=-1==Me?[j.decodeKey(Oe),""]:[j.decodeKey(Oe.slice(0,Me)),j.decodeValue(Oe.slice(Me+1))],tt=$.get(Le)||[];tt.push(Ut),$.set(Le,tt)}),$}(j.fromString,this.encoder)}else j.fromObject?(this.map=new Map,Object.keys(j.fromObject).forEach($=>{const ue=j.fromObject[$],Oe=Array.isArray(ue)?ue.map(yt):[yt(ue)];this.map.set($,Oe)})):this.map=null}has(j){return this.init(),this.map.has(j)}get(j){this.init();const $=this.map.get(j);return $?$[0]:null}getAll(j){return this.init(),this.map.get(j)||null}keys(){return this.init(),Array.from(this.map.keys())}append(j,$){return this.clone({param:j,value:$,op:"a"})}appendAll(j){const $=[];return Object.keys(j).forEach(ue=>{const Oe=j[ue];Array.isArray(Oe)?Oe.forEach(Me=>{$.push({param:ue,value:Me,op:"a"})}):$.push({param:ue,value:Oe,op:"a"})}),this.clone($)}set(j,$){return this.clone({param:j,value:$,op:"s"})}delete(j,$){return this.clone({param:j,value:$,op:"d"})}toString(){return this.init(),this.keys().map(j=>{const $=this.encoder.encodeKey(j);return this.map.get(j).map(ue=>$+"="+this.encoder.encodeValue(ue)).join("&")}).filter(j=>""!==j).join("&")}clone(j){const $=new xe({encoder:this.encoder});return $.cloneFrom=this.cloneFrom||this,$.updates=(this.updates||[]).concat(j),$}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(j=>this.map.set(j,this.cloneFrom.map.get(j))),this.updates.forEach(j=>{switch(j.op){case"a":case"s":const $=("a"===j.op?this.map.get(j.param):void 0)||[];$.push(yt(j.value)),this.map.set(j.param,$);break;case"d":if(void 0===j.value){this.map.delete(j.param);break}{let ue=this.map.get(j.param)||[];const Oe=ue.indexOf(yt(j.value));-1!==Oe&&ue.splice(Oe,1),ue.length>0?this.map.set(j.param,ue):this.map.delete(j.param)}}}),this.cloneFrom=this.updates=null)}}class Ke{constructor(){this.map=new Map}set(j,$){return this.map.set(j,$),this}get(j){return this.map.has(j)||this.map.set(j,j.defaultValue()),this.map.get(j)}delete(j){return this.map.delete(j),this}has(j){return this.map.has(j)}keys(){return this.map.keys()}}function ke(Q){return typeof ArrayBuffer<"u"&&Q instanceof ArrayBuffer}function Te(Q){return typeof Blob<"u"&&Q instanceof Blob}function ot(Q){return typeof FormData<"u"&&Q instanceof FormData}class Pe{constructor(j,$,ue,Oe){let Me;if(this.url=$,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=j.toUpperCase(),function we(Q){switch(Q){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||Oe?(this.body=void 0!==ue?ue:null,Me=Oe):Me=ue,Me&&(this.reportProgress=!!Me.reportProgress,this.withCredentials=!!Me.withCredentials,Me.responseType&&(this.responseType=Me.responseType),Me.headers&&(this.headers=Me.headers),Me.context&&(this.context=Me.context),Me.params&&(this.params=Me.params)),this.headers||(this.headers=new ve),this.context||(this.context=new Ke),this.params){const Le=this.params.toString();if(0===Le.length)this.urlWithParams=$;else{const Ut=$.indexOf("?");this.urlWithParams=$+(-1===Ut?"?":Ut<$.length-1?"&":"")+Le}}else this.params=new xe,this.urlWithParams=$}serializeBody(){return null===this.body?null:ke(this.body)||Te(this.body)||ot(this.body)||function rt(Q){return typeof URLSearchParams<"u"&&Q instanceof URLSearchParams}(this.body)||"string"==typeof this.body?this.body:this.body instanceof xe?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||ot(this.body)?null:Te(this.body)?this.body.type||null:ke(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof xe?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(j={}){const $=j.method||this.method,ue=j.url||this.url,Oe=j.responseType||this.responseType,Me=void 0!==j.body?j.body:this.body,Le=void 0!==j.withCredentials?j.withCredentials:this.withCredentials,Ut=void 0!==j.reportProgress?j.reportProgress:this.reportProgress;let tt=j.headers||this.headers,vn=j.params||this.params;const $n=j.context??this.context;return void 0!==j.setHeaders&&(tt=Object.keys(j.setHeaders).reduce((In,pn)=>In.set(pn,j.setHeaders[pn]),tt)),j.setParams&&(vn=Object.keys(j.setParams).reduce((In,pn)=>In.set(pn,j.setParams[pn]),vn)),new Pe($,ue,Me,{params:vn,headers:tt,context:$n,reportProgress:Ut,responseType:Oe,withCredentials:Le})}}var cn=function(Q){return Q[Q.Sent=0]="Sent",Q[Q.UploadProgress=1]="UploadProgress",Q[Q.ResponseHeader=2]="ResponseHeader",Q[Q.DownloadProgress=3]="DownloadProgress",Q[Q.Response=4]="Response",Q[Q.User=5]="User",Q}(cn||{});class et{constructor(j,$=200,ue="OK"){this.headers=j.headers||new ve,this.status=void 0!==j.status?j.status:$,this.statusText=j.statusText||ue,this.url=j.url||null,this.ok=this.status>=200&&this.status<300}}class gt extends et{constructor(j={}){super(j),this.type=cn.ResponseHeader}clone(j={}){return new gt({headers:j.headers||this.headers,status:void 0!==j.status?j.status:this.status,statusText:j.statusText||this.statusText,url:j.url||this.url||void 0})}}class dn extends et{constructor(j={}){super(j),this.type=cn.Response,this.body=void 0!==j.body?j.body:null}clone(j={}){return new dn({body:void 0!==j.body?j.body:this.body,headers:j.headers||this.headers,status:void 0!==j.status?j.status:this.status,statusText:j.statusText||this.statusText,url:j.url||this.url||void 0})}}class jt extends et{constructor(j){super(j,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${j.url||"(unknown url)"}`:`Http failure response for ${j.url||"(unknown url)"}: ${j.status} ${j.statusText}`,this.error=j.error||null}}function Mn(Q,j){return{body:j,headers:Q.headers,context:Q.context,observe:Q.observe,params:Q.params,reportProgress:Q.reportProgress,responseType:Q.responseType,withCredentials:Q.withCredentials}}let Sn=(()=>{class Q{constructor($){this.handler=$}request($,ue,Oe={}){let Me;if($ instanceof Pe)Me=$;else{let tt,vn;tt=Oe.headers instanceof ve?Oe.headers:new ve(Oe.headers),Oe.params&&(vn=Oe.params instanceof xe?Oe.params:new xe({fromObject:Oe.params})),Me=new Pe($,ue,void 0!==Oe.body?Oe.body:null,{headers:tt,context:Oe.context,params:vn,reportProgress:Oe.reportProgress,responseType:Oe.responseType||"json",withCredentials:Oe.withCredentials})}const Le=(0,C.of)(Me).pipe((0,X.H)(tt=>this.handler.handle(tt)));if($ instanceof Pe||"events"===Oe.observe)return Le;const Ut=Le.pipe((0,ne.p)(tt=>tt instanceof dn));switch(Oe.observe||"body"){case"body":switch(Me.responseType){case"arraybuffer":return Ut.pipe((0,ee.T)(tt=>{if(null!==tt.body&&!(tt.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return tt.body}));case"blob":return Ut.pipe((0,ee.T)(tt=>{if(null!==tt.body&&!(tt.body instanceof Blob))throw new Error("Response is not a Blob.");return tt.body}));case"text":return Ut.pipe((0,ee.T)(tt=>{if(null!==tt.body&&"string"!=typeof tt.body)throw new Error("Response is not a string.");return tt.body}));default:return Ut.pipe((0,ee.T)(tt=>tt.body))}case"response":return Ut;default:throw new Error(`Unreachable: unhandled observe type ${Oe.observe}}`)}}delete($,ue={}){return this.request("DELETE",$,ue)}get($,ue={}){return this.request("GET",$,ue)}head($,ue={}){return this.request("HEAD",$,ue)}jsonp($,ue){return this.request("JSONP",$,{params:(new xe).append(ue,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options($,ue={}){return this.request("OPTIONS",$,ue)}patch($,ue,Oe={}){return this.request("PATCH",$,Mn(Oe,ue))}post($,ue,Oe={}){return this.request("POST",$,Mn(Oe,ue))}put($,ue,Oe={}){return this.request("PUT",$,Mn(Oe,ue))}static{this.\u0275fac=function(ue){return new(ue||Q)(c.KVO(H))}}static{this.\u0275prov=c.jDH({token:Q,factory:Q.\u0275fac})}}return Q})();function Ge(Q,j){return j(Q)}function ft(Q,j){return($,ue)=>j.intercept($,{handle:Oe=>Q(Oe,ue)})}const ut=new c.nKC(""),xt=new c.nKC(""),Qt=new c.nKC("");function tn(){let Q=null;return(j,$)=>{null===Q&&(Q=((0,c.WQX)(ut,{optional:!0})??[]).reduceRight(ft,Ge));const ue=(0,c.WQX)(c.$K3),Oe=ue.add();return Q(j,$).pipe((0,de.j)(()=>ue.remove(Oe)))}}let Ae=(()=>{class Q extends H{constructor($,ue){super(),this.backend=$,this.injector=ue,this.chain=null,this.pendingTasks=(0,c.WQX)(c.$K3)}handle($){if(null===this.chain){const Oe=Array.from(new Set([...this.injector.get(xt),...this.injector.get(Qt,[])]));this.chain=Oe.reduceRight((Me,Le)=>function Bt(Q,j,$){return(ue,Oe)=>$.runInContext(()=>j(ue,Me=>Q(Me,Oe)))}(Me,Le,this.injector),Ge)}const ue=this.pendingTasks.add();return this.chain($,Oe=>this.backend.handle(Oe)).pipe((0,de.j)(()=>this.pendingTasks.remove(ue)))}static{this.\u0275fac=function(ue){return new(ue||Q)(c.KVO(G),c.KVO(c.uvJ))}}static{this.\u0275prov=c.jDH({token:Q,factory:Q.\u0275fac})}}return Q})();const Fn=/^\)\]\}',?\n/;let an=(()=>{class Q{constructor($){this.xhrFactory=$}handle($){if("JSONP"===$.method)throw new c.wOt(-2800,!1);const ue=this.xhrFactory;return(ue.\u0275loadImpl?(0,ce.H)(ue.\u0275loadImpl()):(0,C.of)(null)).pipe((0,le.n)(()=>new se.c(Me=>{const Le=ue.build();if(Le.open($.method,$.urlWithParams),$.withCredentials&&(Le.withCredentials=!0),$.headers.forEach((ct,Je)=>Le.setRequestHeader(ct,Je.join(","))),$.headers.has("Accept")||Le.setRequestHeader("Accept","application/json, text/plain, */*"),!$.headers.has("Content-Type")){const ct=$.detectContentTypeHeader();null!==ct&&Le.setRequestHeader("Content-Type",ct)}if($.responseType){const ct=$.responseType.toLowerCase();Le.responseType="json"!==ct?ct:"text"}const Ut=$.serializeBody();let tt=null;const vn=()=>{if(null!==tt)return tt;const ct=Le.statusText||"OK",Je=new ve(Le.getAllResponseHeaders()),Qn=function sn(Q){return"responseURL"in Q&&Q.responseURL?Q.responseURL:/^X-Request-URL:/m.test(Q.getAllResponseHeaders())?Q.getResponseHeader("X-Request-URL"):null}(Le)||$.url;return tt=new gt({headers:Je,status:Le.status,statusText:ct,url:Qn}),tt},$n=()=>{let{headers:ct,status:Je,statusText:Qn,url:ni}=vn(),Tn=null;204!==Je&&(Tn=typeof Le.response>"u"?Le.responseText:Le.response),0===Je&&(Je=Tn?200:0);let dr=Je>=200&&Je<300;if("json"===$.responseType&&"string"==typeof Tn){const fr=Tn;Tn=Tn.replace(Fn,"");try{Tn=""!==Tn?JSON.parse(Tn):null}catch(zr){Tn=fr,dr&&(dr=!1,Tn={error:zr,text:Tn})}}dr?(Me.next(new dn({body:Tn,headers:ct,status:Je,statusText:Qn,url:ni||void 0})),Me.complete()):Me.error(new jt({error:Tn,headers:ct,status:Je,statusText:Qn,url:ni||void 0}))},In=ct=>{const{url:Je}=vn(),Qn=new jt({error:ct,status:Le.status||0,statusText:Le.statusText||"Unknown Error",url:Je||void 0});Me.error(Qn)};let pn=!1;const Tr=ct=>{pn||(Me.next(vn()),pn=!0);let Je={type:cn.DownloadProgress,loaded:ct.loaded};ct.lengthComputable&&(Je.total=ct.total),"text"===$.responseType&&Le.responseText&&(Je.partialText=Le.responseText),Me.next(Je)},wn=ct=>{let Je={type:cn.UploadProgress,loaded:ct.loaded};ct.lengthComputable&&(Je.total=ct.total),Me.next(Je)};return Le.addEventListener("load",$n),Le.addEventListener("error",In),Le.addEventListener("timeout",In),Le.addEventListener("abort",In),$.reportProgress&&(Le.addEventListener("progress",Tr),null!==Ut&&Le.upload&&Le.upload.addEventListener("progress",wn)),Le.send(Ut),Me.next({type:cn.Sent}),()=>{Le.removeEventListener("error",In),Le.removeEventListener("abort",In),Le.removeEventListener("load",$n),Le.removeEventListener("timeout",In),$.reportProgress&&(Le.removeEventListener("progress",Tr),null!==Ut&&Le.upload&&Le.upload.removeEventListener("progress",wn)),Le.readyState!==Le.DONE&&Le.abort()}})))}static{this.\u0275fac=function(ue){return new(ue||Q)(c.KVO(fe.N0))}}static{this.\u0275prov=c.jDH({token:Q,factory:Q.\u0275fac})}}return Q})();const En=new c.nKC("XSRF_ENABLED"),Xt=new c.nKC("XSRF_COOKIE_NAME",{providedIn:"root",factory:()=>"XSRF-TOKEN"}),pt=new c.nKC("XSRF_HEADER_NAME",{providedIn:"root",factory:()=>"X-XSRF-TOKEN"});class ti{}let hi=(()=>{class Q{constructor($,ue,Oe){this.doc=$,this.platform=ue,this.cookieName=Oe,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const $=this.doc.cookie||"";return $!==this.lastCookieString&&(this.parseCount++,this.lastToken=(0,fe._b)($,this.cookieName),this.lastCookieString=$),this.lastToken}static{this.\u0275fac=function(ue){return new(ue||Q)(c.KVO(fe.qQ),c.KVO(c.Agw),c.KVO(Xt))}}static{this.\u0275prov=c.jDH({token:Q,factory:Q.\u0275fac})}}return Q})();function nn(Q,j){const $=Q.url.toLowerCase();if(!(0,c.WQX)(En)||"GET"===Q.method||"HEAD"===Q.method||$.startsWith("http://")||$.startsWith("https://"))return j(Q);const ue=(0,c.WQX)(ti).getToken(),Oe=(0,c.WQX)(pt);return null!=ue&&!Q.headers.has(Oe)&&(Q=Q.clone({headers:Q.headers.set(Oe,ue)})),j(Q)}var _t=function(Q){return Q[Q.Interceptors=0]="Interceptors",Q[Q.LegacyInterceptors=1]="LegacyInterceptors",Q[Q.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",Q[Q.NoXsrfProtection=3]="NoXsrfProtection",Q[Q.JsonpSupport=4]="JsonpSupport",Q[Q.RequestsMadeViaParent=5]="RequestsMadeViaParent",Q[Q.Fetch=6]="Fetch",Q}(_t||{});function dt(Q,j){return{\u0275kind:Q,\u0275providers:j}}function pi(...Q){const j=[Sn,an,Ae,{provide:H,useExisting:Ae},{provide:G,useExisting:an},{provide:xt,useValue:nn,multi:!0},{provide:En,useValue:!0},{provide:ti,useClass:hi}];for(const $ of Q)j.push(...$.\u0275providers);return(0,c.EmA)(j)}const Xn=new c.nKC("LEGACY_INTERCEPTOR_FN");let W=(()=>{class Q{static{this.\u0275fac=function(ue){return new(ue||Q)}}static{this.\u0275mod=c.$C({type:Q})}static{this.\u0275inj=c.G2t({providers:[pi(dt(_t.LegacyInterceptors,[{provide:Xn,useFactory:tn},{provide:xt,useExisting:Xn,multi:!0}]))]})}}return Q})()},6276:(qe,ye,O)=>{O.d(ye,{bc$:()=>fC,iLQ:()=>Ud,sZ2:()=>Ch,hnV:()=>by,Hbi:()=>SM,o8S:()=>da,BIS:()=>hC,gRc:()=>dM,Ql9:()=>Nb,Ocv:()=>Tb,Z63:()=>ka,aKT:()=>ja,uvJ:()=>bo,zcH:()=>Zo,bkB:()=>Mo,$GK:()=>Mt,nKC:()=>dt,zZn:()=>fo,_q3:()=>Wd,MKu:()=>Kd,xe9:()=>vu,Co$:()=>Cm,Vns:()=>_s,NEm:()=>rM,SKi:()=>Fr,Xx1:()=>yl,Agw:()=>mc,PLl:()=>_h,sFG:()=>TC,_9s:()=>Ih,czy:()=>Ml,kdw:()=>vl,C4Q:()=>rl,NYb:()=>Yb,giA:()=>Py,RxE:()=>Th,c1b:()=>mu,gXe:()=>wn,L39:()=>LM,Ol2:()=>Em,w6W:()=>D0,oH4:()=>ky,Rfq:()=>Ke,WQX:()=>Me,QuC:()=>bn,EmA:()=>uc,fpN:()=>MM,HJs:()=>jM,O8t:()=>_,H3F:()=>My,H8p:()=>fc,$K3:()=>Sy,KH2:()=>aa,wOt:()=>Pe,WHO:()=>Oy,e01:()=>Ny,H5H:()=>hd,mq5:()=>Gg,JZv:()=>nn,LfX:()=>ur,plB:()=>Od,jNT:()=>nd,zjR:()=>Xp,TL$:()=>RD,Tbb:()=>yt,Vt3:()=>Gc,GFd:()=>Ip,OA$:()=>L,Jv_:()=>Dm,aNF:()=>wm,R7$:()=>Jh,BMQ:()=>Kc,HbH:()=>mg,ZvI:()=>Pg,STu:()=>Rg,AVh:()=>ud,wni:()=>ry,VBU:()=>Ji,FsC:()=>vi,jDH:()=>Re,G2t:()=>lr,$C:()=>Pi,EJ8:()=>Kr,rXU:()=>Xs,nrm:()=>td,bVm:()=>su,qex:()=>ou,k0s:()=>iu,j41:()=>ru,RV6:()=>Kp,xGo:()=>pf,KVO:()=>ue,kS0:()=>Fu,QTQ:()=>ep,bIt:()=>rd,lsd:()=>iy,XpG:()=>Yp,nI1:()=>Um,bMT:()=>Bm,i5U:()=>$m,brH:()=>Hm,Y8G:()=>Yc,lJ4:()=>Om,eq3:()=>Nm,l_i:()=>Pm,sMw:()=>Rm,ziG:()=>xm,mGM:()=>ty,sdS:()=>Hp,Njj:()=>va,eBV:()=>bu,B4B:()=>ac,n$t:()=>fh,xc7:()=>ld,DNE:()=>Up,EFF:()=>wg,JRh:()=>dd,SpI:()=>uu,Lme:()=>fd,GBs:()=>ny});var c=O(3794),C=O(8359),ce=O(1985),se=O(6365),X=O(8750),ne=O(983),ee=O(9326),de=O(6648),fe=O(4412),H=O(7673),G=O(7707),ve=O(9974);function _e(e={}){const{connector:t=(()=>new c.B),resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:i=!0}=e;return o=>{let s,d,p,E=0,A=!1,x=!1;const z=()=>{d?.unsubscribe(),d=void 0},K=()=>{z(),s=p=void 0,A=x=!1},me=()=>{const Se=s;K(),Se?.unsubscribe()};return(0,ve.N)((Se,$e)=>{E++,!x&&!A&&z();const nt=p=p??t();$e.add(()=>{E--,0===E&&!x&&!A&&(d=Ve(me,i))}),nt.subscribe($e),!s&&E>0&&(s=new G.Ms({next:Ce=>nt.next(Ce),error:Ce=>{x=!0,z(),d=Ve(K,n,Ce),nt.error(Ce)},complete:()=>{A=!0,z(),d=Ve(K,r),nt.complete()}}),(0,X.Tg)(Se).subscribe(s))})(o)}}function Ve(e,t,...n){if(!0===t)return void e();if(!1===t)return;const r=new G.Ms({next:()=>{r.unsubscribe(),e()}});return(0,X.Tg)(t(...n)).subscribe(r)}var Vt=O(5558),bt=O(3294);function Ze(e){for(let t in e)if(e[t]===Ze)return t;throw Error("Could not find renamed property on target object.")}function Rt(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function yt(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(yt).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function xe(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const Ne=Ze({__forward_ref__:Ze});function Ke(e){return e.__forward_ref__=Ke,e.toString=function(){return yt(this())},e}function we(e){return ke(e)?e():e}function ke(e){return"function"==typeof e&&e.hasOwnProperty(Ne)&&e.__forward_ref__===Ke}function Te(e){return e&&!!e.\u0275providers}const rt="https://g.co/ng/security#xss";class Pe extends Error{constructor(t,n){super(function cn(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}(t,n)),this.code=t}}function et(e){return"string"==typeof e?e:null==e?"":String(e)}function Sn(e,t){throw new Pe(-201,!1)}function tn(e,t){null==e&&function Ae(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}(t,e,null,"!=")}function Re(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function lr(e){return{providers:e.providers||[],imports:e.imports||[]}}function _n(e){return vt(e,Fn)||vt(e,an)}function ur(e){return null!==_n(e)}function vt(e,t){return e.hasOwnProperty(t)?e[t]:null}function nr(e){return e&&(e.hasOwnProperty(sn)||e.hasOwnProperty(En))?e[sn]:null}const Fn=Ze({\u0275prov:Ze}),sn=Ze({\u0275inj:Ze}),an=Ze({ngInjectableDef:Ze}),En=Ze({ngInjectorDef:Ze});var Mt=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(Mt||{});let Xt;function pt(e){const t=Xt;return Xt=e,t}function ti(e,t,n){const r=_n(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&Mt.Optional?null:void 0!==t?t:void Sn(yt(e))}const nn=globalThis;class dt{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=Re({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const pe={},Fe="__NG_DI_FLAG__",We="ngTempTokenPath",$t=/\n/gm,Bn="__source";let kn;function j(e){const t=kn;return kn=e,t}function $(e,t=Mt.Default){if(void 0===kn)throw new Pe(-203,!1);return null===kn?ti(e,void 0,t):kn.get(e,t&Mt.Optional?null:void 0,t)}function ue(e,t=Mt.Default){return(function Oi(){return Xt}()||$)(we(e),t)}function Me(e,t=Mt.Default){return ue(e,Le(t))}function Le(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ut(e){const t=[];for(let n=0;n<e.length;n++){const r=we(e[n]);if(Array.isArray(r)){if(0===r.length)throw new Pe(900,!1);let i,o=Mt.Default;for(let s=0;s<r.length;s++){const d=r[s],p=vn(d);"number"==typeof p?-1===p?i=d.token:o|=p:i=d}t.push(ue(i,o))}else t.push(ue(r))}return t}function tt(e,t){return e[Fe]=t,e.prototype[Fe]=t,e}function vn(e){return e[Fe]}function pn(e){return{toString:e}.toString()}var Tr=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Tr||{}),wn=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(wn||{});const ct={},Je=[],Qn=Ze({\u0275cmp:Ze}),ni=Ze({\u0275dir:Ze}),Tn=Ze({\u0275pipe:Ze}),dr=Ze({\u0275mod:Ze}),fr=Ze({\u0275fac:Ze}),zr=Ze({__NG_ELEMENT_ID__:Ze}),Vr=Ze({__NG_ENV_ID__:Ze});function Ki(e,t,n){let r=e.length;for(;;){const i=e.indexOf(t,n);if(-1===i)return i;if(0===i||e.charCodeAt(i-1)<=32){const o=t.length;if(i+o===r||e.charCodeAt(i+o)<=32)return i}n=i+1}}function ri(e,t,n){let r=0;for(;r<n.length;){const i=n[r];if("number"==typeof i){if(0!==i)break;r++;const o=n[r++],s=n[r++],d=n[r++];e.setAttribute(t,s,d,o)}else{const o=i,s=n[++r];qn(o)?e.setProperty(t,o,s):e.setAttribute(t,o,s),r++}}return r}function Xi(e){return 3===e||4===e||6===e}function qn(e){return 64===e.charCodeAt(0)}function mr(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const i=t[r];"number"==typeof i?n=i:0===n||Ni(e,n,i,null,-1===n||2===n?t[++r]:null)}}return e}function Ni(e,t,n,r,i){let o=0,s=e.length;if(-1===t)s=-1;else for(;o<e.length;){const d=e[o++];if("number"==typeof d){if(d===t){s=-1;break}if(d>t){s=o-1;break}}}for(;o<e.length;){const d=e[o];if("number"==typeof d)break;if(d===n){if(null===r)return void(null!==i&&(e[o+1]=i));if(r===e[o+1])return void(e[o+2]=i)}o++,null!==r&&o++,null!==i&&o++}-1!==s&&(e.splice(s,0,t),o=s+1),e.splice(o++,0,n),null!==r&&e.splice(o++,0,r),null!==i&&e.splice(o++,0,i)}const Qi="ng-template";function Ar(e,t,n){let r=0,i=!0;for(;r<e.length;){let o=e[r++];if("string"==typeof o&&i){const s=e[r++];if(n&&"class"===o&&-1!==Ki(s.toLowerCase(),t,0))return!0}else{if(1===o){for(;r<e.length&&"string"==typeof(o=e[r++]);)if(o.toLowerCase()===t)return!0;return!1}"number"==typeof o&&(i=!1)}}return!1}function yr(e){return 4===e.type&&e.value!==Qi}function qi(e,t,n){return t===(4!==e.type||n?e.value:Qi)}function jr(e,t,n){let r=4;const i=e.attrs||[],o=function No(e){for(let t=0;t<e.length;t++)if(Xi(e[t]))return t;return e.length}(i);let s=!1;for(let d=0;d<t.length;d++){const p=t[d];if("number"!=typeof p){if(!s)if(4&r){if(r=2|1&r,""!==p&&!qi(e,p,n)||""===p&&1===t.length){if(ir(r))return!1;s=!0}}else{const E=8&r?p:t[++d];if(8&r&&null!==e.attrs){if(!Ar(e.attrs,E,n)){if(ir(r))return!1;s=!0}continue}const x=Hn(8&r?"class":p,i,yr(e),n);if(-1===x){if(ir(r))return!1;s=!0;continue}if(""!==E){let z;z=x>o?"":i[x+1].toLowerCase();const K=8&r?z:null;if(K&&-1!==Ki(K,E,0)||2&r&&E!==z){if(ir(r))return!1;s=!0}}}}else{if(!s&&!ir(r)&&!ir(p))return!1;if(s&&ir(p))continue;s=!1,r=p|1&r}}return ir(r)||s}function ir(e){return 0==(1&e)}function Hn(e,t,n,r){if(null===t)return-1;let i=0;if(r||!n){let o=!1;for(;i<t.length;){const s=t[i];if(s===e)return i;if(3===s||6===s)o=!0;else{if(1===s||2===s){let d=t[++i];for(;"string"==typeof d;)d=t[++i];continue}if(4===s)break;if(0===s){i+=4;continue}}i+=o?1:2}return-1}return function Gr(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function Zi(e,t,n=!1){for(let r=0;r<t.length;r++)if(jr(e,t[r],n))return!0;return!1}function gi(e,t){return e?":not("+t.trim()+")":t}function mi(e){let t=e[0],n=1,r=2,i="",o=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&r){const d=e[++n];i+="["+s+(d.length>0?'="'+d+'"':"")+"]"}else 8&r?i+="."+s:4&r&&(i+=" "+s);else""!==i&&!ir(s)&&(t+=gi(o,i),i=""),r=s,o=o||!ir(r);n++}return""!==i&&(t+=gi(o,i)),t}function Ji(e){return pn(()=>{const t=ii(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Tr.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||wn.Emulated,styles:e.styles||Je,_:null,schemas:e.schemas||null,tView:null,id:""};Ri(n);const r=e.dependencies;return n.directiveDefs=Xr(r,!1),n.pipeDefs=Xr(r,!0),n.id=function ao(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const i of n)t=Math.imul(31,t)+i.charCodeAt(0)<<0;return t+=2147483648,"c"+t}(n),n})}function $o(e){return Nt(e)||gn(e)}function yi(e){return null!==e}function Pi(e){return pn(()=>({type:e.type,bootstrap:e.bootstrap||Je,declarations:e.declarations||Je,imports:e.imports||Je,exports:e.exports||Je,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function so(e,t){if(null==e)return ct;const n={};for(const r in e)if(e.hasOwnProperty(r)){let i=e[r],o=i;Array.isArray(i)&&(o=i[1],i=i[0]),n[i]=r,t&&(t[i]=o)}return n}function vi(e){return pn(()=>{const t=ii(e);return Ri(t),t})}function Kr(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function Nt(e){return e[Qn]||null}function gn(e){return e[ni]||null}function Ln(e){return e[Tn]||null}function bn(e){const t=Nt(e)||gn(e)||Ln(e);return null!==t&&t.standalone}function An(e,t){const n=e[dr]||null;if(!n&&!0===t)throw new Error(`Type ${yt(e)} does not have '\u0275mod' property.`);return n}function ii(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||ct,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||Je,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:so(e.inputs,t),outputs:so(e.outputs)}}function Ri(e){e.features?.forEach(t=>t(e))}function Xr(e,t){if(!e)return null;const n=t?Ln:$o;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(yi)}const rn=0,je=1,Dt=2,qt=3,Zn=4,Qr=5,On=6,Br=7,Dn=8,Or=9,Nr=10,mt=11,vr=12,lo=13,Dr=14,Yt=15,oi=16,qr=17,hr=18,Vn=19,Zr=20,Jn=21,Pr=22,Yi=23,xi=24,Ot=25,go=1,si=2,Rr=7,zn=9,Lt=11;function Yn(e){return Array.isArray(e)&&"object"==typeof e[go]}function or(e){return Array.isArray(e)&&!0===e[go]}function eo(e){return 0!=(4&e.flags)}function Cr(e){return e.componentOffset>-1}function Li(e){return 1==(1&e.flags)}function pr(e){return!!e.template}function Di(e){return 0!=(512&e[Dt])}function Bi(e,t){return e.hasOwnProperty(fr)?e[fr]:null}let sr=null,$i=!1;function _r(e){const t=sr;return sr=e,t}const gr={version:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{}};function os(e){if(!vo(e)||e.dirty){if(!e.producerMustRecompute(e)&&!Go(e))return void(e.dirty=!1);e.producerRecomputeValue(e),e.dirty=!1}}function uo(e){e.dirty=!0,function ss(e){if(void 0===e.liveConsumerNode)return;const t=$i;$i=!0;try{for(const n of e.liveConsumerNode)n.dirty||uo(n)}finally{$i=t}}(e),e.consumerMarkedDirty?.(e)}function Fo(e){return e&&(e.nextProducerIndex=0),_r(e)}function co(e,t){if(_r(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(vo(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)ko(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Go(e){Ei(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(os(n),r!==n.version))return!0}return!1}function as(e){if(Ei(e),vo(e))for(let t=0;t<e.producerNode.length;t++)ko(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function ko(e,t){if(function us(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),Ei(e),1===e.liveConsumerNode.length)for(let r=0;r<e.producerNode.length;r++)ko(e.producerNode[r],e.producerIndexOfThis[r]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[t],i=e.liveConsumerNode[t];Ei(i),i.producerIndexOfThis[r]=t}}function vo(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Ei(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let T=null;function _(e){const t=_r(null);try{return e()}finally{_r(t)}}const l=()=>{},y=(()=>({...gr,consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:e=>{e.schedule(e.ref)},hasRun:!1,cleanupFn:l}))();class R{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function L(){return U}function U(e){return e.type.prototype.ngOnChanges&&(e.setInput=ge),J}function J(){const e=He(this),t=e?.current;if(t){const n=e.previous;if(n===ct)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function ge(e,t,n,r){const i=this.declaredInputs[n],o=He(e)||function Ft(e,t){return e[Ue]=t}(e,{previous:ct,current:null}),s=o.current||(o.current={}),d=o.previous,p=d[i];s[i]=new R(p&&p.currentValue,t,d===ct),e[r]=t}L.ngInherit=!0;const Ue="__ngSimpleChanges__";function He(e){return e[Ue]||null}const at=function(e,t,n){};function St(e){for(;Array.isArray(e);)e=e[rn];return e}function Gt(e,t){return St(t[e])}function Gn(e,t){return St(t[e.index])}function Wn(e,t){return e.data[t]}function Er(e,t){return e[t]}function Nn(e,t){const n=t[e];return Yn(n)?n:n[rn]}function xr(e,t){return null==t?null:e[t]}function Lo(e){e[qr]=0}function cs(e){1024&e[Dt]||(e[Dt]|=1024,lt(e,1))}function Ye(e){1024&e[Dt]&&(e[Dt]&=-1025,lt(e,-1))}function lt(e,t){let n=e[qt];if(null===n)return;n[Qr]+=t;let r=n;for(n=n[qt];null!==n&&(1===t&&1===r[Qr]||-1===t&&0===r[Qr]);)n[Qr]+=t,r=n,n=n[qt]}const Xe={lFrame:Et(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Ms(){return Xe.bindingsEnabled}function Ee(){return Xe.lFrame.lView}function Wt(){return Xe.lFrame.tView}function bu(e){return Xe.lFrame.contextLView=e,e[Dn]}function va(e){return Xe.lFrame.contextLView=null,e}function wr(){let e=sl();for(;null!==e&&64===e.type;)e=e.parent;return e}function sl(){return Xe.lFrame.currentTNode}function to(e,t){const n=Xe.lFrame;n.currentTNode=e,n.isParent=t}function Da(){return Xe.lFrame.isParent}function Ca(){Xe.lFrame.isParent=!1}function Hr(){const e=Xe.lFrame;let t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function fs(){return Xe.lFrame.bindingIndex++}function Eo(e){const t=Xe.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function f(e,t){const n=Xe.lFrame;n.bindingIndex=n.bindingRootIndex=e,u(t)}function u(e){Xe.lFrame.currentDirectiveIndex=e}function w(){return Xe.lFrame.currentQueryIndex}function N(e){Xe.lFrame.currentQueryIndex=e}function F(e){const t=e[je];return 2===t.type?t.declTNode:1===t.type?e[On]:null}function re(e,t,n){if(n&Mt.SkipSelf){let i=t,o=e;for(;!(i=i.parent,null!==i||n&Mt.Host||(i=F(o),null===i||(o=o[Dr],10&i.type))););if(null===i)return!1;t=i,e=o}const r=Xe.lFrame=Be();return r.currentTNode=t,r.lView=e,!0}function Y(e){const t=Be(),n=e[je];Xe.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function Be(){const e=Xe.lFrame,t=null===e?null:e.child;return null===t?Et(e):t}function Et(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function un(){const e=Xe.lFrame;return Xe.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const Kt=un;function zi(){const e=un();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function br(){return Xe.lFrame.selectedIndex}function Mi(e){Xe.lFrame.selectedIndex=e}function Rn(){const e=Xe.lFrame;return Wn(e.tView,e.selectedIndex)}let ef=!0;function al(){return ef}function Qo(e){ef=e}function ll(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const o=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:d,ngAfterViewInit:p,ngAfterViewChecked:E,ngOnDestroy:A}=o;s&&(e.contentHooks??=[]).push(-n,s),d&&((e.contentHooks??=[]).push(n,d),(e.contentCheckHooks??=[]).push(n,d)),p&&(e.viewHooks??=[]).push(-n,p),E&&((e.viewHooks??=[]).push(n,E),(e.viewCheckHooks??=[]).push(n,E)),null!=A&&(e.destroyHooks??=[]).push(n,A)}}function ul(e,t,n){tf(e,t,3,n)}function cl(e,t,n,r){(3&e[Dt])===n&&tf(e,t,n,r)}function Tu(e,t){let n=e[Dt];(3&n)===t&&(n&=8191,n+=1,e[Dt]=n)}function tf(e,t,n,r){const o=r??-1,s=t.length-1;let d=0;for(let p=void 0!==r?65535&e[qr]:0;p<s;p++)if("number"==typeof t[p+1]){if(d=t[p],null!=r&&d>=r)break}else t[p]<0&&(e[qr]+=65536),(d<o||-1==o)&&(pv(e,n,t,p),e[qr]=(**********&e[qr])+p+2),p++}function nf(e,t){at(4,e,t);const n=_r(null);try{t.call(e)}finally{_r(n),at(5,e,t)}}function pv(e,t,n,r){const i=n[r]<0,o=n[r+1],d=e[i?-n[r]:n[r]];i?e[Dt]>>13<e[qr]>>16&&(3&e[Dt])===t&&(e[Dt]+=8192,nf(d,o)):nf(d,o)}const Is=-1;class Ea{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function Ou(e){return e!==Is}function wa(e){return 32767&e}function ba(e,t){let n=function vv(e){return e>>16}(e),r=t;for(;n>0;)r=r[Dr],n--;return r}let Nu=!0;function dl(e){const t=Nu;return Nu=e,t}const rf=255,sf=5;let Dv=0;const wo={};function fl(e,t){const n=af(e,t);if(-1!==n)return n;const r=t[je];r.firstCreatePass&&(e.injectorIndex=t.length,Pu(r.data,e),Pu(t,null),Pu(r.blueprint,null));const i=hl(e,t),o=e.injectorIndex;if(Ou(i)){const s=wa(i),d=ba(i,t),p=d[je].data;for(let E=0;E<8;E++)t[o+E]=d[s+E]|p[s+E]}return t[o+8]=i,o}function Pu(e,t){e.push(0,0,0,0,0,0,0,0,t)}function af(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function hl(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,i=t;for(;null!==i;){if(r=gf(i),null===r)return Is;if(n++,i=i[Dr],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return Is}function Ru(e,t,n){!function Cv(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(zr)&&(r=n[zr]),null==r&&(r=n[zr]=Dv++);const i=r&rf;t.data[e+(i>>sf)]|=1<<i}(e,t,n)}function lf(e,t,n){if(n&Mt.Optional||void 0!==e)return e;Sn()}function uf(e,t,n,r){if(n&Mt.Optional&&void 0===r&&(r=null),!(n&(Mt.Self|Mt.Host))){const i=e[Or],o=pt(void 0);try{return i?i.get(t,r,n&Mt.Optional):ti(t,r,n&Mt.Optional)}finally{pt(o)}}return lf(r,0,n)}function cf(e,t,n,r=Mt.Default,i){if(null!==e){if(2048&t[Dt]&&!(r&Mt.Self)){const s=function Sv(e,t,n,r,i){let o=e,s=t;for(;null!==o&&null!==s&&2048&s[Dt]&&!(512&s[Dt]);){const d=df(o,s,n,r|Mt.Self,wo);if(d!==wo)return d;let p=o.parent;if(!p){const E=s[Zr];if(E){const A=E.get(n,wo,r);if(A!==wo)return A}p=gf(s),s=s[Dr]}o=p}return i}(e,t,n,r,wo);if(s!==wo)return s}const o=df(e,t,n,r,wo);if(o!==wo)return o}return uf(t,n,r,i)}function df(e,t,n,r,i){const o=function wv(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(zr)?e[zr]:void 0;return"number"==typeof t?t>=0?t&rf:Mv:t}(n);if("function"==typeof o){if(!re(t,e,r))return r&Mt.Host?lf(i,0,r):uf(t,n,r,i);try{let s;if(s=o(r),null!=s||r&Mt.Optional)return s;Sn()}finally{Kt()}}else if("number"==typeof o){let s=null,d=af(e,t),p=Is,E=r&Mt.Host?t[Yt][On]:null;for((-1===d||r&Mt.SkipSelf)&&(p=-1===d?hl(e,t):t[d+8],p!==Is&&hf(r,!1)?(s=t[je],d=wa(p),t=ba(p,t)):d=-1);-1!==d;){const A=t[je];if(ff(o,d,A.data)){const x=Ev(d,t,n,s,r,E);if(x!==wo)return x}p=t[d+8],p!==Is&&hf(r,t[je].data[d+8]===E)&&ff(o,d,t)?(s=A,d=wa(p),t=ba(p,t)):d=-1}}return i}function Ev(e,t,n,r,i,o){const s=t[je],d=s.data[e+8],A=pl(d,s,n,null==r?Cr(d)&&Nu:r!=s&&0!=(3&d.type),i&Mt.Host&&o===d);return null!==A?gs(t,s,A,d):wo}function pl(e,t,n,r,i){const o=e.providerIndexes,s=t.data,d=1048575&o,p=e.directiveStart,A=o>>20,z=i?d+A:e.directiveEnd;for(let K=r?d:d+A;K<z;K++){const me=s[K];if(K<p&&n===me||K>=p&&me.type===n)return K}if(i){const K=s[p];if(K&&pr(K)&&K.type===n)return p}return null}function gs(e,t,n,r){let i=e[n];const o=t.data;if(function gv(e){return e instanceof Ea}(i)){const s=i;s.resolving&&function dn(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new Pe(-200,`Circular dependency in DI detected for ${e}${n}`)}(function gt(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():et(e)}(o[n]));const d=dl(s.canSeeViewProviders);s.resolving=!0;const E=s.injectImpl?pt(s.injectImpl):null;re(e,r,Mt.Default);try{i=e[n]=s.factory(void 0,o,e,r),t.firstCreatePass&&n>=r.directiveStart&&function hv(e,t,n){const{ngOnChanges:r,ngOnInit:i,ngDoCheck:o}=t.type.prototype;if(r){const s=U(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}i&&(n.preOrderHooks??=[]).push(0-e,i),o&&((n.preOrderHooks??=[]).push(e,o),(n.preOrderCheckHooks??=[]).push(e,o))}(n,o[n],t)}finally{null!==E&&pt(E),dl(d),s.resolving=!1,Kt()}}return i}function ff(e,t,n){return!!(n[t+(e>>sf)]&1<<e)}function hf(e,t){return!(e&Mt.Self||e&Mt.Host&&t)}class di{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return cf(this._tNode,this._lView,t,Le(r),n)}}function Mv(){return new di(wr(),Ee())}function pf(e){return pn(()=>{const t=e.prototype.constructor,n=t[fr]||xu(t),r=Object.prototype;let i=Object.getPrototypeOf(e.prototype).constructor;for(;i&&i!==r;){const o=i[fr]||xu(i);if(o&&o!==n)return o;i=Object.getPrototypeOf(i)}return o=>new o})}function xu(e){return ke(e)?()=>{const t=xu(we(e));return t&&t()}:Bi(e)}function gf(e){const t=e[je],n=t.type;return 2===n?t.declTNode:1===n?e[On]:null}function Fu(e){return function _v(e,t){if("class"===t)return e.classes;if("style"===t)return e.styles;const n=e.attrs;if(n){const r=n.length;let i=0;for(;i<r;){const o=n[i];if(Xi(o))break;if(0===o)i+=2;else if("number"==typeof o)for(i++;i<r&&"string"==typeof n[i];)i++;else{if(o===t)return n[i+1];i+=2}}}return null}(wr(),e)}const As="__parameters__";function Ns(e,t,n){return pn(()=>{const r=function ku(e){return function(...n){if(e){const r=e(...n);for(const i in r)this[i]=r[i]}}}(t);function i(...o){if(this instanceof i)return r.apply(this,o),this;const s=new i(...o);return d.annotation=s,d;function d(p,E,A){const x=p.hasOwnProperty(As)?p[As]:Object.defineProperty(p,As,{value:[]})[As];for(;x.length<=A;)x.push(null);return(x[A]=x[A]||[]).push(s),p}}return n&&(i.prototype=Object.create(n.prototype)),i.prototype.ngMetadataName=e,i.annotationCls=i,i})}function Rs(e,t){e.forEach(n=>Array.isArray(n)?Rs(n,t):t(n))}function yf(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function gl(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Gi(e,t,n){let r=xs(e,t);return r>=0?e[1|r]=n:(r=~r,function Rv(e,t,n,r){let i=e.length;if(i==t)e.push(n,r);else if(1===i)e.push(r,e[0]),e[0]=n;else{for(i--,e.push(e[i-1],e[i]);i>t;)e[i]=e[i-2],i--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function Lu(e,t){const n=xs(e,t);if(n>=0)return e[1|n]}function xs(e,t){return function vf(e,t,n){let r=0,i=e.length>>n;for(;i!==r;){const o=r+(i-r>>1),s=e[o<<n];if(t===s)return o<<n;s>t?i=o:r=o+1}return~(i<<n)}(e,t,1)}const yl=tt(Ns("Optional"),8),vl=tt(Ns("SkipSelf"),4);function wl(e){return 128==(128&e.flags)}var Ml=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Ml||{});const eD=/^>|^->|<!--|-->|--!>|<!-$/g,tD=/(<|>)/g,nD="\u200b$1\u200b";const $u=new Map;let rD=0;const zu="__ngContext__";function Yr(e,t){Yn(t)?(e[zu]=t[Vn],function oD(e){$u.set(e[Vn],e)}(t)):e[zu]=t}let Gu;function Wu(e,t){return Gu(e,t)}function Oa(e){const t=e[qt];return or(t)?t[qt]:t}function Vf(e){return Uf(e[vr])}function jf(e){return Uf(e[Zn])}function Uf(e){for(;null!==e&&!or(e);)e=e[Zn];return e}function Ls(e,t,n,r,i){if(null!=r){let o,s=!1;or(r)?o=r:Yn(r)&&(s=!0,r=r[rn]);const d=St(r);0===e&&null!==n?null==i?zf(t,n,d):ms(t,n,d,i||null,!0):1===e&&null!==n?ms(t,n,d,i||null,!0):2===e?function Nl(e,t,n){const r=Al(e,t);r&&function bD(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}(t,d,s):3===e&&t.destroyNode(d),null!=o&&function ID(e,t,n,r,i){const o=n[Rr];o!==St(n)&&Ls(t,e,r,o,i);for(let d=Lt;d<n.length;d++){const p=n[d];Pa(p[je],p,e,t,r,o)}}(t,e,o,n,i)}}function Ku(e,t){return e.createComment(function Of(e){return e.replace(eD,t=>t.replace(tD,nD))}(t))}function Il(e,t,n){return e.createElement(t,n)}function $f(e,t){const n=e[zn],r=n.indexOf(t);Ye(t),n.splice(r,1)}function Tl(e,t){if(e.length<=Lt)return;const n=Lt+t,r=e[n];if(r){const i=r[oi];null!==i&&i!==e&&$f(i,r),t>0&&(e[n-1][Zn]=r[Zn]);const o=gl(e,Lt+t);!function mD(e,t){Pa(e,t,t[mt],2,null,null),t[rn]=null,t[On]=null}(r[je],r);const s=o[hr];null!==s&&s.detachView(o[je]),r[qt]=null,r[Zn]=null,r[Dt]&=-129}return r}function Xu(e,t){if(!(256&t[Dt])){const n=t[mt];t[Yi]&&as(t[Yi]),t[xi]&&as(t[xi]),n.destroyNode&&Pa(e,t,n,3,null,null),function DD(e){let t=e[vr];if(!t)return Qu(e[je],e);for(;t;){let n=null;if(Yn(t))n=t[vr];else{const r=t[Lt];r&&(n=r)}if(!n){for(;t&&!t[Zn]&&t!==e;)Yn(t)&&Qu(t[je],t),t=t[qt];null===t&&(t=e),Yn(t)&&Qu(t[je],t),n=t&&t[Zn]}t=n}}(t)}}function Qu(e,t){if(!(256&t[Dt])){t[Dt]&=-129,t[Dt]|=256,function wD(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const i=t[n[r]];if(!(i instanceof Ea)){const o=n[r+1];if(Array.isArray(o))for(let s=0;s<o.length;s+=2){const d=i[o[s]],p=o[s+1];at(4,d,p);try{p.call(d)}finally{at(5,d,p)}}else{at(4,i,o);try{o.call(i)}finally{at(5,i,o)}}}}}(e,t),function ED(e,t){const n=e.cleanup,r=t[Br];if(null!==n)for(let o=0;o<n.length-1;o+=2)if("string"==typeof n[o]){const s=n[o+3];s>=0?r[s]():r[-s].unsubscribe(),o+=2}else n[o].call(r[n[o+1]]);null!==r&&(t[Br]=null);const i=t[Jn];if(null!==i){t[Jn]=null;for(let o=0;o<i.length;o++)(0,i[o])()}}(e,t),1===t[je].type&&t[mt].destroy();const n=t[oi];if(null!==n&&or(t[qt])){n!==t[qt]&&$f(n,t);const r=t[hr];null!==r&&r.detachView(e)}!function sD(e){$u.delete(e[Vn])}(t)}}function qu(e,t,n){return function Hf(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[rn];{const{componentOffset:i}=r;if(i>-1){const{encapsulation:o}=e.data[r.directiveStart+i];if(o===wn.None||o===wn.Emulated)return null}return Gn(r,n)}}(e,t.parent,n)}function ms(e,t,n,r,i){e.insertBefore(t,n,r,i)}function zf(e,t,n){e.appendChild(t,n)}function Gf(e,t,n,r,i){null!==r?ms(e,t,n,r,i):zf(e,t,n)}function Al(e,t){return e.parentNode(t)}let Zu,tc,Rl,Xf=function Kf(e,t,n){return 40&e.type?Gn(e,n):null};function Ol(e,t,n,r){const i=qu(e,r,t),o=t[mt],d=function Wf(e,t,n){return Xf(e,t,n)}(r.parent||t[On],r,t);if(null!=i)if(Array.isArray(n))for(let p=0;p<n.length;p++)Gf(o,i,n[p],d,!1);else Gf(o,i,n,d,!1);void 0!==Zu&&Zu(o,r,t,n,i)}function Na(e,t){if(null!==t){const n=t.type;if(3&n)return Gn(t,e);if(4&n)return Ju(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return Na(e,r);{const i=e[t.index];return or(i)?Ju(-1,i):St(i)}}if(32&n)return Wu(t,e)()||St(e[t.index]);{const r=qf(e,t);return null!==r?Array.isArray(r)?r[0]:Na(Oa(e[Yt]),r):Na(e,t.next)}}return null}function qf(e,t){return null!==t?e[Yt][On].projection[t.projection]:null}function Ju(e,t){const n=Lt+e+1;if(n<t.length){const r=t[n],i=r[je].firstChild;if(null!==i)return Na(r,i)}return t[Rr]}function Yu(e,t,n,r,i,o,s){for(;null!=n;){const d=r[n.index],p=n.type;if(s&&0===t&&(d&&Yr(St(d),r),n.flags|=2),32!=(32&n.flags))if(8&p)Yu(e,t,n.child,r,i,o,!1),Ls(t,e,i,d,o);else if(32&p){const E=Wu(n,r);let A;for(;A=E();)Ls(t,e,i,A,o);Ls(t,e,i,d,o)}else 16&p?Jf(e,t,r,n,i,o):Ls(t,e,i,d,o);n=s?n.projectionNext:n.next}}function Pa(e,t,n,r,i,o){Yu(n,r,e.firstChild,t,i,o,!1)}function Jf(e,t,n,r,i,o){const s=n[Yt],p=s[On].projection[r.projection];if(Array.isArray(p))for(let E=0;E<p.length;E++)Ls(t,e,i,p[E],o);else{let E=p;const A=s[qt];wl(r)&&(E.flags|=128),Yu(e,t,E,A,i,o,!0)}}function Yf(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function eh(e,t,n){const{mergedAttrs:r,classes:i,styles:o}=n;null!==r&&ri(e,t,r),null!==i&&Yf(e,t,i),null!==o&&function AD(e,t,n){e.setAttribute(t,"style",n)}(e,t,o)}function RD(e){tc=e}function rh(e){return function nc(){if(void 0===Rl&&(Rl=null,nn.trustedTypes))try{Rl=nn.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Rl}()?.createScriptURL(e)||e}class ih{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${rt})`}}function qo(e){return e instanceof ih?e.changingThisBreaksApplicationSecurity:e}function Ra(e,t){const n=function jD(e){return e instanceof ih&&e.getTypeName()||null}(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${rt})`)}return n===t}const HD=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;var Us=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Us||{});function ac(e){const t=Fa();return t?t.sanitize(Us.URL,e)||"":Ra(e,"URL")?qo(e):function rc(e){return(e=String(e)).match(HD)?e:"unsafe:"+e}(et(e))}function dh(e){const t=Fa();if(t)return rh(t.sanitize(Us.RESOURCE_URL,e)||"");if(Ra(e,"ResourceURL"))return rh(qo(e));throw new Pe(904,!1)}function fh(e,t,n){return function tC(e,t){return"src"===t&&("embed"===e||"frame"===e||"iframe"===e||"media"===e||"script"===e)||"href"===t&&("base"===e||"link"===e)?dh:ac}(t,n)(e)}function Fa(){const e=Ee();return e&&e[Nr].sanitizer}const ka=new dt("ENVIRONMENT_INITIALIZER"),hh=new dt("INJECTOR",-1),ph=new dt("INJECTOR_DEF_TYPES");class lc{get(t,n=pe){if(n===pe){const r=new Error(`NullInjectorError: No provider for ${yt(t)}!`);throw r.name="NullInjectorError",r}return n}}function uc(e){return{\u0275providers:e}}function nC(...e){return{\u0275providers:gh(0,e),\u0275fromNgModule:!0}}function gh(e,...t){const n=[],r=new Set;let i;const o=s=>{n.push(s)};return Rs(t,s=>{const d=s;Fl(d,o,[],r)&&(i||=[],i.push(d))}),void 0!==i&&mh(i,o),n}function mh(e,t){for(let n=0;n<e.length;n++){const{ngModule:r,providers:i}=e[n];cc(i,o=>{t(o,r)})}}function Fl(e,t,n,r){if(!(e=we(e)))return!1;let i=null,o=nr(e);const s=!o&&Nt(e);if(o||s){if(s&&!s.standalone)return!1;i=e}else{const p=e.ngModule;if(o=nr(p),!o)return!1;i=p}const d=r.has(i);if(s){if(d)return!1;if(r.add(i),s.dependencies){const p="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const E of p)Fl(E,t,n,r)}}else{if(!o)return!1;{if(null!=o.imports&&!d){let E;r.add(i);try{Rs(o.imports,A=>{Fl(A,t,n,r)&&(E||=[],E.push(A))})}finally{}void 0!==E&&mh(E,t)}if(!d){const E=Bi(i)||(()=>new i);t({provide:i,useFactory:E,deps:Je},i),t({provide:ph,useValue:i,multi:!0},i),t({provide:ka,useValue:()=>ue(i),multi:!0},i)}const p=o.providers;if(null!=p&&!d){const E=e;cc(p,A=>{t(A,E)})}}}return i!==e&&void 0!==e.providers}function cc(e,t){for(let n of e)Te(n)&&(n=n.\u0275providers),Array.isArray(n)?cc(n,t):t(n)}const rC=Ze({provide:String,useValue:Ze});function dc(e){return null!==e&&"object"==typeof e&&rC in e}function ys(e){return"function"==typeof e}const fc=new dt("Set Injector scope."),kl={},oC={};let hc;function Ll(){return void 0===hc&&(hc=new lc),hc}class bo{}class Bs extends bo{get destroyed(){return this._destroyed}constructor(t,n,r,i){super(),this.parent=n,this.source=r,this.scopes=i,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,gc(t,s=>this.processProvider(s)),this.records.set(hh,$s(void 0,this)),i.has("environment")&&this.records.set(bo,$s(void 0,this));const o=this.records.get(fc);null!=o&&"string"==typeof o.value&&this.scopes.add(o.value),this.injectorDefTypes=new Set(this.get(ph.multi,Je,Mt.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const n of this._ngOnDestroyHooks)n.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const n of t)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=j(this),r=pt(void 0);try{return t()}finally{j(n),pt(r)}}get(t,n=pe,r=Mt.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Vr))return t[Vr](this);r=Le(r);const o=j(this),s=pt(void 0);try{if(!(r&Mt.SkipSelf)){let p=this.records.get(t);if(void 0===p){const E=function cC(e){return"function"==typeof e||"object"==typeof e&&e instanceof dt}(t)&&_n(t);p=E&&this.injectableDefInScope(E)?$s(pc(t),kl):null,this.records.set(t,p)}if(null!=p)return this.hydrate(t,p)}return(r&Mt.Self?Ll():this.parent).get(t,n=r&Mt.Optional&&n===pe?null:n)}catch(d){if("NullInjectorError"===d.name){if((d[We]=d[We]||[]).unshift(yt(t)),o)throw d;return function $n(e,t,n,r){const i=e[We];throw t[Bn]&&i.unshift(t[Bn]),e.message=function In(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let i=yt(t);if(Array.isArray(t))i=t.map(yt).join(" -> ");else if("object"==typeof t){let o=[];for(let s in t)if(t.hasOwnProperty(s)){let d=t[s];o.push(s+":"+("string"==typeof d?JSON.stringify(d):yt(d)))}i=`{${o.join(", ")}}`}return`${n}${r?"("+r+")":""}[${i}]: ${e.replace($t,"\n  ")}`}("\n"+e.message,i,n,r),e.ngTokenPath=i,e[We]=null,e}(d,t,"R3InjectorError",this.source)}throw d}finally{pt(s),j(o)}}resolveInjectorInitializers(){const t=j(this),n=pt(void 0);try{const i=this.get(ka.multi,Je,Mt.Self);for(const o of i)o()}finally{j(t),pt(n)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(yt(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new Pe(205,!1)}processProvider(t){let n=ys(t=we(t))?t:we(t&&t.provide);const r=function aC(e){return dc(e)?$s(void 0,e.useValue):$s(Dh(e),kl)}(t);if(ys(t)||!0!==t.multi)this.records.get(n);else{let i=this.records.get(n);i||(i=$s(void 0,kl,!0),i.factory=()=>Ut(i.multi),this.records.set(n,i)),n=t,i.multi.push(t)}this.records.set(n,r)}hydrate(t,n){return n.value===kl&&(n.value=oC,n.value=n.factory()),"object"==typeof n.value&&n.value&&function uC(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=we(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function pc(e){const t=_n(e),n=null!==t?t.factory:Bi(e);if(null!==n)return n;if(e instanceof dt)throw new Pe(204,!1);if(e instanceof Function)return function sC(e){const t=e.length;if(t>0)throw function Ia(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(t,"?"),new Pe(204,!1);const n=function ht(e){return e&&(e[Fn]||e[an])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new Pe(204,!1)}function Dh(e,t,n){let r;if(ys(e)){const i=we(e);return Bi(i)||pc(i)}if(dc(e))r=()=>we(e.useValue);else if(function vh(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...Ut(e.deps||[]));else if(function yh(e){return!(!e||!e.useExisting)}(e))r=()=>ue(we(e.useExisting));else{const i=we(e&&(e.useClass||e.provide));if(!function lC(e){return!!e.deps}(e))return Bi(i)||pc(i);r=()=>new i(...Ut(e.deps))}return r}function $s(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function gc(e,t){for(const n of e)Array.isArray(n)?gc(n,t):n&&Te(n)?gc(n.\u0275providers,t):t(n)}const Ch=new dt("AppId",{providedIn:"root",factory:()=>dC}),dC="ng",_h=new dt("Platform Initializer"),mc=new dt("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),fC=new dt("AnimationModuleType"),hC=new dt("CSP nonce",{providedIn:"root",factory:()=>function js(){if(void 0!==tc)return tc;if(typeof document<"u")return document;throw new Pe(210,!1)}().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let Eh=(e,t,n)=>null;function bc(e,t,n=!1){return Eh(e,t,n)}class wC{}class Mh{}class MC{resolveComponentFactory(t){throw function bC(e){const t=Error(`No component factory found for ${yt(e)}.`);return t.ngComponent=e,t}(t)}}let Hl=(()=>{class e{static{this.NULL=new MC}}return e})();function SC(){return Gs(wr(),Ee())}function Gs(e,t){return new ja(Gn(e,t))}let ja=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=SC}}return e})();function IC(e){return e instanceof ja?e.nativeElement:e}class Ih{}let TC=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>function AC(){const e=Ee(),n=Nn(wr().index,e);return(Yn(n)?n:e)[mt]}()}}return e})(),OC=(()=>{class e{static{this.\u0275prov=Re({token:e,providedIn:"root",factory:()=>null})}}return e})();class Th{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const NC=new Th("16.2.12"),Ic={};function Ph(e,t=null,n=null,r){const i=Rh(e,t,n,r);return i.resolveInjectorInitializers(),i}function Rh(e,t=null,n=null,r,i=new Set){const o=[n||Je,nC(e)];return r=r||("object"==typeof e?void 0:yt(e)),new Bs(o,t||Ll(),r||null,i)}let fo=(()=>{class e{static{this.THROW_IF_NOT_FOUND=pe}static{this.NULL=new lc}static create(n,r){if(Array.isArray(n))return Ph({name:""},r,n,"");{const i=n.name??"";return Ph({name:i},n.parent,n.providers,i)}}static{this.\u0275prov=Re({token:e,providedIn:"any",factory:()=>ue(hh)})}static{this.__NG_ELEMENT_ID__=-1}}return e})();function Ac(e){return e.ngOriginalError}class Zo{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Ac(t);for(;n&&Ac(n);)n=Ac(n);return n||null}}function Oc(e){return t=>{setTimeout(e,void 0,t)}}const Mo=class jC extends c.B{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){let i=t,o=n||(()=>null),s=r;if(t&&"object"==typeof t){const p=t;i=p.next?.bind(p),o=p.error?.bind(p),s=p.complete?.bind(p)}this.__isAsync&&(o=Oc(o),i&&(i=Oc(i)),s&&(s=Oc(s)));const d=super.subscribe({next:i,error:o,complete:s});return t instanceof C.yU&&t.add(d),d}};function Fh(...e){}class Fr{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new Mo(!1),this.onMicrotaskEmpty=new Mo(!1),this.onStable=new Mo(!1),this.onError=new Mo(!1),typeof Zone>"u")throw new Pe(908,!1);Zone.assertZonePatched();const i=this;i._nesting=0,i._outer=i._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(i._inner=i._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(i._inner=i._inner.fork(Zone.longStackTraceZoneSpec)),i.shouldCoalesceEventChangeDetection=!r&&n,i.shouldCoalesceRunChangeDetection=r,i.lastRequestAnimationFrameId=-1,i.nativeRequestAnimationFrame=function UC(){const e="function"==typeof nn.requestAnimationFrame;let t=nn[e?"requestAnimationFrame":"setTimeout"],n=nn[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);const i=n[Zone.__symbol__("OriginalDelegate")];i&&(n=i)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function HC(e){const t=()=>{!function $C(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(nn,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,Pc(e),e.isCheckStableRunning=!0,Nc(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),Pc(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,i,o,s,d)=>{if(function GC(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(d))return n.invokeTask(i,o,s,d);try{return kh(e),n.invokeTask(i,o,s,d)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===o.type||e.shouldCoalesceRunChangeDetection)&&t(),Lh(e)}},onInvoke:(n,r,i,o,s,d,p)=>{try{return kh(e),n.invoke(i,o,s,d,p)}finally{e.shouldCoalesceRunChangeDetection&&t(),Lh(e)}},onHasTask:(n,r,i,o)=>{n.hasTask(i,o),r===i&&("microTask"==o.change?(e._hasPendingMicrotasks=o.microTask,Pc(e),Nc(e)):"macroTask"==o.change&&(e.hasPendingMacrotasks=o.macroTask))},onHandleError:(n,r,i,o)=>(n.handleError(i,o),e.runOutsideAngular(()=>e.onError.emit(o)),!1)})}(i)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!Fr.isInAngularZone())throw new Pe(909,!1)}static assertNotInAngularZone(){if(Fr.isInAngularZone())throw new Pe(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,i){const o=this._inner,s=o.scheduleEventTask("NgZoneEvent: "+i,t,BC,Fh,Fh);try{return o.runTask(s,n,r)}finally{o.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const BC={};function Nc(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Pc(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function kh(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Lh(e){e._nesting--,Nc(e)}class zC{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new Mo,this.onMicrotaskEmpty=new Mo,this.onStable=new Mo,this.onError=new Mo}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,i){return t.apply(n,r)}}const Vh=new dt("",{providedIn:"root",factory:jh});function jh(){const e=Me(Fr);let t=!0;return function le(...e){const t=(0,ee.lI)(e),n=(0,ee.R0)(e,1/0),r=e;return r.length?1===r.length?(0,X.Tg)(r[0]):(0,se.U)(n)((0,de.H)(r,t)):ne.w}(new ce.c(i=>{t=e.isStable&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks,e.runOutsideAngular(()=>{i.next(t),i.complete()})}),new ce.c(i=>{let o;e.runOutsideAngular(()=>{o=e.onStable.subscribe(()=>{Fr.assertNotInAngularZone(),queueMicrotask(()=>{!t&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks&&(t=!0,i.next(!0))})})});const s=e.onUnstable.subscribe(()=>{Fr.assertInAngularZone(),t&&(t=!1,e.runOutsideAngular(()=>{i.next(!1)}))});return()=>{o.unsubscribe(),s.unsubscribe()}}).pipe(_e()))}function jo(e){return e instanceof Function?e():e}let Rc=(()=>{class e{constructor(){this.renderDepth=0,this.handler=null}begin(){this.handler?.validateBegin(),this.renderDepth++}end(){this.renderDepth--,0===this.renderDepth&&this.handler?.execute()}ngOnDestroy(){this.handler?.destroy(),this.handler=null}static{this.\u0275prov=Re({token:e,providedIn:"root",factory:()=>new e})}}return e})();function Ua(e){for(;e;){e[Dt]|=64;const t=Oa(e);if(Di(e)&&!t)return e;e=t}return null}const zh=new dt("",{providedIn:"root",factory:()=>!1});let Wl=null;function Xh(e,t){return e[t]??Zh()}function Qh(e,t){const n=Zh();n.producerNode?.length&&(e[t]=Wl,n.lView=e,Wl=qh())}const t_={...gr,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Ua(e.lView)},lView:null};function qh(){return Object.create(t_)}function Zh(){return Wl??=qh(),Wl}const Pt={};function Jh(e){Yh(Wt(),Ee(),br()+e,!1)}function Yh(e,t,n,r){if(!r)if(3==(3&t[Dt])){const o=e.preOrderCheckHooks;null!==o&&ul(t,o,n)}else{const o=e.preOrderHooks;null!==o&&cl(t,o,0,n)}Mi(n)}function Xs(e,t=Mt.Default){const n=Ee();return null===n?ue(e,t):cf(wr(),n,we(e),t)}function ep(){throw new Error("invalid")}function Kl(e,t,n,r,i,o,s,d,p,E,A){const x=t.blueprint.slice();return x[rn]=i,x[Dt]=140|r,(null!==E||e&&2048&e[Dt])&&(x[Dt]|=2048),Lo(x),x[qt]=x[Dr]=e,x[Dn]=n,x[Nr]=s||e&&e[Nr],x[mt]=d||e&&e[mt],x[Or]=p||e&&e[Or]||null,x[On]=o,x[Vn]=function iD(){return rD++}(),x[Pr]=A,x[Zr]=E,x[Yt]=2==t.type?e[Yt]:x,x}function Qs(e,t,n,r,i){let o=e.data[t];if(null===o)o=function xc(e,t,n,r,i){const o=sl(),s=Da(),p=e.data[t]=function u_(e,t,n,r,i,o){let s=t?t.injectorIndex:-1,d=0;return function Pn(){return null!==Xe.skipHydrationRootTNode}()&&(d|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:d,providerIndexes:0,value:i,attrs:o,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?o:o&&o.parent,n,t,r,i);return null===e.firstChild&&(e.firstChild=p),null!==o&&(s?null==o.child&&null!==p.parent&&(o.child=p):null===o.next&&(o.next=p,p.prev=o)),p}(e,t,n,r,i),function Jd(){return Xe.lFrame.inI18n}()&&(o.flags|=32);else if(64&o.type){o.type=n,o.value=r,o.attrs=i;const s=function ds(){const e=Xe.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();o.injectorIndex=null===s?-1:s.injectorIndex}return to(o,!0),o}function Ba(e,t,n,r){if(0===n)return-1;const i=t.length;for(let o=0;o<n;o++)t.push(r),e.blueprint.push(r),e.data.push(null);return i}function tp(e,t,n,r,i){const o=Xh(t,Yi),s=br(),d=2&r;try{Mi(-1),d&&t.length>Ot&&Yh(e,t,Ot,!1),at(d?2:0,i);const E=d?o:null,A=Fo(E);try{null!==E&&(E.dirty=!1),n(r,i)}finally{co(E,A)}}finally{d&&null===t[Yi]&&Qh(t,Yi),Mi(s),at(d?3:1,i)}}function Fc(e,t,n){if(eo(t)){const r=_r(null);try{const o=t.directiveEnd;for(let s=t.directiveStart;s<o;s++){const d=e.data[s];d.contentQueries&&d.contentQueries(1,n[s],s)}}finally{_r(r)}}}function kc(e,t,n){Ms()&&(function m_(e,t,n,r){const i=n.directiveStart,o=n.directiveEnd;Cr(n)&&function w_(e,t,n){const r=Gn(t,e),i=np(n);let s=16;n.signals?s=4096:n.onPush&&(s=64);const d=Xl(e,Kl(e,i,null,s,r,t,null,e[Nr].rendererFactory.createRenderer(r,n),null,null,null));e[t.index]=d}(t,n,e.data[i+n.componentOffset]),e.firstCreatePass||fl(n,t),Yr(r,t);const s=n.initialInputs;for(let d=i;d<o;d++){const p=e.data[d],E=gs(t,e,d,n);Yr(E,t),null!==s&&b_(0,d-i,E,p,0,s),pr(p)&&(Nn(n.index,t)[Dn]=gs(t,e,d,n))}}(e,t,n,Gn(n,t)),64==(64&n.flags)&&ap(e,t,n))}function Lc(e,t,n=Gn){const r=t.localNames;if(null!==r){let i=t.index+1;for(let o=0;o<r.length;o+=2){const s=r[o+1],d=-1===s?n(t,e):e[s];e[i++]=d}}}function np(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Vc(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Vc(e,t,n,r,i,o,s,d,p,E,A){const x=Ot+r,z=x+i,K=function r_(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:Pt);return n}(x,z),me="function"==typeof E?E():E;return K[je]={type:e,blueprint:K,template:n,queries:null,viewQuery:d,declTNode:t,data:K.slice().fill(null,x),bindingStartIndex:x,expandoStartIndex:z,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof o?o():o,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:p,consts:me,incompleteFirstPass:!1,ssrId:A}}let rp=e=>null;function ip(e,t,n,r){for(let i in e)if(e.hasOwnProperty(i)){n=null===n?{}:n;const o=e[i];null===r?op(n,t,i,o):r.hasOwnProperty(i)&&op(n,t,r[i],o)}return n}function op(e,t,n,r){e.hasOwnProperty(n)?e[n].push(t,r):e[n]=[t,r]}function jc(e,t,n,r){if(Ms()){const i=null===r?null:{"":-1},o=function v_(e,t){const n=e.directiveRegistry;let r=null,i=null;if(n)for(let o=0;o<n.length;o++){const s=n[o];if(Zi(t,s.selectors,!1))if(r||(r=[]),pr(s))if(null!==s.findHostDirectiveDefs){const d=[];i=i||new Map,s.findHostDirectiveDefs(s,d,i),r.unshift(...d,s),Uc(e,t,d.length)}else r.unshift(s),Uc(e,t,0);else i=i||new Map,s.findHostDirectiveDefs?.(s,r,i),r.push(s)}return null===r?null:[r,i]}(e,n);let s,d;null===o?s=d=null:[s,d]=o,null!==s&&sp(e,t,n,s,i,d),i&&function D_(e,t,n){if(t){const r=e.localNames=[];for(let i=0;i<t.length;i+=2){const o=n[t[i+1]];if(null==o)throw new Pe(-301,!1);r.push(t[i],o)}}}(n,r,i)}n.mergedAttrs=mr(n.mergedAttrs,n.attrs)}function sp(e,t,n,r,i,o){for(let E=0;E<r.length;E++)Ru(fl(n,t),e,r[E].type);!function __(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let E=0;E<r.length;E++){const A=r[E];A.providersResolver&&A.providersResolver(A)}let s=!1,d=!1,p=Ba(e,t,r.length,null);for(let E=0;E<r.length;E++){const A=r[E];n.mergedAttrs=mr(n.mergedAttrs,A.hostAttrs),E_(e,n,t,p,A),C_(p,A,i),null!==A.contentQueries&&(n.flags|=4),(null!==A.hostBindings||null!==A.hostAttrs||0!==A.hostVars)&&(n.flags|=64);const x=A.type.prototype;!s&&(x.ngOnChanges||x.ngOnInit||x.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!d&&(x.ngOnChanges||x.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),d=!0),p++}!function c_(e,t,n){const i=t.directiveEnd,o=e.data,s=t.attrs,d=[];let p=null,E=null;for(let A=t.directiveStart;A<i;A++){const x=o[A],z=n?n.get(x):null,me=z?z.outputs:null;p=ip(x.inputs,A,p,z?z.inputs:null),E=ip(x.outputs,A,E,me);const Se=null===p||null===s||yr(t)?null:M_(p,A,s);d.push(Se)}null!==p&&(p.hasOwnProperty("class")&&(t.flags|=8),p.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=d,t.inputs=p,t.outputs=E}(e,n,o)}function ap(e,t,n){const r=n.directiveStart,i=n.directiveEnd,o=n.index,s=function g(){return Xe.lFrame.currentDirectiveIndex}();try{Mi(o);for(let d=r;d<i;d++){const p=e.data[d],E=t[d];u(d),(null!==p.hostBindings||0!==p.hostVars||null!==p.hostAttrs)&&y_(p,E)}}finally{Mi(-1),u(s)}}function y_(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function Uc(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function C_(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;pr(t)&&(n[""]=e)}}function E_(e,t,n,r,i){e.data[r]=i;const o=i.factory||(i.factory=Bi(i.type)),s=new Ea(o,pr(i),Xs);e.blueprint[r]=s,n[r]=s,function p_(e,t,n,r,i){const o=i.hostBindings;if(o){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const d=~t.index;(function g_(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=d&&s.push(d),s.push(n,r,o)}}(e,t,r,Ba(e,n,i.hostVars,Pt),i)}function So(e,t,n,r,i,o){const s=Gn(e,t);!function Bc(e,t,n,r,i,o,s){if(null==o)e.removeAttribute(t,i,n);else{const d=null==s?et(o):s(o,r||"",i);e.setAttribute(t,i,d,n)}}(t[mt],s,o,e.value,n,r,i)}function b_(e,t,n,r,i,o){const s=o[t];if(null!==s)for(let d=0;d<s.length;)lp(r,n,s[d++],s[d++],s[d++])}function lp(e,t,n,r,i){const o=_r(null);try{const s=e.inputTransforms;null!==s&&s.hasOwnProperty(r)&&(i=s[r].call(t,i)),null!==e.setInput?e.setInput(t,i,n,r):t[r]=i}finally{_r(o)}}function M_(e,t,n){let r=null,i=0;for(;i<n.length;){const o=n[i];if(0!==o)if(5!==o){if("number"==typeof o)break;if(e.hasOwnProperty(o)){null===r&&(r=[]);const s=e[o];for(let d=0;d<s.length;d+=2)if(s[d]===t){r.push(o,s[d+1],n[i+1]);break}}i+=2}else i+=2;else i+=4}return r}function up(e,t,n,r){return[e,!0,!1,t,null,0,r,n,null,null,null]}function cp(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const o=n[r+1];if(-1!==o){const s=e.data[o];N(n[r]),s.contentQueries(2,t[o],o)}}}function Xl(e,t){return e[vr]?e[lo][Zn]=t:e[vr]=t,e[lo]=t,t}function $c(e,t,n){N(0);const r=_r(null);try{t(e,n)}finally{_r(r)}}function dp(e){return e[Br]||(e[Br]=[])}function fp(e){return e.cleanup||(e.cleanup=[])}function pp(e,t){const n=e[Or],r=n?n.get(Zo,null):null;r&&r.handleError(t)}function Hc(e,t,n,r,i){for(let o=0;o<n.length;){const s=n[o++],d=n[o++];lp(e.data[s],t[s],r,d,i)}}function Uo(e,t,n){const r=Gt(t,e);!function Bf(e,t,n){e.setValue(t,n)}(e[mt],r,n)}function S_(e,t){const n=Nn(t,e),r=n[je];!function I_(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n);const i=n[rn];null!==i&&null===n[Pr]&&(n[Pr]=bc(i,n[Or])),zc(r,n,n[Dn])}function zc(e,t,n){Y(t);try{const r=e.viewQuery;null!==r&&$c(1,r,n);const i=e.template;null!==i&&tp(e,t,i,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&cp(e,t),e.staticViewQueries&&$c(2,e.viewQuery,n);const o=e.components;null!==o&&function T_(e,t){for(let n=0;n<t.length;n++)S_(e,t[n])}(t,o)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[Dt]&=-5,zi()}}let gp=(()=>{class e{constructor(){this.all=new Set,this.queue=new Map}create(n,r,i){const o=typeof Zone>"u"?null:Zone.current,s=function a(e,t,n){const r=Object.create(y);n&&(r.consumerAllowSignalWrites=!0),r.fn=e,r.schedule=t;const i=s=>{r.cleanupFn=s};return r.ref={notify:()=>uo(r),run:()=>{if(r.dirty=!1,r.hasRun&&!Go(r))return;r.hasRun=!0;const s=Fo(r);try{r.cleanupFn(),r.cleanupFn=l,r.fn(i)}finally{co(r,s)}},cleanup:()=>r.cleanupFn()},r.ref}(n,E=>{this.all.has(E)&&this.queue.set(E,o)},i);let d;this.all.add(s),s.notify();const p=()=>{s.cleanup(),d?.(),this.all.delete(s),this.queue.delete(s)};return d=r?.onDestroy(p),{destroy:p}}flush(){if(0!==this.queue.size)for(const[n,r]of this.queue)this.queue.delete(n),r?r.run(()=>n.run()):n.run()}get isQueueEmpty(){return 0===this.queue.size}static{this.\u0275prov=Re({token:e,providedIn:"root",factory:()=>new e})}}return e})();function Ql(e,t,n){let r=n?e.styles:null,i=n?e.classes:null,o=0;if(null!==t)for(let s=0;s<t.length;s++){const d=t[s];"number"==typeof d?o=d:1==o?i=xe(i,d):2==o&&(r=xe(r,d+": "+t[++s]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=i:e.classesWithoutHost=i}function $a(e,t,n,r,i=!1){for(;null!==n;){const o=t[n.index];null!==o&&r.push(St(o)),or(o)&&mp(o,r);const s=n.type;if(8&s)$a(e,t,n.child,r);else if(32&s){const d=Wu(n,t);let p;for(;p=d();)r.push(p)}else if(16&s){const d=qf(t,n);if(Array.isArray(d))r.push(...d);else{const p=Oa(t[Yt]);$a(p[je],p,d,r,!0)}}n=i?n.projectionNext:n.next}return r}function mp(e,t){for(let n=Lt;n<e.length;n++){const r=e[n],i=r[je].firstChild;null!==i&&$a(r[je],r,i,t)}e[Rr]!==e[rn]&&t.push(e[Rr])}function ql(e,t,n,r=!0){const i=t[Nr],o=i.rendererFactory,s=i.afterRenderEventManager;o.begin?.(),s?.begin();try{yp(e,t,e.template,n)}catch(p){throw r&&pp(t,p),p}finally{o.end?.(),i.effectManager?.flush(),s?.end()}}function yp(e,t,n,r){const i=t[Dt];if(256!=(256&i)){t[Nr].effectManager?.flush(),Y(t);try{Lo(t),function Su(e){return Xe.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&tp(e,t,n,2,r);const s=3==(3&i);if(s){const E=e.preOrderCheckHooks;null!==E&&ul(t,E,null)}else{const E=e.preOrderHooks;null!==E&&cl(t,E,0,null),Tu(t,0)}if(function N_(e){for(let t=Vf(e);null!==t;t=jf(t)){if(!t[si])continue;const n=t[zn];for(let r=0;r<n.length;r++){cs(n[r])}}}(t),vp(t,2),null!==e.contentQueries&&cp(e,t),s){const E=e.contentCheckHooks;null!==E&&ul(t,E)}else{const E=e.contentHooks;null!==E&&cl(t,E,1),Tu(t,1)}!function n_(e,t){const n=e.hostBindingOpCodes;if(null===n)return;const r=Xh(t,xi);try{for(let i=0;i<n.length;i++){const o=n[i];if(o<0)Mi(~o);else{const s=o,d=n[++i],p=n[++i];f(d,s),r.dirty=!1;const E=Fo(r);try{p(2,t[s])}finally{co(r,E)}}}}finally{null===t[xi]&&Qh(t,xi),Mi(-1)}}(e,t);const d=e.components;null!==d&&Cp(t,d,0);const p=e.viewQuery;if(null!==p&&$c(2,p,r),s){const E=e.viewCheckHooks;null!==E&&ul(t,E)}else{const E=e.viewHooks;null!==E&&cl(t,E,2),Tu(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[Dt]&=-73,Ye(t)}finally{zi()}}}function vp(e,t){for(let n=Vf(e);null!==n;n=jf(n))for(let r=Lt;r<n.length;r++)Dp(n[r],t)}function P_(e,t,n){Dp(Nn(t,e),n)}function Dp(e,t){if(!function ya(e){return 128==(128&e[Dt])}(e))return;const n=e[je],r=e[Dt];if(80&r&&0===t||1024&r||2===t)yp(n,e,n.template,e[Dn]);else if(e[Qr]>0){vp(e,1);const i=n.components;null!==i&&Cp(e,i,1)}}function Cp(e,t,n){for(let r=0;r<t.length;r++)P_(e,t[r],n)}class Ha{get rootNodes(){const t=this._lView,n=t[je];return $a(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[Dn]}set context(t){this._lView[Dn]=t}get destroyed(){return 256==(256&this._lView[Dt])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[qt];if(or(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(Tl(t,r),gl(n,r))}this._attachedToViewContainer=!1}Xu(this._lView[je],this._lView)}onDestroy(t){!function it(e,t){if(256==(256&e[Dt]))throw new Pe(911,!1);null===e[Jn]&&(e[Jn]=[]),e[Jn].push(t)}(this._lView,t)}markForCheck(){Ua(this._cdRefInjectingView||this._lView)}detach(){this._lView[Dt]&=-129}reattach(){this._lView[Dt]|=128}detectChanges(){ql(this._lView[je],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new Pe(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function vD(e,t){Pa(e,t,t[mt],2,null,null)}(this._lView[je],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new Pe(902,!1);this._appRef=t}}class R_ extends Ha{constructor(t){super(t),this._view=t}detectChanges(){const t=this._view;ql(t[je],t,t[Dn],!1)}checkNoChanges(){}get context(){return null}}class _p extends Hl{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=Nt(t);return new za(n,this.ngModule)}}function Ep(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class F_{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Le(r);const i=this.injector.get(t,Ic,r);return i!==Ic||n===Ic?i:this.parentInjector.get(t,n,r)}}class za extends Mh{get inputs(){const t=this.componentDef,n=t.inputTransforms,r=Ep(t.inputs);if(null!==n)for(const i of r)n.hasOwnProperty(i.propName)&&(i.transform=n[i.propName]);return r}get outputs(){return Ep(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function oo(e){return e.map(mi).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,i){let o=(i=i||this.ngModule)instanceof bo?i:i?.injector;o&&null!==this.componentDef.getStandaloneInjector&&(o=this.componentDef.getStandaloneInjector(o)||o);const s=o?new F_(t,o):t,d=s.get(Ih,null);if(null===d)throw new Pe(407,!1);const x={rendererFactory:d,sanitizer:s.get(OC,null),effectManager:s.get(gp,null),afterRenderEventManager:s.get(Rc,null)},z=d.createRenderer(null,this.componentDef),K=this.componentDef.selectors[0][0]||"div",me=r?function i_(e,t,n,r){const o=r.get(zh,!1)||n===wn.ShadowDom,s=e.selectRootElement(t,o);return function o_(e){rp(e)}(s),s}(z,r,this.componentDef.encapsulation,s):Il(z,K,function x_(e){const t=e.toLowerCase();return"svg"===t?"svg":"math"===t?"math":null}(K)),nt=this.componentDef.signals?4608:this.componentDef.onPush?576:528;let Ce=null;null!==me&&(Ce=bc(me,s,!0));const It=Vc(0,null,null,1,0,null,null,null,null,null,null),kt=Kl(null,It,null,nt,null,null,x,z,s,null,Ce);let yn,Ai;Y(kt);try{const Bo=this.componentDef;let fa,Qd=null;Bo.findHostDirectiveDefs?(fa=[],Qd=new Map,Bo.findHostDirectiveDefs(Bo,fa,Qd),fa.push(Bo)):fa=[Bo];const UM=function L_(e,t){const n=e[je],r=Ot;return e[r]=t,Qs(n,r,2,"#host",null)}(kt,me),BM=function V_(e,t,n,r,i,o,s){const d=i[je];!function j_(e,t,n,r){for(const i of e)t.mergedAttrs=mr(t.mergedAttrs,i.hostAttrs);null!==t.mergedAttrs&&(Ql(t,t.mergedAttrs,!0),null!==n&&eh(r,n,t))}(r,e,t,s);let p=null;null!==t&&(p=bc(t,i[Or]));const E=o.rendererFactory.createRenderer(t,n);let A=16;n.signals?A=4096:n.onPush&&(A=64);const x=Kl(i,np(n),null,A,i[e.index],e,o,E,null,null,p);return d.firstCreatePass&&Uc(d,e,r.length-1),Xl(i,x),i[e.index]=x}(UM,me,Bo,fa,kt,x,z);Ai=Wn(It,Ot),me&&function B_(e,t,n,r){if(r)ri(e,n,["ng-version",NC.full]);else{const{attrs:i,classes:o}=function Po(e){const t=[],n=[];let r=1,i=2;for(;r<e.length;){let o=e[r];if("string"==typeof o)2===i?""!==o&&t.push(o,e[++r]):8===i&&n.push(o);else{if(!ir(i))break;i=o}r++}return{attrs:t,classes:n}}(t.selectors[0]);i&&ri(e,n,i),o&&o.length>0&&Yf(e,n,o.join(" "))}}(z,Bo,me,r),void 0!==n&&function $_(e,t,n){const r=e.projection=[];for(let i=0;i<t.length;i++){const o=n[i];r.push(null!=o?Array.from(o):null)}}(Ai,this.ngContentSelectors,n),yn=function U_(e,t,n,r,i,o){const s=wr(),d=i[je],p=Gn(s,i);sp(d,i,s,n,null,r);for(let A=0;A<n.length;A++)Yr(gs(i,d,s.directiveStart+A,s),i);ap(d,i,s),p&&Yr(p,i);const E=gs(i,d,s.directiveStart+s.componentOffset,s);if(e[Dn]=i[Dn]=E,null!==o)for(const A of o)A(E,t);return Fc(d,s,e),E}(BM,Bo,fa,Qd,kt,[H_]),zc(It,kt,null)}finally{zi()}return new k_(this.componentType,yn,Gs(Ai,kt),kt,Ai)}}class k_ extends wC{constructor(t,n,r,i,o){super(),this.location=r,this._rootLView=i,this._tNode=o,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new R_(i),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let i;if(null!==r&&(i=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const o=this._rootLView;Hc(o[je],o,i,t,n),this.previousInputValues.set(t,n),Ua(Nn(this._tNode.index,o))}}get injector(){return new di(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function H_(){const e=wr();ll(Ee()[je],e)}function Gc(e){let t=function wp(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const r=[e];for(;t;){let i;if(pr(e))i=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new Pe(903,!1);i=t.\u0275dir}if(i){if(n){r.push(i);const s=e;s.inputs=Zl(e.inputs),s.inputTransforms=Zl(e.inputTransforms),s.declaredInputs=Zl(e.declaredInputs),s.outputs=Zl(e.outputs);const d=i.hostBindings;d&&K_(e,d);const p=i.viewQuery,E=i.contentQueries;if(p&&G_(e,p),E&&W_(e,E),Rt(e.inputs,i.inputs),Rt(e.declaredInputs,i.declaredInputs),Rt(e.outputs,i.outputs),null!==i.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),Rt(s.inputTransforms,i.inputTransforms)),pr(i)&&i.data.animation){const A=e.data;A.animation=(A.animation||[]).concat(i.data.animation)}}const o=i.features;if(o)for(let s=0;s<o.length;s++){const d=o[s];d&&d.ngInherit&&d(e),d===Gc&&(n=!1)}}t=Object.getPrototypeOf(t)}!function z_(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const i=e[r];i.hostVars=t+=i.hostVars,i.hostAttrs=mr(i.hostAttrs,n=mr(n,i.hostAttrs))}}(r)}function Zl(e){return e===ct?{}:e===Je?[]:e}function G_(e,t){const n=e.viewQuery;e.viewQuery=n?(r,i)=>{t(r,i),n(r,i)}:t}function W_(e,t){const n=e.contentQueries;e.contentQueries=n?(r,i,o)=>{t(r,i,o),n(r,i,o)}:t}function K_(e,t){const n=e.hostBindings;e.hostBindings=n?(r,i)=>{t(r,i),n(r,i)}:t}function Ip(e){const t=e.inputConfig,n={};for(const r in t)if(t.hasOwnProperty(r)){const i=t[r];Array.isArray(i)&&i[2]&&(n[r]=i[2])}e.inputTransforms=n}function Jl(e){return!!Wc(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function Wc(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function Io(e,t,n){return e[t]=n}function ei(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function vs(e,t,n,r){const i=ei(e,t,n);return ei(e,t+1,r)||i}function Kc(e,t,n,r){const i=Ee();return ei(i,fs(),t)&&(Wt(),So(Rn(),i,e,t,n,r)),Kc}function Zs(e,t,n,r){return ei(e,fs(),n)?t+et(n)+r:Pt}function Js(e,t,n,r,i,o){const d=vs(e,function _o(){return Xe.lFrame.bindingIndex}(),n,i);return Eo(2),d?t+et(n)+r+et(i)+o:Pt}function Up(e,t,n,r,i,o,s,d){const p=Ee(),E=Wt(),A=e+Ot,x=E.firstCreatePass?function vE(e,t,n,r,i,o,s,d,p){const E=t.consts,A=Qs(t,e,4,s||null,xr(E,d));jc(t,n,A,xr(E,p)),ll(t,A);const x=A.tView=Vc(2,A,r,i,o,t.directiveRegistry,t.pipeRegistry,null,t.schemas,E,null);return null!==t.queries&&(t.queries.template(t,A),x.queries=t.queries.embeddedTView(A)),A}(A,E,p,t,n,r,i,o,s):E.data[A];to(x,!1);const z=Bp(E,p,x,e);al()&&Ol(E,p,z,x),Yr(z,p),Xl(p,p[A]=up(z,p,z,x)),Li(x)&&kc(E,p,x),null!=s&&Lc(p,x,d)}let Bp=function $p(e,t,n,r){return Qo(!0),t[mt].createComment("")};function Hp(e){return Er(function Mu(){return Xe.lFrame.contextLView}(),Ot+e)}function Yc(e,t,n){const r=Ee();return ei(r,fs(),t)&&function Wi(e,t,n,r,i,o,s,d){const p=Gn(t,n);let A,E=t.inputs;!d&&null!=E&&(A=E[r])?(Hc(e,n,A,r,i),Cr(t)&&function f_(e,t){const n=Nn(t,e);16&n[Dt]||(n[Dt]|=64)}(n,t.index)):3&t.type&&(r=function d_(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),i=null!=s?s(i,t.value||"",r):i,o.setProperty(p,r,i))}(Wt(),Rn(),r,e,t,r[mt],n,!1),Yc}function ed(e,t,n,r,i){const s=i?"class":"style";Hc(e,n,t.inputs[s],s,r)}function ru(e,t,n,r){const i=Ee(),o=Wt(),s=Ot+e,d=i[mt],p=o.firstCreatePass?function EE(e,t,n,r,i,o){const s=t.consts,p=Qs(t,e,2,r,xr(s,i));return jc(t,n,p,xr(s,o)),null!==p.attrs&&Ql(p,p.attrs,!1),null!==p.mergedAttrs&&Ql(p,p.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,p),p}(s,o,i,t,n,r):o.data[s],E=zp(o,i,p,d,t,e);i[s]=E;const A=Li(p);return to(p,!0),eh(d,E,p),32!=(32&p.flags)&&al()&&Ol(o,i,E,p),0===function Hi(){return Xe.lFrame.elementDepthCount}()&&Yr(E,i),function Cn(){Xe.lFrame.elementDepthCount++}(),A&&(kc(o,i,p),Fc(o,p,i)),null!==r&&Lc(i,p),ru}function iu(){let e=wr();Da()?Ca():(e=e.parent,to(e,!1));const t=e;(function Co(e){return Xe.skipHydrationRootTNode===e})(t)&&function qd(){Xe.skipHydrationRootTNode=null}(),function Ko(){Xe.lFrame.elementDepthCount--}();const n=Wt();return n.firstCreatePass&&(ll(n,e),eo(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function mv(e){return 0!=(8&e.flags)}(t)&&ed(n,t,Ee(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function yv(e){return 0!=(16&e.flags)}(t)&&ed(n,t,Ee(),t.stylesWithoutHost,!1),iu}function td(e,t,n,r){return ru(e,t,n,r),iu(),td}let zp=(e,t,n,r,i,o)=>(Qo(!0),Il(r,i,function Yd(){return Xe.lFrame.currentNamespace}()));function ou(e,t,n){const r=Ee(),i=Wt(),o=e+Ot,s=i.firstCreatePass?function ME(e,t,n,r,i){const o=t.consts,s=xr(o,r),d=Qs(t,e,8,"ng-container",s);return null!==s&&Ql(d,s,!0),jc(t,n,d,xr(o,i)),null!==t.queries&&t.queries.elementStart(t,d),d}(o,i,r,t,n):i.data[o];to(s,!0);const d=Wp(i,r,s,e);return r[o]=d,al()&&Ol(i,r,d,s),Yr(d,r),Li(s)&&(kc(i,r,s),Fc(i,s,r)),null!=n&&Lc(r,s),ou}function su(){let e=wr();const t=Wt();return Da()?Ca():(e=e.parent,to(e,!1)),t.firstCreatePass&&(ll(t,e),eo(e)&&t.queries.elementEnd(e)),su}let Wp=(e,t,n,r)=>(Qo(!0),Ku(t[mt],""));function Kp(){return Ee()}function nd(e){return!!e&&"function"==typeof e.then}function Xp(e){return!!e&&"function"==typeof e.subscribe}function rd(e,t,n,r){const i=Ee(),o=Wt(),s=wr();return function qp(e,t,n,r,i,o,s){const d=Li(r),E=e.firstCreatePass&&fp(e),A=t[Dn],x=dp(t);let z=!0;if(3&r.type||s){const Se=Gn(r,t),$e=s?s(Se):Se,nt=x.length,Ce=s?kt=>s(St(kt[r.index])):r.index;let It=null;if(!s&&d&&(It=function TE(e,t,n,r){const i=e.cleanup;if(null!=i)for(let o=0;o<i.length-1;o+=2){const s=i[o];if(s===n&&i[o+1]===r){const d=t[Br],p=i[o+2];return d.length>p?d[p]:null}"string"==typeof s&&(o+=2)}return null}(e,t,i,r.index)),null!==It)(It.__ngLastListenerFn__||It).__ngNextListenerFn__=o,It.__ngLastListenerFn__=o,z=!1;else{o=Jp(r,t,A,o,!1);const kt=n.listen($e,i,o);x.push(o,kt),E&&E.push(i,Ce,nt,nt+1)}}else o=Jp(r,t,A,o,!1);const K=r.outputs;let me;if(z&&null!==K&&(me=K[i])){const Se=me.length;if(Se)for(let $e=0;$e<Se;$e+=2){const yn=t[me[$e]][me[$e+1]].subscribe(o),Ai=x.length;x.push(o,yn),E&&E.push(i,r.index,Ai,-(Ai+1))}}}(o,i,i[mt],s,e,t,r),rd}function Zp(e,t,n,r){try{return at(6,t,n),!1!==n(r)}catch(i){return pp(e,i),!1}finally{at(7,t,n)}}function Jp(e,t,n,r,i){return function o(s){if(s===Function)return r;Ua(e.componentOffset>-1?Nn(e.index,t):t);let p=Zp(t,n,r,s),E=o.__ngNextListenerFn__;for(;E;)p=Zp(t,n,E,s)&&p,E=E.__ngNextListenerFn__;return i&&!1===p&&s.preventDefault(),p}}function Yp(e=1){return function hs(e){return(Xe.lFrame.contextLView=function ps(e,t){for(;e>0;)t=t[Dr],e--;return t}(e,Xe.lFrame.contextLView))[Dn]}(e)}function au(e,t){return e<<17|t<<2}function Jo(e){return e>>17&32767}function od(e){return 2|e}function Ds(e){return(131068&e)>>2}function sd(e,t){return-131069&e|t<<2}function ad(e){return 1|e}function ug(e,t,n,r,i){const o=e[n+1],s=null===t;let d=r?Jo(o):Ds(o),p=!1;for(;0!==d&&(!1===p||s);){const A=e[d+1];LE(e[d],t)&&(p=!0,e[d+1]=r?ad(A):od(A)),d=r?Jo(A):Ds(A)}p&&(e[n+1]=r?od(o):ad(o))}function LE(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&xs(e,t)>=0}const Sr={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function cg(e){return e.substring(Sr.key,Sr.keyEnd)}function dg(e,t){const n=Sr.textEnd;return n===t?-1:(t=Sr.keyEnd=function BE(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}(e,Sr.key=t,n),oa(e,t,n))}function oa(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function ld(e,t,n){return ho(e,t,n,!1),ld}function ud(e,t){return ho(e,t,null,!0),ud}function mg(e){po(qE,Ao,e,!0)}function Ao(e,t){for(let n=function jE(e){return function hg(e){Sr.key=0,Sr.keyEnd=0,Sr.value=0,Sr.valueEnd=0,Sr.textEnd=e.length}(e),dg(e,oa(e,0,Sr.textEnd))}(t);n>=0;n=dg(t,n))Gi(e,cg(t),!0)}function ho(e,t,n,r){const i=Ee(),o=Wt(),s=Eo(2);o.firstUpdatePass&&vg(o,e,s,r),t!==Pt&&ei(i,s,t)&&Cg(o,o.data[br()],i,i[mt],e,i[s+1]=function JE(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=yt(qo(e)))),e}(t,n),r,s)}function po(e,t,n,r){const i=Wt(),o=Eo(2);i.firstUpdatePass&&vg(i,null,o,r);const s=Ee();if(n!==Pt&&ei(s,o,n)){const d=i.data[br()];if(Eg(d,r)&&!yg(i,o)){let p=r?d.classesWithoutHost:d.stylesWithoutHost;null!==p&&(n=xe(p,n||"")),ed(i,d,s,n,r)}else!function ZE(e,t,n,r,i,o,s,d){i===Pt&&(i=Je);let p=0,E=0,A=0<i.length?i[0]:null,x=0<o.length?o[0]:null;for(;null!==A||null!==x;){const z=p<i.length?i[p+1]:void 0,K=E<o.length?o[E+1]:void 0;let Se,me=null;A===x?(p+=2,E+=2,z!==K&&(me=x,Se=K)):null===x||null!==A&&A<x?(p+=2,me=A):(E+=2,me=x,Se=K),null!==me&&Cg(e,t,n,r,me,Se,s,d),A=p<i.length?i[p]:null,x=E<o.length?o[E]:null}}(i,d,s,s[mt],s[o+1],s[o+1]=function QE(e,t,n){if(null==n||""===n)return Je;const r=[],i=qo(n);if(Array.isArray(i))for(let o=0;o<i.length;o++)e(r,i[o],!0);else if("object"==typeof i)for(const o in i)i.hasOwnProperty(o)&&e(r,o,i[o]);else"string"==typeof i&&t(r,i);return r}(e,t,n),r,o)}}function yg(e,t){return t>=e.expandoStartIndex}function vg(e,t,n,r){const i=e.data;if(null===i[n+1]){const o=i[br()],s=yg(e,n);Eg(o,r)&&null===t&&!s&&(t=!1),t=function GE(e,t,n,r){const i=function m(e){const t=Xe.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let o=r?t.residualClasses:t.residualStyles;if(null===i)0===(r?t.classBindings:t.styleBindings)&&(n=Qa(n=cd(null,e,t,n,r),t.attrs,r),o=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==i)if(n=cd(i,e,t,n,r),null===o){let p=function WE(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==Ds(r))return e[Jo(r)]}(e,t,r);void 0!==p&&Array.isArray(p)&&(p=cd(null,e,t,p[1],r),p=Qa(p,t.attrs,r),function KE(e,t,n,r){e[Jo(n?t.classBindings:t.styleBindings)]=r}(e,t,r,p))}else o=function XE(e,t,n){let r;const i=t.directiveEnd;for(let o=1+t.directiveStylingLast;o<i;o++)r=Qa(r,e[o].hostAttrs,n);return Qa(r,t.attrs,n)}(e,t,r)}return void 0!==o&&(r?t.residualClasses=o:t.residualStyles=o),n}(i,o,t,r),function FE(e,t,n,r,i,o){let s=o?t.classBindings:t.styleBindings,d=Jo(s),p=Ds(s);e[r]=n;let A,E=!1;if(Array.isArray(n)?(A=n[1],(null===A||xs(n,A)>0)&&(E=!0)):A=n,i)if(0!==p){const z=Jo(e[d+1]);e[r+1]=au(z,d),0!==z&&(e[z+1]=sd(e[z+1],r)),e[d+1]=function RE(e,t){return 131071&e|t<<17}(e[d+1],r)}else e[r+1]=au(d,0),0!==d&&(e[d+1]=sd(e[d+1],r)),d=r;else e[r+1]=au(p,0),0===d?d=r:e[p+1]=sd(e[p+1],r),p=r;E&&(e[r+1]=od(e[r+1])),ug(e,A,r,!0),ug(e,A,r,!1),function kE(e,t,n,r,i){const o=i?e.residualClasses:e.residualStyles;null!=o&&"string"==typeof t&&xs(o,t)>=0&&(n[r+1]=ad(n[r+1]))}(t,A,e,r,o),s=au(d,p),o?t.classBindings=s:t.styleBindings=s}(i,o,t,n,s,r)}}function cd(e,t,n,r,i){let o=null;const s=n.directiveEnd;let d=n.directiveStylingLast;for(-1===d?d=n.directiveStart:d++;d<s&&(o=t[d],r=Qa(r,o.hostAttrs,i),o!==e);)d++;return null!==e&&(n.directiveStylingLast=d),r}function Qa(e,t,n){const r=n?1:2;let i=-1;if(null!==t)for(let o=0;o<t.length;o++){const s=t[o];"number"==typeof s?i=s:i===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),Gi(e,s,!!n||t[++o]))}return void 0===e?null:e}function qE(e,t,n){const r=String(t);""!==r&&!r.includes(" ")&&Gi(e,r,n)}function Cg(e,t,n,r,i,o,s,d){if(!(3&t.type))return;const p=e.data,E=p[d+1],A=function xE(e){return 1==(1&e)}(E)?_g(p,t,n,i,Ds(E),s):void 0;lu(A)||(lu(o)||function PE(e){return 2==(2&e)}(E)&&(o=_g(p,null,n,i,d,s)),function TD(e,t,n,r,i){if(t)i?e.addClass(n,r):e.removeClass(n,r);else{let o=-1===r.indexOf("-")?void 0:Ml.DashCase;null==i?e.removeStyle(n,r,o):("string"==typeof i&&i.endsWith("!important")&&(i=i.slice(0,-10),o|=Ml.Important),e.setStyle(n,r,i,o))}}(r,s,Gt(br(),n),i,o))}function _g(e,t,n,r,i,o){const s=null===t;let d;for(;i>0;){const p=e[i],E=Array.isArray(p),A=E?p[1]:p,x=null===A;let z=n[i+1];z===Pt&&(z=x?Je:void 0);let K=x?Lu(z,r):A===r?z:void 0;if(E&&!lu(K)&&(K=Lu(p,r)),lu(K)&&(d=K,s))return d;const me=e[i+1];i=s?Jo(me):Ds(me)}if(null!==t){let p=o?t.residualClasses:t.residualStyles;null!=p&&(d=Lu(p,r))}return d}function lu(e){return void 0!==e}function Eg(e,t){return 0!=(e.flags&(t?8:16))}function wg(e,t=""){const n=Ee(),r=Wt(),i=e+Ot,o=r.firstCreatePass?Qs(r,i,1,t,null):r.data[i],s=bg(r,n,o,t,e);n[i]=s,al()&&Ol(r,n,s,o),to(o,!1)}let bg=(e,t,n,r,i)=>(Qo(!0),function Sl(e,t){return e.createText(t)}(t[mt],r));function dd(e){return uu("",e,""),dd}function uu(e,t,n){const r=Ee(),i=Zs(r,e,t,n);return i!==Pt&&Uo(r,br(),i),uu}function fd(e,t,n,r,i){const o=Ee(),s=Js(o,e,t,n,r,i);return s!==Pt&&Uo(o,br(),s),fd}function Pg(e,t,n){po(Gi,Ao,Zs(Ee(),e,t,n),!0)}function Rg(e,t,n,r,i){po(Gi,Ao,Js(Ee(),e,t,n,r,i),!0)}const Cs=void 0;var vw=["en",[["a","p"],["AM","PM"],Cs],[["AM","PM"],Cs,Cs],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Cs,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Cs,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Cs,"{1} 'at' {0}",Cs],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function yw(e){const n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===n&&0===r?1:5}];let sa={};function hd(e){const t=function Dw(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=Wg(t);if(n)return n;const r=t.split("-")[0];if(n=Wg(r),n)return n;if("en"===r)return vw;throw new Pe(701,!1)}function Gg(e){return hd(e)[aa.PluralCase]}function Wg(e){return e in sa||(sa[e]=nn.ng&&nn.ng.common&&nn.ng.common.locales&&nn.ng.common.locales[e]),sa[e]}var aa=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(aa||{});const la="en-US";let Kg=la;function md(e,t,n,r,i){if(e=we(e),Array.isArray(e))for(let o=0;o<e.length;o++)md(e[o],t,n,r,i);else{const o=Wt(),s=Ee(),d=wr();let p=ys(e)?e:we(e.provide);const E=Dh(e),A=1048575&d.providerIndexes,x=d.directiveStart,z=d.providerIndexes>>20;if(ys(e)||!e.multi){const K=new Ea(E,i,Xs),me=vd(p,t,i?A:A+z,x);-1===me?(Ru(fl(d,s),o,p),yd(o,e,t.length),t.push(p),d.directiveStart++,d.directiveEnd++,i&&(d.providerIndexes+=1048576),n.push(K),s.push(K)):(n[me]=K,s[me]=K)}else{const K=vd(p,t,A+z,x),me=vd(p,t,A,A+z),$e=me>=0&&n[me];if(i&&!$e||!i&&!(K>=0&&n[K])){Ru(fl(d,s),o,p);const nt=function v0(e,t,n,r,i){const o=new Ea(e,n,Xs);return o.multi=[],o.index=t,o.componentProviders=0,vm(o,i,r&&!n),o}(i?y0:m0,n.length,i,r,E);!i&&$e&&(n[me].providerFactory=nt),yd(o,e,t.length,0),t.push(p),d.directiveStart++,d.directiveEnd++,i&&(d.providerIndexes+=1048576),n.push(nt),s.push(nt)}else yd(o,e,K>-1?K:me,vm(n[i?me:K],E,!i&&r));!i&&r&&$e&&n[me].componentProviders++}}}function yd(e,t,n,r){const i=ys(t),o=function iC(e){return!!e.useClass}(t);if(i||o){const p=(o?we(t.useClass):t).prototype.ngOnDestroy;if(p){const E=e.destroyHooks||(e.destroyHooks=[]);if(!i&&t.multi){const A=E.indexOf(n);-1===A?E.push(n,[r,p]):E[A+1].push(r,p)}else E.push(n,p)}}}function vm(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function vd(e,t,n,r){for(let i=n;i<r;i++)if(t[i]===e)return i;return-1}function m0(e,t,n,r){return Dd(this.multi,[])}function y0(e,t,n,r){const i=this.multi;let o;if(this.providerFactory){const s=this.providerFactory.componentProviders,d=gs(n,n[je],this.providerFactory.index,r);o=d.slice(0,s),Dd(i,o);for(let p=s;p<d.length;p++)o.push(d[p])}else o=[],Dd(i,o);return o}function Dd(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function Dm(e,t=[]){return n=>{n.providersResolver=(r,i)=>function g0(e,t,n){const r=Wt();if(r.firstCreatePass){const i=pr(e);md(n,r.data,r.blueprint,i,!0),md(t,r.data,r.blueprint,i,!1)}}(r,i?i(e):e,t)}}class _s{}class Cm{}function D0(e,t){return new Cd(e,t??null,[])}class Cd extends _s{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new _p(this);const i=An(t);this._bootstrapComponents=jo(i.bootstrap),this._r3Injector=Rh(t,n,[{provide:_s,useValue:this},{provide:Hl,useValue:this.componentFactoryResolver},...r],yt(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class _d extends Cm{constructor(t){super(),this.moduleType=t}create(t){return new Cd(this.moduleType,t,[])}}class _m extends _s{constructor(t){super(),this.componentFactoryResolver=new _p(this),this.instance=null;const n=new Bs([...t.providers,{provide:_s,useValue:this},{provide:Hl,useValue:this.componentFactoryResolver}],t.parent||Ll(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}function Em(e,t,n=null){return new _m({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}let _0=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){const r=gh(0,n.type),i=r.length>0?Em([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,i)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=Re({token:e,providedIn:"environment",factory:()=>new e(ue(bo))})}}return e})();function wm(e){e.getStandaloneInjector=t=>t.get(_0).getOrCreateStandaloneInjector(e)}function Om(e,t,n){const r=Hr()+e,i=Ee();return i[r]===Pt?Io(i,r,n?t.call(n):t()):function Ga(e,t){return e[t]}(i,r)}function Nm(e,t,n,r){return Fm(Ee(),Hr(),e,t,n,r)}function Pm(e,t,n,r,i){return km(Ee(),Hr(),e,t,n,r,i)}function Rm(e,t,n,r,i,o){return Lm(Ee(),Hr(),e,t,n,r,i,o)}function xm(e,t,n,r,i,o,s){return function Vm(e,t,n,r,i,o,s,d,p){const E=t+n;return function ro(e,t,n,r,i,o){const s=vs(e,t,n,r);return vs(e,t+2,i,o)||s}(e,E,i,o,s,d)?Io(e,E+4,p?r.call(p,i,o,s,d):r(i,o,s,d)):tl(e,E+4)}(Ee(),Hr(),e,t,n,r,i,o,s)}function tl(e,t){const n=e[t];return n===Pt?void 0:n}function Fm(e,t,n,r,i,o){const s=t+n;return ei(e,s,i)?Io(e,s+1,o?r.call(o,i):r(i)):tl(e,s+1)}function km(e,t,n,r,i,o,s){const d=t+n;return vs(e,d,i,o)?Io(e,d+2,s?r.call(s,i,o):r(i,o)):tl(e,d+2)}function Lm(e,t,n,r,i,o,s,d){const p=t+n;return function Yl(e,t,n,r,i){const o=vs(e,t,n,r);return ei(e,t+2,i)||o}(e,p,i,o,s)?Io(e,p+3,d?r.call(d,i,o,s):r(i,o,s)):tl(e,p+3)}function Um(e,t){const n=Wt();let r;const i=e+Ot;n.firstCreatePass?(r=function F0(e,t){if(t)for(let n=t.length-1;n>=0;n--){const r=t[n];if(e===r.name)return r}}(t,n.pipeRegistry),n.data[i]=r,r.onDestroy&&(n.destroyHooks??=[]).push(i,r.onDestroy)):r=n.data[i];const o=r.factory||(r.factory=Bi(r.type)),d=pt(Xs);try{const p=dl(!1),E=o();return dl(p),function _E(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}(n,Ee(),i,E),E}finally{pt(d)}}function Bm(e,t,n){const r=e+Ot,i=Ee(),o=Er(i,r);return nl(i,r)?Fm(i,Hr(),t,o.transform,n,o):o.transform(n)}function $m(e,t,n,r){const i=e+Ot,o=Ee(),s=Er(o,i);return nl(o,i)?km(o,Hr(),t,s.transform,n,r,s):s.transform(n,r)}function Hm(e,t,n,r,i){const o=e+Ot,s=Ee(),d=Er(s,o);return nl(s,o)?Lm(s,Hr(),t,d.transform,n,r,i,d):d.transform(n,r,i)}function nl(e,t){return e[je].data[t].pure}function V0(){return this._results[Symbol.iterator]()}class wd{get changes(){return this._changes||(this._changes=new Mo)}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const n=wd.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=V0)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){const r=this;r.dirty=!1;const i=function no(e){return e.flat(Number.POSITIVE_INFINITY)}(t);(this._changesDetected=!function Nv(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let i=e[r],o=t[r];if(n&&(i=n(i),o=n(o)),o!==i)return!1}return!0}(r._results,i,n))&&(r._results=i,r.length=i.length,r.last=i[this.length-1],r.first=i[0])}notifyOnChanges(){this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}function U0(e,t,n,r=!0){const i=t[je];if(function CD(e,t,n,r){const i=Lt+r,o=n.length;r>0&&(n[i-1][Zn]=t),r<o-Lt?(t[Zn]=n[i],yf(n,Lt+r,t)):(n.push(t),t[Zn]=null),t[qt]=n;const s=t[oi];null!==s&&n!==s&&function _D(e,t){const n=e[zn];t[Yt]!==t[qt][qt][Yt]&&(e[si]=!0),null===n?e[zn]=[t]:n.push(t)}(s,t);const d=t[hr];null!==d&&d.insertView(e),t[Dt]|=128}(i,t,e,n),r){const o=Ju(n,e),s=t[mt],d=Al(s,e[Rr]);null!==d&&function yD(e,t,n,r,i,o){r[rn]=i,r[On]=t,Pa(e,r,n,1,i,o)}(i,e[On],s,t,d,o)}}let rl=(()=>{class e{static{this.__NG_ELEMENT_ID__=H0}}return e})();const B0=rl,$0=class extends B0{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){const i=function j0(e,t,n,r){const i=t.tView,d=Kl(e,i,n,4096&e[Dt]?4096:16,null,t,null,null,null,r?.injector??null,r?.hydrationInfo??null);d[oi]=e[t.index];const E=e[hr];return null!==E&&(d[hr]=E.createEmbeddedView(i)),zc(i,d,n),d}(this._declarationLView,this._declarationTContainer,t,{injector:n,hydrationInfo:r});return new Ha(i)}};function H0(){return pu(wr(),Ee())}function pu(e,t){return 4&e.type?new $0(t,e,Gs(e,t)):null}let mu=(()=>{class e{static{this.__NG_ELEMENT_ID__=Q0}}return e})();function Q0(){return qm(wr(),Ee())}const q0=mu,Xm=class extends q0{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return Gs(this._hostTNode,this._hostLView)}get injector(){return new di(this._hostTNode,this._hostLView)}get parentInjector(){const t=hl(this._hostTNode,this._hostLView);if(Ou(t)){const n=ba(t,this._hostLView),r=wa(t);return new di(n[je].data[r+8],n)}return new di(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=Qm(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-Lt}createEmbeddedView(t,n,r){let i,o;"number"==typeof r?i=r:null!=r&&(i=r.index,o=r.injector);const d=t.createEmbeddedViewImpl(n||{},o,null);return this.insertImpl(d,i,false),d}createComponent(t,n,r,i,o){const s=t&&!function Sa(e){return"function"==typeof e}(t);let d;if(s)d=n;else{const Se=n||{};d=Se.index,r=Se.injector,i=Se.projectableNodes,o=Se.environmentInjector||Se.ngModuleRef}const p=s?t:new za(Nt(t)),E=r||this.parentInjector;if(!o&&null==p.ngModule){const $e=(s?E:this.parentInjector).get(bo,null);$e&&(o=$e)}Nt(p.componentType??{});const K=p.create(E,i,null,o);return this.insertImpl(K.hostView,d,false),K}insert(t,n){return this.insertImpl(t,n,!1)}insertImpl(t,n,r){const i=t._lView;if(function Wo(e){return or(e[qt])}(i)){const p=this.indexOf(t);if(-1!==p)this.detach(p);else{const E=i[qt],A=new Xm(E,E[On],E[qt]);A.detach(A.indexOf(t))}}const s=this._adjustIndex(n),d=this._lContainer;return U0(d,i,s,!r),t.attachToViewContainerRef(),yf(bd(d),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=Qm(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=Tl(this._lContainer,n);r&&(gl(bd(this._lContainer),n),Xu(r[je],r))}detach(t){const n=this._adjustIndex(t,-1),r=Tl(this._lContainer,n);return r&&null!=gl(bd(this._lContainer),n)?new Ha(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function Qm(e){return e[8]}function bd(e){return e[8]||(e[8]=[])}function qm(e,t){let n;const r=t[e.index];return or(r)?n=r:(n=up(r,t,null,e),t[e.index]=n,Xl(t,n)),Zm(n,t,e,r),new Xm(n,e,t)}let Zm=function Jm(e,t,n,r){if(e[Rr])return;let i;i=8&n.type?St(r):function Z0(e,t){const n=e[mt],r=n.createComment(""),i=Gn(t,e);return ms(n,Al(n,i),r,function MD(e,t){return e.nextSibling(t)}(n,i),!1),r}(t,n),e[Rr]=i};class Md{constructor(t){this.queryList=t,this.matches=null}clone(){return new Md(this.queryList)}setDirty(){this.queryList.setDirty()}}class Sd{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const r=null!==t.contentQueries?t.contentQueries[0]:n.length,i=[];for(let o=0;o<r;o++){const s=n.getByIndex(o);i.push(this.queries[s.indexInDeclarationView].clone())}return new Sd(i)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==ay(t,n).matches&&this.queries[n].setDirty()}}class Ym{constructor(t,n,r=null){this.predicate=t,this.flags=n,this.read=r}}class Id{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){const i=null!==n?n.length:0,o=this.getByIndex(r).embeddedTView(t,i);o&&(o.indexInDeclarationView=r,null!==n?n.push(o):n=[o])}return null!==n?new Id(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Td{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new Td(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const n=this._declarationNodeIndex;let r=t.parent;for(;null!==r&&8&r.type&&r.index!==n;)r=r.parent;return n===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const r=this.metadata.predicate;if(Array.isArray(r))for(let i=0;i<r.length;i++){const o=r[i];this.matchTNodeWithReadOption(t,n,eb(n,o)),this.matchTNodeWithReadOption(t,n,pl(n,t,o,!1,!1))}else r===rl?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,pl(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(null!==r){const i=this.metadata.read;if(null!==i)if(i===ja||i===mu||i===rl&&4&n.type)this.addMatch(n.index,-2);else{const o=pl(n,t,i,!1,!1);null!==o&&this.addMatch(n.index,o)}else this.addMatch(n.index,r)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function eb(e,t){const n=e.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function nb(e,t,n,r){return-1===n?function tb(e,t){return 11&e.type?Gs(e,t):4&e.type?pu(e,t):null}(t,e):-2===n?function rb(e,t,n){return n===ja?Gs(t,e):n===rl?pu(t,e):n===mu?qm(t,e):void 0}(e,t,r):gs(e,e[je],n,t)}function ey(e,t,n,r){const i=t[hr].queries[r];if(null===i.matches){const o=e.data,s=n.matches,d=[];for(let p=0;p<s.length;p+=2){const E=s[p];d.push(E<0?null:nb(t,o[E],s[p+1],n.metadata.read))}i.matches=d}return i.matches}function Ad(e,t,n,r){const i=e.queries.getByIndex(n),o=i.matches;if(null!==o){const s=ey(e,t,i,n);for(let d=0;d<o.length;d+=2){const p=o[d];if(p>0)r.push(s[d/2]);else{const E=o[d+1],A=t[-p];for(let x=Lt;x<A.length;x++){const z=A[x];z[oi]===z[qt]&&Ad(z[je],z,E,r)}if(null!==A[zn]){const x=A[zn];for(let z=0;z<x.length;z++){const K=x[z];Ad(K[je],K,E,r)}}}}}return r}function ty(e){const t=Ee(),n=Wt(),r=w();N(r+1);const i=ay(n,r);if(e.dirty&&function wi(e){return 4==(4&e[Dt])}(t)===(2==(2&i.metadata.flags))){if(null===i.matches)e.reset([]);else{const o=i.crossesNgTemplate?Ad(n,t,r,[]):ey(n,t,i,r);e.reset(o,IC),e.notifyOnChanges()}return!0}return!1}function ny(e,t,n){const r=Wt();r.firstCreatePass&&(sy(r,new Ym(e,t,n),-1),2==(2&t)&&(r.staticViewQueries=!0)),oy(r,Ee(),t)}function ry(e,t,n,r){const i=Wt();if(i.firstCreatePass){const o=wr();sy(i,new Ym(t,n,r),o.index),function ob(e,t){const n=e.contentQueries||(e.contentQueries=[]);t!==(n.length?n[n.length-1]:-1)&&n.push(e.queries.length-1,t)}(i,e),2==(2&n)&&(i.staticContentQueries=!0)}oy(i,Ee(),n)}function iy(){return function ib(e,t){return e[hr].queries[t].queryList}(Ee(),w())}function oy(e,t,n){const r=new wd(4==(4&n));(function l_(e,t,n,r){const i=dp(t);i.push(n),e.firstCreatePass&&fp(e).push(r,i.length-1)})(e,t,r,r.destroy),null===t[hr]&&(t[hr]=new Sd),t[hr].queries.push(new Md(r))}function sy(e,t,n){null===e.queries&&(e.queries=new Id),e.queries.track(new Td(t,n))}function ay(e,t){return e.queries.getByIndex(t)}function Od(e){return!!An(e)}const by=new dt("Application Initializer");let Fd=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=Me(by,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const n=[];for(const i of this.appInits){const o=i();if(nd(o))n.push(o);else if(Xp(o)){const s=new Promise((d,p)=>{o.subscribe({complete:d,error:p})});n.push(s)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(i=>{this.reject(i)}),0===n.length&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),My=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();const vu=new dt("LocaleId",{providedIn:"root",factory:()=>Me(vu,Mt.Optional|Mt.SkipSelf)||function Ib(){return typeof $localize<"u"&&$localize.locale||la}()}),Tb=new dt("DefaultCurrencyCode",{providedIn:"root",factory:()=>"USD"});let Sy=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new fe.t(!1)}add(){this.hasPendingTasks.next(!0);const n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),0===this.pendingTasks.size&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks.next(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();class Ob{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}let Nb=(()=>{class e{compileModuleSync(n){return new _d(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){const r=this.compileModuleSync(n),o=jo(An(n).declarations).reduce((s,d)=>{const p=Nt(d);return p&&s.push(new za(p)),s},[]);return new Ob(r,o)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();const Oy=new dt(""),Ny=new dt("");let Vd,Yb=(()=>{class e{constructor(n,r,i){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Vd||(function eM(e){Vd=e}(i),i.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{Fr.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,i){let o=-1;r&&r>0&&(o=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==o),n(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:n,timeoutId:o,updateCb:i})}whenStable(n,r,i){if(i&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,i),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,i){return[]}static{this.\u0275fac=function(r){return new(r||e)(ue(Fr),ue(Py),ue(Ny))}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac})}}return e})(),Py=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Vd?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),Yo=null;const Ry=new dt("AllowMultipleToken"),jd=new dt("PlatformDestroyListeners"),Ud=new dt("appBootstrapListener");class rM{constructor(t,n){this.name=t,this.token=n}}function ky(e,t,n=[]){const r=`Platform: ${t}`,i=new dt(r);return(o=[])=>{let s=Bd();if(!s||s.injector.get(Ry,!1)){const d=[...n,...o,{provide:i,useValue:!0}];e?e(d):function iM(e){if(Yo&&!Yo.get(Ry,!1))throw new Pe(400,!1);(function xy(){!function I(e){T=e}(()=>{throw new Pe(600,!1)})})(),Yo=e;const t=e.get(Vy);(function Fy(e){e.get(_h,null)?.forEach(n=>n())})(e)}(function Ly(e=[],t){return fo.create({name:t,providers:[{provide:fc,useValue:"platform"},{provide:jd,useValue:new Set([()=>Yo=null])},...e]})}(d,r))}return function sM(e){const t=Bd();if(!t)throw new Pe(401,!1);return t}()}}function Bd(){return Yo?.get(Vy)??null}let Vy=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const i=function aM(e="zone.js",t){return"noop"===e?new zC:"zone.js"===e?new Fr(t):e}(r?.ngZone,function jy(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return i.run(()=>{const o=function C0(e,t,n){return new Cd(e,t,n)}(n.moduleType,this.injector,function zy(e){return[{provide:Fr,useFactory:e},{provide:ka,multi:!0,useFactory:()=>{const t=Me(uM,{optional:!0});return()=>t.initialize()}},{provide:Hy,useFactory:lM},{provide:Vh,useFactory:jh}]}(()=>i)),s=o.injector.get(Zo,null);return i.runOutsideAngular(()=>{const d=i.onError.subscribe({next:p=>{s.handleError(p)}});o.onDestroy(()=>{Cu(this._modules,o),d.unsubscribe()})}),function Uy(e,t,n){try{const r=n();return nd(r)?r.catch(i=>{throw t.runOutsideAngular(()=>e.handleError(i)),i}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(s,i,()=>{const d=o.injector.get(Fd);return d.runInitializers(),d.donePromise.then(()=>(function Xg(e){tn(e,"Expected localeId to be defined"),"string"==typeof e&&(Kg=e.toLowerCase().replace(/_/g,"-"))}(o.injector.get(vu,la)||la),this._moduleDoBootstrap(o),o))})})}bootstrapModule(n,r=[]){const i=By({},r);return function tM(e,t,n){const r=new _d(n);return Promise.resolve(r)}(0,0,n).then(o=>this.bootstrapModuleFactory(o,i))}_moduleDoBootstrap(n){const r=n.injector.get(da);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(i=>r.bootstrap(i));else{if(!n.instance.ngDoBootstrap)throw new Pe(-403,!1);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new Pe(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const n=this._injector.get(jd,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(ue(fo))}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function By(e,t){return Array.isArray(t)?t.reduce(By,e):{...e,...t}}let da=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=Me(Hy),this.zoneIsStable=Me(Vh),this.componentTypes=[],this.components=[],this.isStable=Me(Sy).hasPendingTasks.pipe((0,Vt.n)(n=>n?(0,H.of)(!1):this.zoneIsStable),(0,bt.F)(),_e()),this._injector=Me(bo)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){const i=n instanceof Mh;if(!this._injector.get(Fd).done)throw!i&&bn(n),new Pe(405,!1);let s;s=i?n:this._injector.get(Hl).resolveComponentFactory(n),this.componentTypes.push(s.componentType);const d=function nM(e){return e.isBoundToModule}(s)?void 0:this._injector.get(_s),E=s.create(fo.NULL,[],r||s.selector,d),A=E.location.nativeElement,x=E.injector.get(Oy,null);return x?.registerApplication(A),E.onDestroy(()=>{this.detachView(E.hostView),Cu(this.components,E),x?.unregisterApplication(A)}),this._loadComponent(E),E}tick(){if(this._runningTick)throw new Pe(101,!1);try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this.internalErrorHandler(n)}finally{this._runningTick=!1}}attachView(n){const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){const r=n;Cu(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const r=this._injector.get(Ud,[]);r.push(...this._bootstrapListeners),r.forEach(i=>i(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Cu(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new Pe(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Cu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const Hy=new dt("",{providedIn:"root",factory:()=>Me(Zo).handleError.bind(void 0)});function lM(){const e=Me(Fr),t=Me(Zo);return n=>e.runOutsideAngular(()=>t.handleError(n))}let uM=(()=>{class e{constructor(){this.zone=Me(Fr),this.applicationRef=Me(da)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=Re({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();let dM=(()=>{class e{static{this.__NG_ELEMENT_ID__=fM}}return e})();function fM(e){return function hM(e,t,n){if(Cr(e)&&!n){const r=Nn(e.index,t);return new Ha(r,r)}return 47&e.type?new Ha(t[Yt],t):null}(wr(),Ee(),16==(16&e))}class Qy{constructor(){}supports(t){return Jl(t)}create(t){return new vM(t)}}const yM=(e,t)=>t;class vM{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||yM}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,i=0,o=null;for(;n||r;){const s=!r||n&&n.currentIndex<Zy(r,i,o)?n:r,d=Zy(s,i,o),p=s.currentIndex;if(s===r)i--,r=r._nextRemoved;else if(n=n._next,null==s.previousIndex)i++;else{o||(o=[]);const E=d-i,A=p-i;if(E!=A){for(let z=0;z<E;z++){const K=z<o.length?o[z]:o[z]=0,me=K+z;A<=me&&me<E&&(o[z]=K+1)}o[s.previousIndex]=A-E}}d!==p&&t(s,d,p)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!Jl(t))throw new Pe(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let i,o,s,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let d=0;d<this.length;d++)o=t[d],s=this._trackByFn(d,o),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,o,s,d)),Object.is(n.item,o)||this._addIdentityChange(n,o)):(n=this._mismatch(n,o,s,d),r=!0),n=n._next}else i=0,function eE(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,d=>{s=this._trackByFn(i,d),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,d,s,i)),Object.is(n.item,d)||this._addIdentityChange(n,d)):(n=this._mismatch(n,d,s,i),r=!0),n=n._next,i++}),this.length=i;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,i){let o;return null===t?o=this._itTail:(o=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,o,i)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,i))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,o,i)):t=this._addAfter(new DM(n,r),o,i),t}_verifyReinsertion(t,n,r,i){let o=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==o?t=this._reinsertAfter(o,t._prev,i):t.currentIndex!=i&&(t.currentIndex=i,this._addToMoves(t,i)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const i=t._prevRemoved,o=t._nextRemoved;return null===i?this._removalsHead=o:i._nextRemoved=o,null===o?this._removalsTail=i:o._prevRemoved=i,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const i=null===n?this._itHead:n._next;return t._next=i,t._prev=n,null===i?this._itTail=t:i._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new qy),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new qy),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class DM{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class CM{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class qy{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new CM,this.map.set(n,r)),r.add(t)}get(t,n){const i=this.map.get(t);return i?i.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function Zy(e,t,n){const r=e.previousIndex;if(null===r)return r;let i=0;return n&&r<n.length&&(i=n[r]),r+t+i}class Jy{constructor(){}supports(t){return t instanceof Map||Wc(t)}create(){return new _M}}class _M{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||Wc(t)))throw new Pe(900,!1)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,i)=>{if(n&&n.key===i)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const o=this._getOrCreateRecordForKey(i,r);n=this._insertBeforeOrAppend(n,o)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const i=this._records.get(t);this._maybeAddToChanges(i,n);const o=i._prev,s=i._next;return o&&(o._next=s),s&&(s._prev=o),i._next=null,i._prev=null,i}const r=new EM(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class EM{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function Yy(){return new Wd([new Qy])}let Wd=(()=>{class e{static{this.\u0275prov=Re({token:e,providedIn:"root",factory:Yy})}constructor(n){this.factories=n}static create(n,r){if(null!=r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||Yy()),deps:[[e,new vl,new yl]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(null!=r)return r;throw new Pe(901,!1)}}return e})();function ev(){return new Kd([new Jy])}let Kd=(()=>{class e{static{this.\u0275prov=Re({token:e,providedIn:"root",factory:ev})}constructor(n){this.factories=n}static create(n,r){if(r){const i=r.factories.slice();n=n.concat(i)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||ev()),deps:[[e,new vl,new yl]]}}find(n){const r=this.factories.find(i=>i.supports(n));if(r)return r;throw new Pe(901,!1)}}return e})();const MM=ky(null,"core",[]);let SM=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(ue(da))}}static{this.\u0275mod=Pi({type:e})}static{this.\u0275inj=lr({})}}return e})();function LM(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}function jM(e){const t=Nt(e);if(!t)return null;const n=new za(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}},4341:(qe,ye,O)=>{O.d(ye,{Zm:()=>bt,me:()=>xe,ok:()=>Li,JD:()=>rn,j4:()=>bn,YN:()=>mo,tU:()=>si,zX:()=>oi,VZ:()=>hr,BC:()=>vt,cb:()=>ht,vS:()=>mi,xH:()=>Qr,Q0:()=>Ji,Fm:()=>Wr,X1:()=>es,wz:()=>Zn,k0:()=>ot,qT:()=>oo,y7:()=>mt});var c=O(6276),C=O(177),ce=O(6648),se=O(1985),X=O(3073),ne=O(8750),ee=O(9326),de=O(4360),le=O(6450),fe=O(8496),G=O(6354);let ve=(()=>{class b{constructor(v,P){this._renderer=v,this._elementRef=P,this.onChange=B=>{},this.onTouched=()=>{}}setProperty(v,P){this._renderer.setProperty(this._elementRef.nativeElement,v,P)}registerOnTouched(v){this.onTouched=v}registerOnChange(v){this.onChange=v}setDisabledState(v){this.setProperty("disabled",v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.sFG),c.rXU(c.aKT))}}static{this.\u0275dir=c.FsC({type:b})}}return b})(),_e=(()=>{class b extends ve{static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,features:[c.Vt3]})}}return b})();const Ve=new c.nKC("NgValueAccessor"),Vt={provide:Ve,useExisting:(0,c.Rfq)(()=>bt),multi:!0};let bt=(()=>{class b extends _e{writeValue(v){this.setProperty("checked",v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","checkbox","formControlName",""],["input","type","checkbox","formControl",""],["input","type","checkbox","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(Ct){return B.onChange(Ct.target.checked)})("blur",function(){return B.onTouched()})},features:[c.Jv_([Vt]),c.Vt3]})}}return b})();const Ze={provide:Ve,useExisting:(0,c.Rfq)(()=>xe),multi:!0},yt=new c.nKC("CompositionEventMode");let xe=(()=>{class b extends ve{constructor(v,P,B){super(v,P),this._compositionMode=B,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function Rt(){const b=(0,C.QT)()?(0,C.QT)().getUserAgent():"";return/android (\d+)/.test(b.toLowerCase())}())}writeValue(v){this.setProperty("value",v??"")}_handleInput(v){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(v)}_compositionStart(){this._composing=!0}_compositionEnd(v){this._composing=!1,this._compositionMode&&this.onChange(v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.sFG),c.rXU(c.aKT),c.rXU(yt,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(P,B){1&P&&c.bIt("input",function(Ct){return B._handleInput(Ct.target.value)})("blur",function(){return B.onTouched()})("compositionstart",function(){return B._compositionStart()})("compositionend",function(Ct){return B._compositionEnd(Ct.target.value)})},features:[c.Jv_([Ze]),c.Vt3]})}}return b})();function Ne(b){return null==b||("string"==typeof b||Array.isArray(b))&&0===b.length}function Ke(b){return null!=b&&"number"==typeof b.length}const we=new c.nKC("NgValidators"),ke=new c.nKC("NgAsyncValidators"),Te=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;class ot{static min(S){return rt(S)}static max(S){return Pe(S)}static required(S){return function cn(b){return Ne(b.value)?{required:!0}:null}(S)}static requiredTrue(S){return function et(b){return!0===b.value?null:{required:!0}}(S)}static email(S){return function gt(b){return Ne(b.value)||Te.test(b.value)?null:{email:!0}}(S)}static minLength(S){return function dn(b){return S=>Ne(S.value)||!Ke(S.value)?null:S.value.length<b?{minlength:{requiredLength:b,actualLength:S.value.length}}:null}(S)}static maxLength(S){return jt(S)}static pattern(S){return function Mn(b){if(!b)return Sn;let S,v;return"string"==typeof b?(v="","^"!==b.charAt(0)&&(v+="^"),v+=b,"$"!==b.charAt(b.length-1)&&(v+="$"),S=new RegExp(v)):(v=b.toString(),S=b),P=>{if(Ne(P.value))return null;const B=P.value;return S.test(B)?null:{pattern:{requiredPattern:v,actualValue:B}}}}(S)}static nullValidator(S){return null}static compose(S){return be(S)}static composeAsync(S){return ft(S)}}function rt(b){return S=>{if(Ne(S.value)||Ne(b))return null;const v=parseFloat(S.value);return!isNaN(v)&&v<b?{min:{min:b,actual:S.value}}:null}}function Pe(b){return S=>{if(Ne(S.value)||Ne(b))return null;const v=parseFloat(S.value);return!isNaN(v)&&v>b?{max:{max:b,actual:S.value}}:null}}function jt(b){return S=>Ke(S.value)&&S.value.length>b?{maxlength:{requiredLength:b,actualLength:S.value.length}}:null}function Sn(b){return null}function fn(b){return null!=b}function Z(b){return(0,c.jNT)(b)?(0,ce.H)(b):b}function te(b){let S={};return b.forEach(v=>{S=null!=v?{...S,...v}:S}),0===Object.keys(S).length?null:S}function ie(b,S){return S.map(v=>v(b))}function Ie(b){return b.map(S=>function ae(b){return!b.validate}(S)?S:v=>S.validate(v))}function be(b){if(!b)return null;const S=b.filter(fn);return 0==S.length?null:function(v){return te(ie(v,S))}}function Ge(b){return null!=b?be(Ie(b)):null}function ft(b){if(!b)return null;const S=b.filter(fn);return 0==S.length?null:function(v){return function H(...b){const S=(0,ee.ms)(b),{args:v,keys:P}=(0,X.D)(b),B=new se.c(ze=>{const{length:Ct}=v;if(!Ct)return void ze.complete();const er=new Array(Ct);let Vi=Ct,Ci=Ct;for(let ji=0;ji<Ct;ji++){let Ui=!1;(0,ne.Tg)(v[ji]).subscribe((0,de._)(ze,li=>{Ui||(Ui=!0,Ci--),er[ji]=li},()=>Vi--,void 0,()=>{(!Vi||!Ui)&&(Ci||ze.next(P?(0,fe.e)(P,er):er),ze.complete())}))}});return S?B.pipe((0,le.I)(S)):B}(ie(v,S).map(Z)).pipe((0,G.T)(te))}}function Bt(b){return null!=b?ft(Ie(b)):null}function ut(b,S){return null===b?[S]:Array.isArray(b)?[...b,S]:[b,S]}function xt(b){return b._rawValidators}function Qt(b){return b._rawAsyncValidators}function tn(b){return b?Array.isArray(b)?b:[b]:[]}function Ae(b,S){return Array.isArray(b)?b.includes(S):b===S}function hn(b,S){const v=tn(S);return tn(b).forEach(B=>{Ae(v,B)||v.push(B)}),v}function xn(b,S){return tn(S).filter(v=>!Ae(b,v))}class tr{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(S){this._rawValidators=S||[],this._composedValidatorFn=Ge(this._rawValidators)}_setAsyncValidators(S){this._rawAsyncValidators=S||[],this._composedAsyncValidatorFn=Bt(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(S){this._onDestroyCallbacks.push(S)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(S=>S()),this._onDestroyCallbacks=[]}reset(S=void 0){this.control&&this.control.reset(S)}hasError(S,v){return!!this.control&&this.control.hasError(S,v)}getError(S,v){return this.control?this.control.getError(S,v):null}}class Re extends tr{get formDirective(){return null}get path(){return null}}class Un extends tr{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class lr{constructor(S){this._cd=S}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let vt=(()=>{class b extends lr{constructor(v){super(v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Un,2))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(P,B){2&P&&c.AVh("ng-untouched",B.isUntouched)("ng-touched",B.isTouched)("ng-pristine",B.isPristine)("ng-dirty",B.isDirty)("ng-valid",B.isValid)("ng-invalid",B.isInvalid)("ng-pending",B.isPending)},features:[c.Vt3]})}}return b})(),ht=(()=>{class b extends lr{constructor(v){super(v)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Re,10))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(P,B){2&P&&c.AVh("ng-untouched",B.isUntouched)("ng-touched",B.isTouched)("ng-pristine",B.isPristine)("ng-dirty",B.isDirty)("ng-valid",B.isValid)("ng-invalid",B.isInvalid)("ng-pending",B.isPending)("ng-submitted",B.isSubmitted)},features:[c.Vt3]})}}return b})();const Xn="VALID",rr="INVALID",cr="PENDING",kr="DISABLED";function Lr(b){return(W(b)?b.validators:b)||null}function q(b,S){return(W(S)?S.asyncValidators:b)||null}function W(b){return null!=b&&!Array.isArray(b)&&"object"==typeof b}function pe(b,S,v){const P=b.controls;if(!(S?Object.keys(P):P).length)throw new c.wOt(1e3,"");if(!P[v])throw new c.wOt(1001,"")}function Fe(b,S,v){b._forEachChild((P,B)=>{if(void 0===v[B])throw new c.wOt(1002,"")})}class We{constructor(S,v){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(S),this._assignAsyncValidators(v)}get validator(){return this._composedValidatorFn}set validator(S){this._rawValidators=this._composedValidatorFn=S}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(S){this._rawAsyncValidators=this._composedAsyncValidatorFn=S}get parent(){return this._parent}get valid(){return this.status===Xn}get invalid(){return this.status===rr}get pending(){return this.status==cr}get disabled(){return this.status===kr}get enabled(){return this.status!==kr}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(S){this._assignValidators(S)}setAsyncValidators(S){this._assignAsyncValidators(S)}addValidators(S){this.setValidators(hn(S,this._rawValidators))}addAsyncValidators(S){this.setAsyncValidators(hn(S,this._rawAsyncValidators))}removeValidators(S){this.setValidators(xn(S,this._rawValidators))}removeAsyncValidators(S){this.setAsyncValidators(xn(S,this._rawAsyncValidators))}hasValidator(S){return Ae(this._rawValidators,S)}hasAsyncValidator(S){return Ae(this._rawAsyncValidators,S)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(S={}){this.touched=!0,this._parent&&!S.onlySelf&&this._parent.markAsTouched(S)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(S=>S.markAllAsTouched())}markAsUntouched(S={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(v=>{v.markAsUntouched({onlySelf:!0})}),this._parent&&!S.onlySelf&&this._parent._updateTouched(S)}markAsDirty(S={}){this.pristine=!1,this._parent&&!S.onlySelf&&this._parent.markAsDirty(S)}markAsPristine(S={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(v=>{v.markAsPristine({onlySelf:!0})}),this._parent&&!S.onlySelf&&this._parent._updatePristine(S)}markAsPending(S={}){this.status=cr,!1!==S.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!S.onlySelf&&this._parent.markAsPending(S)}disable(S={}){const v=this._parentMarkedDirty(S.onlySelf);this.status=kr,this.errors=null,this._forEachChild(P=>{P.disable({...S,onlySelf:!0})}),this._updateValue(),!1!==S.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...S,skipPristineCheck:v}),this._onDisabledChange.forEach(P=>P(!0))}enable(S={}){const v=this._parentMarkedDirty(S.onlySelf);this.status=Xn,this._forEachChild(P=>{P.enable({...S,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:S.emitEvent}),this._updateAncestors({...S,skipPristineCheck:v}),this._onDisabledChange.forEach(P=>P(!1))}_updateAncestors(S){this._parent&&!S.onlySelf&&(this._parent.updateValueAndValidity(S),S.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(S){this._parent=S}getRawValue(){return this.value}updateValueAndValidity(S={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Xn||this.status===cr)&&this._runAsyncValidator(S.emitEvent)),!1!==S.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!S.onlySelf&&this._parent.updateValueAndValidity(S)}_updateTreeValidity(S={emitEvent:!0}){this._forEachChild(v=>v._updateTreeValidity(S)),this.updateValueAndValidity({onlySelf:!0,emitEvent:S.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?kr:Xn}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(S){if(this.asyncValidator){this.status=cr,this._hasOwnPendingAsyncValidator=!0;const v=Z(this.asyncValidator(this));this._asyncValidationSubscription=v.subscribe(P=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(P,{emitEvent:S})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(S,v={}){this.errors=S,this._updateControlsErrors(!1!==v.emitEvent)}get(S){let v=S;return null==v||(Array.isArray(v)||(v=v.split(".")),0===v.length)?null:v.reduce((P,B)=>P&&P._find(B),this)}getError(S,v){const P=v?this.get(v):this;return P&&P.errors?P.errors[S]:null}hasError(S,v){return!!this.getError(S,v)}get root(){let S=this;for(;S._parent;)S=S._parent;return S}_updateControlsErrors(S){this.status=this._calculateStatus(),S&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(S)}_initObservables(){this.valueChanges=new c.bkB,this.statusChanges=new c.bkB}_calculateStatus(){return this._allControlsDisabled()?kr:this.errors?rr:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(cr)?cr:this._anyControlsHaveStatus(rr)?rr:Xn}_anyControlsHaveStatus(S){return this._anyControls(v=>v.status===S)}_anyControlsDirty(){return this._anyControls(S=>S.dirty)}_anyControlsTouched(){return this._anyControls(S=>S.touched)}_updatePristine(S={}){this.pristine=!this._anyControlsDirty(),this._parent&&!S.onlySelf&&this._parent._updatePristine(S)}_updateTouched(S={}){this.touched=this._anyControlsTouched(),this._parent&&!S.onlySelf&&this._parent._updateTouched(S)}_registerOnCollectionChange(S){this._onCollectionChange=S}_setUpdateStrategy(S){W(S)&&null!=S.updateOn&&(this._updateOn=S.updateOn)}_parentMarkedDirty(S){return!S&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(S){return null}_assignValidators(S){this._rawValidators=Array.isArray(S)?S.slice():S,this._composedValidatorFn=function he(b){return Array.isArray(b)?Ge(b):b||null}(this._rawValidators)}_assignAsyncValidators(S){this._rawAsyncValidators=Array.isArray(S)?S.slice():S,this._composedAsyncValidatorFn=function V(b){return Array.isArray(b)?Bt(b):b||null}(this._rawAsyncValidators)}}class At extends We{constructor(S,v,P){super(Lr(v),q(P,v)),this.controls=S,this._initObservables(),this._setUpdateStrategy(v),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(S,v){return this.controls[S]?this.controls[S]:(this.controls[S]=v,v.setParent(this),v._registerOnCollectionChange(this._onCollectionChange),v)}addControl(S,v,P={}){this.registerControl(S,v),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}removeControl(S,v={}){this.controls[S]&&this.controls[S]._registerOnCollectionChange(()=>{}),delete this.controls[S],this.updateValueAndValidity({emitEvent:v.emitEvent}),this._onCollectionChange()}setControl(S,v,P={}){this.controls[S]&&this.controls[S]._registerOnCollectionChange(()=>{}),delete this.controls[S],v&&this.registerControl(S,v),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}contains(S){return this.controls.hasOwnProperty(S)&&this.controls[S].enabled}setValue(S,v={}){Fe(this,0,S),Object.keys(S).forEach(P=>{pe(this,!0,P),this.controls[P].setValue(S[P],{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v)}patchValue(S,v={}){null!=S&&(Object.keys(S).forEach(P=>{const B=this.controls[P];B&&B.patchValue(S[P],{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v))}reset(S={},v={}){this._forEachChild((P,B)=>{P.reset(S?S[B]:null,{onlySelf:!0,emitEvent:v.emitEvent})}),this._updatePristine(v),this._updateTouched(v),this.updateValueAndValidity(v)}getRawValue(){return this._reduceChildren({},(S,v,P)=>(S[P]=v.getRawValue(),S))}_syncPendingControls(){let S=this._reduceChildren(!1,(v,P)=>!!P._syncPendingControls()||v);return S&&this.updateValueAndValidity({onlySelf:!0}),S}_forEachChild(S){Object.keys(this.controls).forEach(v=>{const P=this.controls[v];P&&S(P,v)})}_setUpControls(){this._forEachChild(S=>{S.setParent(this),S._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(S){for(const[v,P]of Object.entries(this.controls))if(this.contains(v)&&S(P))return!0;return!1}_reduceValue(){return this._reduceChildren({},(v,P,B)=>((P.enabled||this.disabled)&&(v[B]=P.value),v))}_reduceChildren(S,v){let P=S;return this._forEachChild((B,ze)=>{P=v(P,B,ze)}),P}_allControlsDisabled(){for(const S of Object.keys(this.controls))if(this.controls[S].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(S){return this.controls.hasOwnProperty(S)?this.controls[S]:null}}class Bn extends At{}const Q=new c.nKC("CallSetDisabledState",{providedIn:"root",factory:()=>j}),j="always";function $(b,S){return[...S.path,b]}function ue(b,S,v=j){Ut(b,S),S.valueAccessor.writeValue(b.value),(b.disabled||"always"===v)&&S.valueAccessor.setDisabledState?.(b.disabled),function vn(b,S){S.valueAccessor.registerOnChange(v=>{b._pendingValue=v,b._pendingChange=!0,b._pendingDirty=!0,"change"===b.updateOn&&In(b,S)})}(b,S),function pn(b,S){const v=(P,B)=>{S.valueAccessor.writeValue(P),B&&S.viewToModelUpdate(P)};b.registerOnChange(v),S._registerOnDestroy(()=>{b._unregisterOnChange(v)})}(b,S),function $n(b,S){S.valueAccessor.registerOnTouched(()=>{b._pendingTouched=!0,"blur"===b.updateOn&&b._pendingChange&&In(b,S),"submit"!==b.updateOn&&b.markAsTouched()})}(b,S),function Le(b,S){if(S.valueAccessor.setDisabledState){const v=P=>{S.valueAccessor.setDisabledState(P)};b.registerOnDisabledChange(v),S._registerOnDestroy(()=>{b._unregisterOnDisabledChange(v)})}}(b,S)}function Oe(b,S,v=!0){const P=()=>{};S.valueAccessor&&(S.valueAccessor.registerOnChange(P),S.valueAccessor.registerOnTouched(P)),tt(b,S),b&&(S._invokeOnDestroyCallbacks(),b._registerOnCollectionChange(()=>{}))}function Me(b,S){b.forEach(v=>{v.registerOnValidatorChange&&v.registerOnValidatorChange(S)})}function Ut(b,S){const v=xt(b);null!==S.validator?b.setValidators(ut(v,S.validator)):"function"==typeof v&&b.setValidators([v]);const P=Qt(b);null!==S.asyncValidator?b.setAsyncValidators(ut(P,S.asyncValidator)):"function"==typeof P&&b.setAsyncValidators([P]);const B=()=>b.updateValueAndValidity();Me(S._rawValidators,B),Me(S._rawAsyncValidators,B)}function tt(b,S){let v=!1;if(null!==b){if(null!==S.validator){const B=xt(b);if(Array.isArray(B)&&B.length>0){const ze=B.filter(Ct=>Ct!==S.validator);ze.length!==B.length&&(v=!0,b.setValidators(ze))}}if(null!==S.asyncValidator){const B=Qt(b);if(Array.isArray(B)&&B.length>0){const ze=B.filter(Ct=>Ct!==S.asyncValidator);ze.length!==B.length&&(v=!0,b.setAsyncValidators(ze))}}}const P=()=>{};return Me(S._rawValidators,P),Me(S._rawAsyncValidators,P),v}function In(b,S){b._pendingDirty&&b.markAsDirty(),b.setValue(b._pendingValue,{emitModelToViewChange:!1}),S.viewToModelUpdate(b._pendingValue),b._pendingChange=!1}function dr(b,S){if(!b.hasOwnProperty("model"))return!1;const v=b.model;return!!v.isFirstChange()||!Object.is(S,v.currentValue)}function Vr(b,S){if(!S)return null;let v,P,B;return Array.isArray(S),S.forEach(ze=>{ze.constructor===xe?v=ze:function fr(b){return Object.getPrototypeOf(b.constructor)===_e}(ze)?P=ze:B=ze}),B||P||v||null}function Ni(b,S){const v=b.indexOf(S);v>-1&&b.splice(v,1)}function Qi(b){return"object"==typeof b&&null!==b&&2===Object.keys(b).length&&"value"in b&&"disabled"in b}const Ar=class extends We{constructor(S=null,v,P){super(Lr(v),q(P,v)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(S),this._setUpdateStrategy(v),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),W(v)&&(v.nonNullable||v.initialValueIsDefault)&&(this.defaultValue=Qi(S)?S.value:S)}setValue(S,v={}){this.value=this._pendingValue=S,this._onChange.length&&!1!==v.emitModelToViewChange&&this._onChange.forEach(P=>P(this.value,!1!==v.emitViewToModelChange)),this.updateValueAndValidity(v)}patchValue(S,v={}){this.setValue(S,v)}reset(S=this.defaultValue,v={}){this._applyFormState(S),this.markAsPristine(v),this.markAsUntouched(v),this.setValue(this.value,v),this._pendingChange=!1}_updateValue(){}_anyControls(S){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(S){this._onChange.push(S)}_unregisterOnChange(S){Ni(this._onChange,S)}registerOnDisabledChange(S){this._onDisabledChange.push(S)}_unregisterOnDisabledChange(S){Ni(this._onDisabledChange,S)}_forEachChild(S){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(S){Qi(S)?(this.value=this._pendingValue=S.value,S.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=S}},io={provide:Un,useExisting:(0,c.Rfq)(()=>mi)},gi=(()=>Promise.resolve())();let mi=(()=>{class b extends Un{constructor(v,P,B,ze,Ct,er){super(),this._changeDetectorRef=Ct,this.callSetDisabledState=er,this.control=new Ar,this._registered=!1,this.name="",this.update=new c.bkB,this._parent=v,this._setValidators(P),this._setAsyncValidators(B),this.valueAccessor=Vr(0,ze)}ngOnChanges(v){if(this._checkForErrors(),!this._registered||"name"in v){if(this._registered&&(this._checkName(),this.formDirective)){const P=v.name.previousValue;this.formDirective.removeControl({name:P,path:this._getPath(P)})}this._setUpControl()}"isDisabled"in v&&this._updateDisabled(v),dr(v,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(v){this.viewModel=v,this.update.emit(v)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){ue(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(v){gi.then(()=>{this.control.setValue(v,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(v){const P=v.isDisabled.currentValue,B=0!==P&&(0,c.L39)(P);gi.then(()=>{B&&!this.control.disabled?this.control.disable():!B&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(v){return this._parent?$(v,this._parent):[v]}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Re,9),c.rXU(we,10),c.rXU(ke,10),c.rXU(Ve,10),c.rXU(c.gRc,8),c.rXU(Q,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[c.Jv_([io]),c.Vt3,c.OA$]})}}return b})(),oo=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275dir=c.FsC({type:b,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return b})();const Po={provide:Ve,useExisting:(0,c.Rfq)(()=>Ji),multi:!0};let Ji=(()=>{class b extends _e{writeValue(v){this.setProperty("value",v??"")}registerOnChange(v){this.onChange=P=>{v(""==P?null:parseFloat(P))}}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("input",function(Ct){return B.onChange(Ct.target.value)})("blur",function(){return B.onTouched()})},features:[c.Jv_([Po]),c.Vt3]})}}return b})();const Ro={provide:Ve,useExisting:(0,c.Rfq)(()=>Wr),multi:!0};let yi=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({})}}return b})(),Pi=(()=>{class b{constructor(){this._accessors=[]}add(v,P){this._accessors.push([v,P])}remove(v){for(let P=this._accessors.length-1;P>=0;--P)if(this._accessors[P][1]===v)return void this._accessors.splice(P,1)}select(v){this._accessors.forEach(P=>{this._isSameGroup(P,v)&&P[1]!==v&&P[1].fireUncheck(v.value)})}_isSameGroup(v,P){return!!v[0].control&&v[0]._parent===P._control._parent&&v[1].name===P.name}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275prov=c.jDH({token:b,factory:b.\u0275fac,providedIn:yi})}}return b})(),Wr=(()=>{class b extends _e{constructor(v,P,B,ze){super(v,P),this._registry=B,this._injector=ze,this.setDisabledStateFired=!1,this.onChange=()=>{},this.callSetDisabledState=(0,c.WQX)(Q,{optional:!0})??j}ngOnInit(){this._control=this._injector.get(Un),this._checkName(),this._registry.add(this._control,this)}ngOnDestroy(){this._registry.remove(this)}writeValue(v){this._state=v===this.value,this.setProperty("checked",this._state)}registerOnChange(v){this._fn=v,this.onChange=()=>{v(this.value),this._registry.select(this)}}setDisabledState(v){(this.setDisabledStateFired||v||"whenDisabledForLegacyCode"===this.callSetDisabledState)&&this.setProperty("disabled",v),this.setDisabledStateFired=!0}fireUncheck(v){this.writeValue(v)}_checkName(){!this.name&&this.formControlName&&(this.name=this.formControlName)}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.sFG),c.rXU(c.aKT),c.rXU(Pi),c.rXU(c.zZn))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","radio","formControlName",""],["input","type","radio","formControl",""],["input","type","radio","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(){return B.onChange()})("blur",function(){return B.onTouched()})},inputs:{name:"name",formControlName:"formControlName",value:"value"},features:[c.Jv_([Ro]),c.Vt3]})}}return b})();const Kr=new c.nKC("NgModelWithFormControlWarning"),Ln={provide:Re,useExisting:(0,c.Rfq)(()=>bn)};let bn=(()=>{class b extends Re{constructor(v,P,B){super(),this.callSetDisabledState=B,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new c.bkB,this._setValidators(v),this._setAsyncValidators(P)}ngOnChanges(v){this._checkFormPresent(),v.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(tt(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(v){const P=this.form.get(v.path);return ue(P,v,this.callSetDisabledState),P.updateValueAndValidity({emitEvent:!1}),this.directives.push(v),P}getControl(v){return this.form.get(v.path)}removeControl(v){Oe(v.control||null,v,!1),function Ki(b,S){const v=b.indexOf(S);v>-1&&b.splice(v,1)}(this.directives,v)}addFormGroup(v){this._setUpFormContainer(v)}removeFormGroup(v){this._cleanUpFormContainer(v)}getFormGroup(v){return this.form.get(v.path)}addFormArray(v){this._setUpFormContainer(v)}removeFormArray(v){this._cleanUpFormContainer(v)}getFormArray(v){return this.form.get(v.path)}updateModel(v,P){this.form.get(v.path).setValue(P)}onSubmit(v){return this.submitted=!0,function zr(b,S){b._syncPendingControls(),S.forEach(v=>{const P=v.control;"submit"===P.updateOn&&P._pendingChange&&(v.viewToModelUpdate(P._pendingValue),P._pendingChange=!1)})}(this.form,this.directives),this.ngSubmit.emit(v),"dialog"===v?.target?.method}onReset(){this.resetForm()}resetForm(v=void 0){this.form.reset(v),this.submitted=!1}_updateDomValue(){this.directives.forEach(v=>{const P=v.control,B=this.form.get(v.path);P!==B&&(Oe(P||null,v),(b=>b instanceof Ar)(B)&&(ue(B,v,this.callSetDisabledState),v.control=B))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(v){const P=this.form.get(v.path);(function Tr(b,S){Ut(b,S)})(P,v),P.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(v){if(this.form){const P=this.form.get(v.path);P&&function wn(b,S){return tt(b,S)}(P,v)&&P.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Ut(this.form,this),this._oldForm&&tt(this._oldForm,this)}_checkFormPresent(){}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(we,10),c.rXU(ke,10),c.rXU(Q,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formGroup",""]],hostBindings:function(P,B){1&P&&c.bIt("submit",function(Ct){return B.onSubmit(Ct)})("reset",function(){return B.onReset()})},inputs:{form:["formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[c.Jv_([Ln]),c.Vt3,c.OA$]})}}return b})();const ao={provide:Un,useExisting:(0,c.Rfq)(()=>rn)};let rn=(()=>{class b extends Un{set isDisabled(v){}static{this._ngModelWarningSentOnce=!1}constructor(v,P,B,ze,Ct){super(),this._ngModelWarningConfig=Ct,this._added=!1,this.name=null,this.update=new c.bkB,this._ngModelWarningSent=!1,this._parent=v,this._setValidators(P),this._setAsyncValidators(B),this.valueAccessor=Vr(0,ze)}ngOnChanges(v){this._added||this._setUpControl(),dr(v,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(v){this.viewModel=v,this.update.emit(v)}get path(){return $(null==this.name?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(Re,13),c.rXU(we,10),c.rXU(ke,10),c.rXU(Ve,10),c.rXU(Kr,8))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["","formControlName",""]],inputs:{name:["formControlName","name"],isDisabled:["disabled","isDisabled"],model:["ngModel","model"]},outputs:{update:"ngModelChange"},features:[c.Jv_([ao]),c.Vt3,c.OA$]})}}return b})();const je={provide:Ve,useExisting:(0,c.Rfq)(()=>Zn),multi:!0};function Dt(b,S){return null==b?`${S}`:(S&&"object"==typeof S&&(S="Object"),`${b}: ${S}`.slice(0,50))}let Zn=(()=>{class b extends _e{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(v){this._compareWith=v}writeValue(v){this.value=v;const B=Dt(this._getOptionId(v),v);this.setProperty("value",B)}registerOnChange(v){this.onChange=P=>{this.value=this._getOptionValue(P),v(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(v){for(const P of this._optionMap.keys())if(this._compareWith(this._optionMap.get(P),v))return P;return null}_getOptionValue(v){const P=function qt(b){return b.split(":")[0]}(v);return this._optionMap.has(P)?this._optionMap.get(P):v}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(Ct){return B.onChange(Ct.target.value)})("blur",function(){return B.onTouched()})},inputs:{compareWith:"compareWith"},features:[c.Jv_([je]),c.Vt3]})}}return b})(),Qr=(()=>{class b{constructor(v,P,B){this._element=v,this._renderer=P,this._select=B,this._select&&(this.id=this._select._registerOption())}set ngValue(v){null!=this._select&&(this._select._optionMap.set(this.id,v),this._setElementValue(Dt(this.id,v)),this._select.writeValue(this._select.value))}set value(v){this._setElementValue(v),this._select&&this._select.writeValue(this._select.value)}_setElementValue(v){this._renderer.setProperty(this._element.nativeElement,"value",v)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.aKT),c.rXU(c.sFG),c.rXU(Zn,9))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}}return b})();const On={provide:Ve,useExisting:(0,c.Rfq)(()=>Nr),multi:!0};function Br(b,S){return null==b?`${S}`:("string"==typeof S&&(S=`'${S}'`),S&&"object"==typeof S&&(S="Object"),`${b}: ${S}`.slice(0,50))}let Nr=(()=>{class b extends _e{constructor(){super(...arguments),this._optionMap=new Map,this._idCounter=0,this._compareWith=Object.is}set compareWith(v){this._compareWith=v}writeValue(v){let P;if(this.value=v,Array.isArray(v)){const B=v.map(ze=>this._getOptionId(ze));P=(ze,Ct)=>{ze._setSelected(B.indexOf(Ct.toString())>-1)}}else P=(B,ze)=>{B._setSelected(!1)};this._optionMap.forEach(P)}registerOnChange(v){this.onChange=P=>{const B=[],ze=P.selectedOptions;if(void 0!==ze){const Ct=ze;for(let er=0;er<Ct.length;er++){const Ci=this._getOptionValue(Ct[er].value);B.push(Ci)}}else{const Ct=P.options;for(let er=0;er<Ct.length;er++){const Vi=Ct[er];if(Vi.selected){const Ci=this._getOptionValue(Vi.value);B.push(Ci)}}}this.value=B,v(B)}}_registerOption(v){const P=(this._idCounter++).toString();return this._optionMap.set(P,v),P}_getOptionId(v){for(const P of this._optionMap.keys())if(this._compareWith(this._optionMap.get(P)._value,v))return P;return null}_getOptionValue(v){const P=function Dn(b){return b.split(":")[0]}(v);return this._optionMap.has(P)?this._optionMap.get(P)._value:v}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(P,B){1&P&&c.bIt("change",function(Ct){return B.onChange(Ct.target)})("blur",function(){return B.onTouched()})},inputs:{compareWith:"compareWith"},features:[c.Jv_([On]),c.Vt3]})}}return b})(),mt=(()=>{class b{constructor(v,P,B){this._element=v,this._renderer=P,this._select=B,this._select&&(this.id=this._select._registerOption(this))}set ngValue(v){null!=this._select&&(this._value=v,this._setElementValue(Br(this.id,v)),this._select.writeValue(this._select.value))}set value(v){this._select?(this._value=v,this._setElementValue(Br(this.id,v)),this._select.writeValue(this._select.value)):this._setElementValue(v)}_setElementValue(v){this._renderer.setProperty(this._element.nativeElement,"value",v)}_setSelected(v){this._renderer.setProperty(this._element.nativeElement,"selected",v)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static{this.\u0275fac=function(P){return new(P||b)(c.rXU(c.aKT),c.rXU(c.sFG),c.rXU(Nr,9))}}static{this.\u0275dir=c.FsC({type:b,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"}})}}return b})();function lo(b){return"number"==typeof b?b:parseFloat(b)}let Dr=(()=>{class b{constructor(){this._validator=Sn}ngOnChanges(v){if(this.inputName in v){const P=this.normalizeInput(v[this.inputName].currentValue);this._enabled=this.enabled(P),this._validator=this._enabled?this.createValidator(P):Sn,this._onChange&&this._onChange()}}validate(v){return this._validator(v)}registerOnValidatorChange(v){this._onChange=v}enabled(v){return null!=v}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275dir=c.FsC({type:b,features:[c.OA$]})}}return b})();const Yt={provide:we,useExisting:(0,c.Rfq)(()=>oi),multi:!0};let oi=(()=>{class b extends Dr{constructor(){super(...arguments),this.inputName="max",this.normalizeInput=v=>lo(v),this.createValidator=v=>Pe(v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(P,B){2&P&&c.BMQ("max",B._enabled?B.max:null)},inputs:{max:"max"},features:[c.Jv_([Yt]),c.Vt3]})}}return b})();const qr={provide:we,useExisting:(0,c.Rfq)(()=>hr),multi:!0};let hr=(()=>{class b extends Dr{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=v=>lo(v),this.createValidator=v=>rt(v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(P,B){2&P&&c.BMQ("min",B._enabled?B.min:null)},inputs:{min:"min"},features:[c.Jv_([qr]),c.Vt3]})}}return b})();const go={provide:we,useExisting:(0,c.Rfq)(()=>si),multi:!0};let si=(()=>{class b extends Dr{constructor(){super(...arguments),this.inputName="maxlength",this.normalizeInput=v=>function vr(b){return"number"==typeof b?b:parseInt(b,10)}(v),this.createValidator=v=>jt(v)}static{this.\u0275fac=function(){let v;return function(B){return(v||(v=c.xGo(b)))(B||b)}}()}static{this.\u0275dir=c.FsC({type:b,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(P,B){2&P&&c.BMQ("maxlength",B._enabled?B.maxlength:null)},inputs:{maxlength:"maxlength"},features:[c.Jv_([go]),c.Vt3]})}}return b})(),ki=(()=>{class b{static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({imports:[yi]})}}return b})();class Yn extends We{constructor(S,v,P){super(Lr(v),q(P,v)),this.controls=S,this._initObservables(),this._setUpdateStrategy(v),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(S){return this.controls[this._adjustIndex(S)]}push(S,v={}){this.controls.push(S),this._registerControl(S),this.updateValueAndValidity({emitEvent:v.emitEvent}),this._onCollectionChange()}insert(S,v,P={}){this.controls.splice(S,0,v),this._registerControl(v),this.updateValueAndValidity({emitEvent:P.emitEvent})}removeAt(S,v={}){let P=this._adjustIndex(S);P<0&&(P=0),this.controls[P]&&this.controls[P]._registerOnCollectionChange(()=>{}),this.controls.splice(P,1),this.updateValueAndValidity({emitEvent:v.emitEvent})}setControl(S,v,P={}){let B=this._adjustIndex(S);B<0&&(B=0),this.controls[B]&&this.controls[B]._registerOnCollectionChange(()=>{}),this.controls.splice(B,1),v&&(this.controls.splice(B,0,v),this._registerControl(v)),this.updateValueAndValidity({emitEvent:P.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(S,v={}){Fe(this,0,S),S.forEach((P,B)=>{pe(this,!1,B),this.at(B).setValue(P,{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v)}patchValue(S,v={}){null!=S&&(S.forEach((P,B)=>{this.at(B)&&this.at(B).patchValue(P,{onlySelf:!0,emitEvent:v.emitEvent})}),this.updateValueAndValidity(v))}reset(S=[],v={}){this._forEachChild((P,B)=>{P.reset(S[B],{onlySelf:!0,emitEvent:v.emitEvent})}),this._updatePristine(v),this._updateTouched(v),this.updateValueAndValidity(v)}getRawValue(){return this.controls.map(S=>S.getRawValue())}clear(S={}){this.controls.length<1||(this._forEachChild(v=>v._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:S.emitEvent}))}_adjustIndex(S){return S<0?S+this.length:S}_syncPendingControls(){let S=this.controls.reduce((v,P)=>!!P._syncPendingControls()||v,!1);return S&&this.updateValueAndValidity({onlySelf:!0}),S}_forEachChild(S){this.controls.forEach((v,P)=>{S(v,P)})}_updateValue(){this.value=this.controls.filter(S=>S.enabled||this.disabled).map(S=>S.value)}_anyControls(S){return this.controls.some(v=>v.enabled&&S(v))}_setUpControls(){this._forEachChild(S=>this._registerControl(S))}_allControlsDisabled(){for(const S of this.controls)if(S.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(S){S.setParent(this),S._registerOnCollectionChange(this._onCollectionChange)}_find(S){return this.at(S)??null}}function Cr(b){return!!b&&(void 0!==b.asyncValidators||void 0!==b.validators||void 0!==b.updateOn)}let Li=(()=>{class b{constructor(){this.useNonNullable=!1}get nonNullable(){const v=new b;return v.useNonNullable=!0,v}group(v,P=null){const B=this._reduceControls(v);let ze={};return Cr(P)?ze=P:null!==P&&(ze.validators=P.validator,ze.asyncValidators=P.asyncValidator),new At(B,ze)}record(v,P=null){const B=this._reduceControls(v);return new Bn(B,P)}control(v,P,B){let ze={};return this.useNonNullable?(Cr(P)?ze=P:(ze.validators=P,ze.asyncValidators=B),new Ar(v,{...ze,nonNullable:!0})):new Ar(v,P,B)}array(v,P,B){const ze=v.map(Ct=>this._createControl(Ct));return new Yn(ze,P,B)}_reduceControls(v){const P={};return Object.keys(v).forEach(B=>{P[B]=this._createControl(v[B])}),P}_createControl(v){return v instanceof Ar||v instanceof We?v:Array.isArray(v)?this.control(v[0],v.length>1?v[1]:null,v.length>2?v[2]:null):this.control(v)}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275prov=c.jDH({token:b,factory:b.\u0275fac,providedIn:"root"})}}return b})(),mo=(()=>{class b{static withConfig(v){return{ngModule:b,providers:[{provide:Q,useValue:v.callSetDisabledState??j}]}}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({imports:[ki]})}}return b})(),es=(()=>{class b{static withConfig(v){return{ngModule:b,providers:[{provide:Kr,useValue:v.warnOnNgModelWithFormControl??"always"},{provide:Q,useValue:v.callSetDisabledState??j}]}}static{this.\u0275fac=function(P){return new(P||b)}}static{this.\u0275mod=c.$C({type:b})}static{this.\u0275inj=c.G2t({imports:[ki]})}}return b})()},345:(qe,ye,O)=>{O.d(ye,{B7:()=>Te,Bb:()=>Ae,hE:()=>Un,sG:()=>ut});var c=O(6276),C=O(177);class ce extends C.VF{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class se extends ce{static makeCurrent(){(0,C.ZD)(new se)}onAndCancel(q,V,W){return q.addEventListener(V,W),()=>{q.removeEventListener(V,W)}}dispatchEvent(q,V){q.dispatchEvent(V)}remove(q){q.parentNode&&q.parentNode.removeChild(q)}createElement(q,V){return(V=V||this.getDefaultDocument()).createElement(q)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(q){return q.nodeType===Node.ELEMENT_NODE}isShadowRoot(q){return q instanceof DocumentFragment}getGlobalEventTarget(q,V){return"window"===V?window:"document"===V?q:"body"===V?q.body:null}getBaseHref(q){const V=function ne(){return X=X||document.querySelector("base"),X?X.getAttribute("href"):null}();return null==V?null:function de(he){ee=ee||document.createElement("a"),ee.setAttribute("href",he);const q=ee.pathname;return"/"===q.charAt(0)?q:`/${q}`}(V)}resetBaseElement(){X=null}getUserAgent(){return window.navigator.userAgent}getCookie(q){return(0,C._b)(document.cookie,q)}}let ee,X=null,fe=(()=>{class he{build(){return new XMLHttpRequest}static{this.\u0275fac=function(W){return new(W||he)}}static{this.\u0275prov=c.jDH({token:he,factory:he.\u0275fac})}}return he})();const H=new c.nKC("EventManagerPlugins");let G=(()=>{class he{constructor(V,W){this._zone=W,this._eventNameToPlugin=new Map,V.forEach(pe=>{pe.manager=this}),this._plugins=V.slice().reverse()}addEventListener(V,W,pe){return this._findPluginFor(W).addEventListener(V,W,pe)}getZone(){return this._zone}_findPluginFor(V){let W=this._eventNameToPlugin.get(V);if(W)return W;if(W=this._plugins.find(Fe=>Fe.supports(V)),!W)throw new c.wOt(5101,!1);return this._eventNameToPlugin.set(V,W),W}static{this.\u0275fac=function(W){return new(W||he)(c.KVO(H),c.KVO(c.SKi))}}static{this.\u0275prov=c.jDH({token:he,factory:he.\u0275fac})}}return he})();class ve{constructor(q){this._doc=q}}const _e="ng-app-id";let Ve=(()=>{class he{constructor(V,W,pe,Fe={}){this.doc=V,this.appId=W,this.nonce=pe,this.platformId=Fe,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=(0,C.Vy)(Fe),this.resetHostNodes()}addStyles(V){for(const W of V)1===this.changeUsageCount(W,1)&&this.onStyleAdded(W)}removeStyles(V){for(const W of V)this.changeUsageCount(W,-1)<=0&&this.onStyleRemoved(W)}ngOnDestroy(){const V=this.styleNodesInDOM;V&&(V.forEach(W=>W.remove()),V.clear());for(const W of this.getAllStyles())this.onStyleRemoved(W);this.resetHostNodes()}addHost(V){this.hostNodes.add(V);for(const W of this.getAllStyles())this.addStyleToHost(V,W)}removeHost(V){this.hostNodes.delete(V)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(V){for(const W of this.hostNodes)this.addStyleToHost(W,V)}onStyleRemoved(V){const W=this.styleRef;W.get(V)?.elements?.forEach(pe=>pe.remove()),W.delete(V)}collectServerRenderedStyles(){const V=this.doc.head?.querySelectorAll(`style[${_e}="${this.appId}"]`);if(V?.length){const W=new Map;return V.forEach(pe=>{null!=pe.textContent&&W.set(pe.textContent,pe)}),W}return null}changeUsageCount(V,W){const pe=this.styleRef;if(pe.has(V)){const Fe=pe.get(V);return Fe.usage+=W,Fe.usage}return pe.set(V,{usage:W,elements:[]}),W}getStyleElement(V,W){const pe=this.styleNodesInDOM,Fe=pe?.get(W);if(Fe?.parentNode===V)return pe.delete(W),Fe.removeAttribute(_e),Fe;{const We=this.doc.createElement("style");return this.nonce&&We.setAttribute("nonce",this.nonce),We.textContent=W,this.platformIsServer&&We.setAttribute(_e,this.appId),We}}addStyleToHost(V,W){const pe=this.getStyleElement(V,W);V.appendChild(pe);const Fe=this.styleRef,We=Fe.get(W)?.elements;We?We.push(pe):Fe.set(W,{elements:[pe],usage:1})}resetHostNodes(){const V=this.hostNodes;V.clear(),V.add(this.doc.head)}static{this.\u0275fac=function(W){return new(W||he)(c.KVO(C.qQ),c.KVO(c.sZ2),c.KVO(c.BIS,8),c.KVO(c.Agw))}}static{this.\u0275prov=c.jDH({token:he,factory:he.\u0275fac})}}return he})();const Vt={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},bt=/%COMP%/g,Ne=new c.nKC("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!1});function ke(he,q){return q.map(V=>V.replace(bt,he))}let Te=(()=>{class he{constructor(V,W,pe,Fe,We,At,$t,ln=null){this.eventManager=V,this.sharedStylesHost=W,this.appId=pe,this.removeStylesOnCompDestroy=Fe,this.doc=We,this.platformId=At,this.ngZone=$t,this.nonce=ln,this.rendererByCompId=new Map,this.platformIsServer=(0,C.Vy)(At),this.defaultRenderer=new ot(V,We,$t,this.platformIsServer)}createRenderer(V,W){if(!V||!W)return this.defaultRenderer;this.platformIsServer&&W.encapsulation===c.gXe.ShadowDom&&(W={...W,encapsulation:c.gXe.Emulated});const pe=this.getOrCreateRenderer(V,W);return pe instanceof dn?pe.applyToHost(V):pe instanceof gt&&pe.applyStyles(),pe}getOrCreateRenderer(V,W){const pe=this.rendererByCompId;let Fe=pe.get(W.id);if(!Fe){const We=this.doc,At=this.ngZone,$t=this.eventManager,ln=this.sharedStylesHost,Bn=this.removeStylesOnCompDestroy,kn=this.platformIsServer;switch(W.encapsulation){case c.gXe.Emulated:Fe=new dn($t,ln,W,this.appId,Bn,We,At,kn);break;case c.gXe.ShadowDom:return new et($t,ln,V,W,We,At,this.nonce,kn);default:Fe=new gt($t,ln,W,Bn,We,At,kn)}pe.set(W.id,Fe)}return Fe}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(W){return new(W||he)(c.KVO(G),c.KVO(Ve),c.KVO(c.sZ2),c.KVO(Ne),c.KVO(C.qQ),c.KVO(c.Agw),c.KVO(c.SKi),c.KVO(c.BIS))}}static{this.\u0275prov=c.jDH({token:he,factory:he.\u0275fac})}}return he})();class ot{constructor(q,V,W,pe){this.eventManager=q,this.doc=V,this.ngZone=W,this.platformIsServer=pe,this.data=Object.create(null),this.destroyNode=null}destroy(){}createElement(q,V){return V?this.doc.createElementNS(Vt[V]||V,q):this.doc.createElement(q)}createComment(q){return this.doc.createComment(q)}createText(q){return this.doc.createTextNode(q)}appendChild(q,V){(cn(q)?q.content:q).appendChild(V)}insertBefore(q,V,W){q&&(cn(q)?q.content:q).insertBefore(V,W)}removeChild(q,V){q&&q.removeChild(V)}selectRootElement(q,V){let W="string"==typeof q?this.doc.querySelector(q):q;if(!W)throw new c.wOt(-5104,!1);return V||(W.textContent=""),W}parentNode(q){return q.parentNode}nextSibling(q){return q.nextSibling}setAttribute(q,V,W,pe){if(pe){V=pe+":"+V;const Fe=Vt[pe];Fe?q.setAttributeNS(Fe,V,W):q.setAttribute(V,W)}else q.setAttribute(V,W)}removeAttribute(q,V,W){if(W){const pe=Vt[W];pe?q.removeAttributeNS(pe,V):q.removeAttribute(`${W}:${V}`)}else q.removeAttribute(V)}addClass(q,V){q.classList.add(V)}removeClass(q,V){q.classList.remove(V)}setStyle(q,V,W,pe){pe&(c.czy.DashCase|c.czy.Important)?q.style.setProperty(V,W,pe&c.czy.Important?"important":""):q.style[V]=W}removeStyle(q,V,W){W&c.czy.DashCase?q.style.removeProperty(V):q.style[V]=""}setProperty(q,V,W){q[V]=W}setValue(q,V){q.nodeValue=V}listen(q,V,W){if("string"==typeof q&&!(q=(0,C.QT)().getGlobalEventTarget(this.doc,q)))throw new Error(`Unsupported event target ${q} for event ${V}`);return this.eventManager.addEventListener(q,V,this.decoratePreventDefault(W))}decoratePreventDefault(q){return V=>{if("__ngUnwrap__"===V)return q;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>q(V)):q(V))&&V.preventDefault()}}}function cn(he){return"TEMPLATE"===he.tagName&&void 0!==he.content}class et extends ot{constructor(q,V,W,pe,Fe,We,At,$t){super(q,Fe,We,$t),this.sharedStylesHost=V,this.hostEl=W,this.shadowRoot=W.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const ln=ke(pe.id,pe.styles);for(const Bn of ln){const kn=document.createElement("style");At&&kn.setAttribute("nonce",At),kn.textContent=Bn,this.shadowRoot.appendChild(kn)}}nodeOrShadowRoot(q){return q===this.hostEl?this.shadowRoot:q}appendChild(q,V){return super.appendChild(this.nodeOrShadowRoot(q),V)}insertBefore(q,V,W){return super.insertBefore(this.nodeOrShadowRoot(q),V,W)}removeChild(q,V){return super.removeChild(this.nodeOrShadowRoot(q),V)}parentNode(q){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(q)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class gt extends ot{constructor(q,V,W,pe,Fe,We,At,$t){super(q,Fe,We,At),this.sharedStylesHost=V,this.removeStylesOnCompDestroy=pe,this.styles=$t?ke($t,W.styles):W.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class dn extends gt{constructor(q,V,W,pe,Fe,We,At,$t){const ln=pe+"-"+W.id;super(q,V,W,Fe,We,At,$t,ln),this.contentAttr=function Ke(he){return"_ngcontent-%COMP%".replace(bt,he)}(ln),this.hostAttr=function we(he){return"_nghost-%COMP%".replace(bt,he)}(ln)}applyToHost(q){this.applyStyles(),this.setAttribute(q,this.hostAttr,"")}createElement(q,V){const W=super.createElement(q,V);return super.setAttribute(W,this.contentAttr,""),W}}let jt=(()=>{class he extends ve{constructor(V){super(V)}supports(V){return!0}addEventListener(V,W,pe){return V.addEventListener(W,pe,!1),()=>this.removeEventListener(V,W,pe)}removeEventListener(V,W,pe){return V.removeEventListener(W,pe)}static{this.\u0275fac=function(W){return new(W||he)(c.KVO(C.qQ))}}static{this.\u0275prov=c.jDH({token:he,factory:he.\u0275fac})}}return he})();const Mn=["alt","control","meta","shift"],Sn={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},fn={alt:he=>he.altKey,control:he=>he.ctrlKey,meta:he=>he.metaKey,shift:he=>he.shiftKey};let Z=(()=>{class he extends ve{constructor(V){super(V)}supports(V){return null!=he.parseEventName(V)}addEventListener(V,W,pe){const Fe=he.parseEventName(W),We=he.eventCallback(Fe.fullKey,pe,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>(0,C.QT)().onAndCancel(V,Fe.domEventName,We))}static parseEventName(V){const W=V.toLowerCase().split("."),pe=W.shift();if(0===W.length||"keydown"!==pe&&"keyup"!==pe)return null;const Fe=he._normalizeKey(W.pop());let We="",At=W.indexOf("code");if(At>-1&&(W.splice(At,1),We="code."),Mn.forEach(ln=>{const Bn=W.indexOf(ln);Bn>-1&&(W.splice(Bn,1),We+=ln+".")}),We+=Fe,0!=W.length||0===Fe.length)return null;const $t={};return $t.domEventName=pe,$t.fullKey=We,$t}static matchEventFullKeyCode(V,W){let pe=Sn[V.key]||V.key,Fe="";return W.indexOf("code.")>-1&&(pe=V.code,Fe="code."),!(null==pe||!pe)&&(pe=pe.toLowerCase()," "===pe?pe="space":"."===pe&&(pe="dot"),Mn.forEach(We=>{We!==pe&&(0,fn[We])(V)&&(Fe+=We+".")}),Fe+=pe,Fe===W)}static eventCallback(V,W,pe){return Fe=>{he.matchEventFullKeyCode(Fe,V)&&pe.runGuarded(()=>W(Fe))}}static _normalizeKey(V){return"esc"===V?"escape":V}static{this.\u0275fac=function(W){return new(W||he)(c.KVO(C.qQ))}}static{this.\u0275prov=c.jDH({token:he,factory:he.\u0275fac})}}return he})();const ut=(0,c.oH4)(c.fpN,"browser",[{provide:c.Agw,useValue:C.AJ},{provide:c.PLl,useValue:function be(){se.makeCurrent()},multi:!0},{provide:C.qQ,useFactory:function ft(){return(0,c.TL$)(document),document},deps:[]}]),xt=new c.nKC(""),Qt=[{provide:c.e01,useClass:class le{addToWindow(q){c.JZv.getAngularTestability=(W,pe=!0)=>{const Fe=q.findTestabilityInTree(W,pe);if(null==Fe)throw new c.wOt(5103,!1);return Fe},c.JZv.getAllAngularTestabilities=()=>q.getAllTestabilities(),c.JZv.getAllAngularRootElements=()=>q.getAllRootElements(),c.JZv.frameworkStabilizers||(c.JZv.frameworkStabilizers=[]),c.JZv.frameworkStabilizers.push(W=>{const pe=c.JZv.getAllAngularTestabilities();let Fe=pe.length,We=!1;const At=function($t){We=We||$t,Fe--,0==Fe&&W(We)};pe.forEach($t=>{$t.whenStable(At)})})}findTestabilityInTree(q,V,W){return null==V?null:q.getTestability(V)??(W?(0,C.QT)().isShadowRoot(V)?this.findTestabilityInTree(q,V.host,!0):this.findTestabilityInTree(q,V.parentElement,!0):null)}},deps:[]},{provide:c.WHO,useClass:c.NYb,deps:[c.SKi,c.giA,c.e01]},{provide:c.NYb,useClass:c.NYb,deps:[c.SKi,c.giA,c.e01]}],tn=[{provide:c.H8p,useValue:"root"},{provide:c.zcH,useFactory:function Ge(){return new c.zcH},deps:[]},{provide:H,useClass:jt,multi:!0,deps:[C.qQ,c.SKi,c.Agw]},{provide:H,useClass:Z,multi:!0,deps:[C.qQ]},Te,Ve,G,{provide:c._9s,useExisting:Te},{provide:C.N0,useClass:fe,deps:[]},[]];let Ae=(()=>{class he{constructor(V){}static withServerTransition(V){return{ngModule:he,providers:[{provide:c.sZ2,useValue:V.appId}]}}static{this.\u0275fac=function(W){return new(W||he)(c.KVO(xt,12))}}static{this.\u0275mod=c.$C({type:he})}static{this.\u0275inj=c.G2t({providers:[...tn,...Qt],imports:[C.MD,c.Hbi]})}}return he})(),Un=(()=>{class he{constructor(V){this._doc=V}getTitle(){return this._doc.title}setTitle(V){this._doc.title=V||""}static{this.\u0275fac=function(W){return new(W||he)(c.KVO(C.qQ))}}static{this.\u0275prov=c.jDH({token:he,factory:function(W){let pe=null;return pe=W?new W:function Re(){return new Un((0,c.KVO)(C.qQ))}(),pe},providedIn:"root"})}}return he})();typeof window<"u"&&window},2434:(qe,ye,O)=>{O.d(ye,{nX:()=>Ur,wF:()=>jr,Ix:()=>Gt,Wk:()=>Wn,wQ:()=>Er,iI:()=>ds,n3:()=>Br});var c=O(6276),C=O(1985),ce=O(8071),X=O(6648),ne=O(7673),ee=O(4412),de=O(3073),le=O(3669),fe=O(6450),H=O(9326),G=O(8496),ve=O(4360),_e=O(5225);function Ve(...f){const g=(0,H.lI)(f),u=(0,H.ms)(f),{args:m,keys:w}=(0,de.D)(f);if(0===m.length)return(0,X.H)([],g);const N=new C.c(function Vt(f,g,u=le.D){return m=>{bt(g,()=>{const{length:w}=f,N=new Array(w);let F=w,re=w;for(let Y=0;Y<w;Y++)bt(g,()=>{const Be=(0,X.H)(f[Y],g);let Et=!1;Be.subscribe((0,ve._)(m,un=>{N[Y]=un,Et||(Et=!0,re--),re||m.next(u(N.slice()))},()=>{--F||m.complete()}))},m)},m)}}(m,g,w?F=>(0,G.e)(w,F):le.D));return u?N.pipe((0,fe.I)(u)):N}function bt(f,g,u){f?(0,_e.N)(u,f,g):g()}const Rt=(0,O(1853).L)(f=>function(){f(this),this.name="EmptyError",this.message="no elements in sequence"});var yt=O(6365);function Ne(...f){return function xe(){return(0,yt.U)(1)}()((0,X.H)(f,(0,H.lI)(f)))}var Ke=O(8750);function we(f){return new C.c(g=>{(0,Ke.Tg)(f()).subscribe(g)})}var ke=O(1203),Te=O(8810),ot=O(983),rt=O(8359),Pe=O(9974);function cn(){return(0,Pe.N)((f,g)=>{let u=null;f._refCount++;const m=(0,ve._)(g,void 0,void 0,void 0,()=>{if(!f||f._refCount<=0||0<--f._refCount)return void(u=null);const w=f._connection,N=u;u=null,w&&(!N||w===N)&&w.unsubscribe(),g.unsubscribe()});f.subscribe(m),m.closed||(u=f.connect())})}class et extends C.c{constructor(g,u){super(),this.source=g,this.subjectFactory=u,this._subject=null,this._refCount=0,this._connection=null,(0,Pe.S)(g)&&(this.lift=g.lift)}_subscribe(g){return this.getSubject().subscribe(g)}getSubject(){const g=this._subject;return(!g||g.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;const{_connection:g}=this;this._subject=this._connection=null,g?.unsubscribe()}connect(){let g=this._connection;if(!g){g=this._connection=new rt.yU;const u=this.getSubject();g.add(this.source.subscribe((0,ve._)(u,void 0,()=>{this._teardown(),u.complete()},m=>{this._teardown(),u.error(m)},()=>this._teardown()))),g.closed&&(this._connection=null,g=rt.yU.EMPTY)}return g}refCount(){return cn()(this)}}var gt=O(3794),dn=O(177),jt=O(6354),Mn=O(5558);function Sn(f){return f<=0?()=>ot.w:(0,Pe.N)((g,u)=>{let m=0;g.subscribe((0,ve._)(u,w=>{++m<=f&&(u.next(w),f<=m&&u.complete())}))})}var Z=O(5964),te=O(1397);function ie(f){return(0,Pe.N)((g,u)=>{let m=!1;g.subscribe((0,ve._)(u,w=>{m=!0,u.next(w)},()=>{m||u.next(f),u.complete()}))})}function ae(f=Ie){return(0,Pe.N)((g,u)=>{let m=!1;g.subscribe((0,ve._)(u,w=>{m=!0,u.next(w)},()=>m?u.complete():u.error(f())))})}function Ie(){return new Rt}function be(f,g){const u=arguments.length>=2;return m=>m.pipe(f?(0,Z.p)((w,N)=>f(w,N,m)):le.D,Sn(1),u?ie(g):ae(()=>new Rt))}var Ge=O(274),ft=O(8141),Bt=O(9437);function Qt(f){return f<=0?()=>ot.w:(0,Pe.N)((g,u)=>{let m=[];g.subscribe((0,ve._)(u,w=>{m.push(w),f<m.length&&m.shift()},()=>{for(const w of m)u.next(w);u.complete()},void 0,()=>{m=null}))})}var hn=O(980),xn=O(6977),tr=O(345);const Re="primary",Un=Symbol("RouteTitle");class lr{constructor(g){this.params=g||{}}has(g){return Object.prototype.hasOwnProperty.call(this.params,g)}get(g){if(this.has(g)){const u=this.params[g];return Array.isArray(u)?u[0]:u}return null}getAll(g){if(this.has(g)){const u=this.params[g];return Array.isArray(u)?u:[u]}return[]}get keys(){return Object.keys(this.params)}}function _n(f){return new lr(f)}function ur(f,g,u){const m=u.path.split("/");if(m.length>f.length||"full"===u.pathMatch&&(g.hasChildren()||m.length<f.length))return null;const w={};for(let N=0;N<m.length;N++){const F=m[N],re=f[N];if(F.startsWith(":"))w[F.substring(1)]=re;else if(F!==re.path)return null}return{consumed:f.slice(0,m.length),posParams:w}}function ht(f,g){const u=f?Object.keys(f):void 0,m=g?Object.keys(g):void 0;if(!u||!m||u.length!=m.length)return!1;let w;for(let N=0;N<u.length;N++)if(w=u[N],!nr(f[w],g[w]))return!1;return!0}function nr(f,g){if(Array.isArray(f)&&Array.isArray(g)){if(f.length!==g.length)return!1;const u=[...f].sort(),m=[...g].sort();return u.every((w,N)=>m[N]===w)}return f===g}function Fn(f){return f.length>0?f[f.length-1]:null}function sn(f){return function se(f){return!!f&&(f instanceof C.c||(0,ce.T)(f.lift)&&(0,ce.T)(f.subscribe))}(f)?f:(0,c.jNT)(f)?(0,X.H)(Promise.resolve(f)):(0,ne.of)(f)}const an={exact:function Oi(f,g,u){if(!Kn(f.segments,g.segments)||!nn(f.segments,g.segments,u)||f.numberOfChildren!==g.numberOfChildren)return!1;for(const m in g.children)if(!f.children[m]||!Oi(f.children[m],g.children[m],u))return!1;return!0},subset:ti},En={exact:function Xt(f,g){return ht(f,g)},subset:function pt(f,g){return Object.keys(g).length<=Object.keys(f).length&&Object.keys(g).every(u=>nr(f[u],g[u]))},ignored:()=>!0};function Mt(f,g,u){return an[u.paths](f.root,g.root,u.matrixParams)&&En[u.queryParams](f.queryParams,g.queryParams)&&!("exact"===u.fragment&&f.fragment!==g.fragment)}function ti(f,g,u){return hi(f,g,g.segments,u)}function hi(f,g,u,m){if(f.segments.length>u.length){const w=f.segments.slice(0,u.length);return!(!Kn(w,u)||g.hasChildren()||!nn(w,u,m))}if(f.segments.length===u.length){if(!Kn(f.segments,u)||!nn(f.segments,u,m))return!1;for(const w in g.children)if(!f.children[w]||!ti(f.children[w],g.children[w],m))return!1;return!0}{const w=u.slice(0,f.segments.length),N=u.slice(f.segments.length);return!!(Kn(f.segments,w)&&nn(f.segments,w,m)&&f.children[Re])&&hi(f.children[Re],g,N,m)}}function nn(f,g,u){return g.every((m,w)=>En[u](f[w].parameters,m.parameters))}class Ir{constructor(g=new _t([],{}),u={},m=null){this.root=g,this.queryParams=u,this.fragment=m}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=_n(this.queryParams)),this._queryParamMap}toString(){return kr.serialize(this)}}class _t{constructor(g,u){this.segments=g,this.children=u,this.parent=null,Object.values(u).forEach(m=>m.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Lr(this)}}class dt{constructor(g,u){this.path=g,this.parameters=u}get parameterMap(){return this._parameterMap||(this._parameterMap=_n(this.parameters)),this._parameterMap}toString(){return At(this)}}function Kn(f,g){return f.length===g.length&&f.every((u,m)=>u.path===g[m].path)}let rr=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:function(){return new cr},providedIn:"root"})}}return f})();class cr{parse(g){const u=new Le(g);return new Ir(u.parseRootSegment(),u.parseQueryParams(),u.parseFragment())}serialize(g){const u=`/${he(g.root,!0)}`,m=function ln(f){const g=Object.keys(f).map(u=>{const m=f[u];return Array.isArray(m)?m.map(w=>`${V(u)}=${V(w)}`).join("&"):`${V(u)}=${V(m)}`}).filter(u=>!!u);return g.length?`?${g.join("&")}`:""}(g.queryParams);return`${u}${m}${"string"==typeof g.fragment?`#${function W(f){return encodeURI(f)}(g.fragment)}`:""}`}}const kr=new cr;function Lr(f){return f.segments.map(g=>At(g)).join("/")}function he(f,g){if(!f.hasChildren())return Lr(f);if(g){const u=f.children[Re]?he(f.children[Re],!1):"",m=[];return Object.entries(f.children).forEach(([w,N])=>{w!==Re&&m.push(`${w}:${he(N,!1)}`)}),m.length>0?`${u}(${m.join("//")})`:u}{const u=function Xn(f,g){let u=[];return Object.entries(f.children).forEach(([m,w])=>{m===Re&&(u=u.concat(g(w,m)))}),Object.entries(f.children).forEach(([m,w])=>{m!==Re&&(u=u.concat(g(w,m)))}),u}(f,(m,w)=>w===Re?[he(f.children[Re],!1)]:[`${w}:${he(m,!1)}`]);return 1===Object.keys(f.children).length&&null!=f.children[Re]?`${Lr(f)}/${u[0]}`:`${Lr(f)}/(${u.join("//")})`}}function q(f){return encodeURIComponent(f).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function V(f){return q(f).replace(/%3B/gi,";")}function pe(f){return q(f).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Fe(f){return decodeURIComponent(f)}function We(f){return Fe(f.replace(/\+/g,"%20"))}function At(f){return`${pe(f.path)}${function $t(f){return Object.keys(f).map(g=>`;${pe(g)}=${pe(f[g])}`).join("")}(f.parameters)}`}const Bn=/^[^\/()?;#]+/;function kn(f){const g=f.match(Bn);return g?g[0]:""}const Q=/^[^\/()?;=#]+/,$=/^[^=?&#]+/,Oe=/^[^&#]+/;class Le{constructor(g){this.url=g,this.remaining=g}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new _t([],{}):new _t([],this.parseChildren())}parseQueryParams(){const g={};if(this.consumeOptional("?"))do{this.parseQueryParam(g)}while(this.consumeOptional("&"));return g}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const g=[];for(this.peekStartsWith("(")||g.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),g.push(this.parseSegment());let u={};this.peekStartsWith("/(")&&(this.capture("/"),u=this.parseParens(!0));let m={};return this.peekStartsWith("(")&&(m=this.parseParens(!1)),(g.length>0||Object.keys(u).length>0)&&(m[Re]=new _t(g,u)),m}parseSegment(){const g=kn(this.remaining);if(""===g&&this.peekStartsWith(";"))throw new c.wOt(4009,!1);return this.capture(g),new dt(Fe(g),this.parseMatrixParams())}parseMatrixParams(){const g={};for(;this.consumeOptional(";");)this.parseParam(g);return g}parseParam(g){const u=function j(f){const g=f.match(Q);return g?g[0]:""}(this.remaining);if(!u)return;this.capture(u);let m="";if(this.consumeOptional("=")){const w=kn(this.remaining);w&&(m=w,this.capture(m))}g[Fe(u)]=Fe(m)}parseQueryParam(g){const u=function ue(f){const g=f.match($);return g?g[0]:""}(this.remaining);if(!u)return;this.capture(u);let m="";if(this.consumeOptional("=")){const F=function Me(f){const g=f.match(Oe);return g?g[0]:""}(this.remaining);F&&(m=F,this.capture(m))}const w=We(u),N=We(m);if(g.hasOwnProperty(w)){let F=g[w];Array.isArray(F)||(F=[F],g[w]=F),F.push(N)}else g[w]=N}parseParens(g){const u={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const m=kn(this.remaining),w=this.remaining[m.length];if("/"!==w&&")"!==w&&";"!==w)throw new c.wOt(4010,!1);let N;m.indexOf(":")>-1?(N=m.slice(0,m.indexOf(":")),this.capture(N),this.capture(":")):g&&(N=Re);const F=this.parseChildren();u[N]=1===Object.keys(F).length?F[Re]:new _t([],F),this.consumeOptional("//")}return u}peekStartsWith(g){return this.remaining.startsWith(g)}consumeOptional(g){return!!this.peekStartsWith(g)&&(this.remaining=this.remaining.substring(g.length),!0)}capture(g){if(!this.consumeOptional(g))throw new c.wOt(4011,!1)}}function Ut(f){return f.segments.length>0?new _t([],{[Re]:f}):f}function tt(f){const g={};for(const m of Object.keys(f.children)){const N=tt(f.children[m]);if(m===Re&&0===N.segments.length&&N.hasChildren())for(const[F,re]of Object.entries(N.children))g[F]=re;else(N.segments.length>0||N.hasChildren())&&(g[m]=N)}return function vn(f){if(1===f.numberOfChildren&&f.children[Re]){const g=f.children[Re];return new _t(f.segments.concat(g.segments),g.children)}return f}(new _t(f.segments,g))}function $n(f){return f instanceof Ir}function pn(f){let g;const w=Ut(function u(N){const F={};for(const Y of N.children){const Be=u(Y);F[Y.outlet]=Be}const re=new _t(N.url,F);return N===f&&(g=re),re}(f.root));return g??w}function Tr(f,g,u,m){let w=f;for(;w.parent;)w=w.parent;if(0===g.length)return Je(w,w,w,u,m);const N=function Tn(f){if("string"==typeof f[0]&&1===f.length&&"/"===f[0])return new ni(!0,0,f);let g=0,u=!1;const m=f.reduce((w,N,F)=>{if("object"==typeof N&&null!=N){if(N.outlets){const re={};return Object.entries(N.outlets).forEach(([Y,Be])=>{re[Y]="string"==typeof Be?Be.split("/"):Be}),[...w,{outlets:re}]}if(N.segmentPath)return[...w,N.segmentPath]}return"string"!=typeof N?[...w,N]:0===F?(N.split("/").forEach((re,Y)=>{0==Y&&"."===re||(0==Y&&""===re?u=!0:".."===re?g++:""!=re&&w.push(re))}),w):[...w,N]},[]);return new ni(u,g,m)}(g);if(N.toRoot())return Je(w,w,new _t([],{}),u,m);const F=function fr(f,g,u){if(f.isAbsolute)return new dr(g,!0,0);if(!u)return new dr(g,!1,NaN);if(null===u.parent)return new dr(u,!0,0);const m=wn(f.commands[0])?0:1;return function zr(f,g,u){let m=f,w=g,N=u;for(;N>w;){if(N-=w,m=m.parent,!m)throw new c.wOt(4005,!1);w=m.segments.length}return new dr(m,!1,w-N)}(u,u.segments.length-1+m,f.numberOfDoubleDots)}(N,w,f),re=F.processChildren?ri(F.segmentGroup,F.index,N.commands):Ki(F.segmentGroup,F.index,N.commands);return Je(w,F.segmentGroup,re,u,m)}function wn(f){return"object"==typeof f&&null!=f&&!f.outlets&&!f.segmentPath}function ct(f){return"object"==typeof f&&null!=f&&f.outlets}function Je(f,g,u,m,w){let F,N={};m&&Object.entries(m).forEach(([Y,Be])=>{N[Y]=Array.isArray(Be)?Be.map(Et=>`${Et}`):`${Be}`}),F=f===g?u:Qn(f,g,u);const re=Ut(tt(F));return new Ir(re,N,w)}function Qn(f,g,u){const m={};return Object.entries(f.children).forEach(([w,N])=>{m[w]=N===g?u:Qn(N,g,u)}),new _t(f.segments,m)}class ni{constructor(g,u,m){if(this.isAbsolute=g,this.numberOfDoubleDots=u,this.commands=m,g&&m.length>0&&wn(m[0]))throw new c.wOt(4003,!1);const w=m.find(ct);if(w&&w!==Fn(m))throw new c.wOt(4004,!1)}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class dr{constructor(g,u,m){this.segmentGroup=g,this.processChildren=u,this.index=m}}function Ki(f,g,u){if(f||(f=new _t([],{})),0===f.segments.length&&f.hasChildren())return ri(f,g,u);const m=function Xi(f,g,u){let m=0,w=g;const N={match:!1,pathIndex:0,commandIndex:0};for(;w<f.segments.length;){if(m>=u.length)return N;const F=f.segments[w],re=u[m];if(ct(re))break;const Y=`${re}`,Be=m<u.length-1?u[m+1]:null;if(w>0&&void 0===Y)break;if(Y&&Be&&"object"==typeof Be&&void 0===Be.outlets){if(!Qi(Y,Be,F))return N;m+=2}else{if(!Qi(Y,{},F))return N;m++}w++}return{match:!0,pathIndex:w,commandIndex:m}}(f,g,u),w=u.slice(m.commandIndex);if(m.match&&m.pathIndex<f.segments.length){const N=new _t(f.segments.slice(0,m.pathIndex),{});return N.children[Re]=new _t(f.segments.slice(m.pathIndex),f.children),ri(N,0,w)}return m.match&&0===w.length?new _t(f.segments,{}):m.match&&!f.hasChildren()?qn(f,g,u):m.match?ri(f,0,w):qn(f,g,u)}function ri(f,g,u){if(0===u.length)return new _t(f.segments,{});{const m=function Vr(f){return ct(f[0])?f[0].outlets:{[Re]:f}}(u),w={};if(Object.keys(m).some(N=>N!==Re)&&f.children[Re]&&1===f.numberOfChildren&&0===f.children[Re].segments.length){const N=ri(f.children[Re],g,u);return new _t(f.segments,N.children)}return Object.entries(m).forEach(([N,F])=>{"string"==typeof F&&(F=[F]),null!==F&&(w[N]=Ki(f.children[N],g,F))}),Object.entries(f.children).forEach(([N,F])=>{void 0===m[N]&&(w[N]=F)}),new _t(f.segments,w)}}function qn(f,g,u){const m=f.segments.slice(0,g);let w=0;for(;w<u.length;){const N=u[w];if(ct(N)){const Y=mr(N.outlets);return new _t(m,Y)}if(0===w&&wn(u[0])){m.push(new dt(f.segments[g].path,Ni(u[0]))),w++;continue}const F=ct(N)?N.outlets[Re]:`${N}`,re=w<u.length-1?u[w+1]:null;F&&re&&wn(re)?(m.push(new dt(F,Ni(re))),w+=2):(m.push(new dt(F,{})),w++)}return new _t(m,{})}function mr(f){const g={};return Object.entries(f).forEach(([u,m])=>{"string"==typeof m&&(m=[m]),null!==m&&(g[u]=qn(new _t([],{}),0,m))}),g}function Ni(f){const g={};return Object.entries(f).forEach(([u,m])=>g[u]=`${m}`),g}function Qi(f,g,u){return f==u.path&&ht(g,u.parameters)}const Ar="imperative";class yr{constructor(g,u){this.id=g,this.url=u}}class qi extends yr{constructor(g,u,m="imperative",w=null){super(g,u),this.type=0,this.navigationTrigger=m,this.restoredState=w}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class jr extends yr{constructor(g,u,m){super(g,u),this.urlAfterRedirects=m,this.type=1}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class ir extends yr{constructor(g,u,m,w){super(g,u),this.reason=m,this.code=w,this.type=2}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class Hn extends yr{constructor(g,u,m,w){super(g,u),this.reason=m,this.code=w,this.type=16}}class Zi extends yr{constructor(g,u,m,w){super(g,u),this.error=m,this.target=w,this.type=3}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class Oo extends yr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=4}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class No extends yr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=7}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class Gr extends yr{constructor(g,u,m,w,N){super(g,u),this.urlAfterRedirects=m,this.state=w,this.shouldActivate=N,this.type=8}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class io extends yr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=5}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class gi extends yr{constructor(g,u,m,w){super(g,u),this.urlAfterRedirects=m,this.state=w,this.type=6}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class mi{constructor(g){this.route=g,this.type=9}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class oo{constructor(g){this.route=g,this.type=10}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class Po{constructor(g){this.snapshot=g,this.type=11}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Ji{constructor(g){this.snapshot=g,this.type=12}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Ro{constructor(g){this.snapshot=g,this.type=13}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class $o{constructor(g){this.snapshot=g,this.type=14}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class yi{constructor(g,u,m){this.routerEvent=g,this.position=u,this.anchor=m,this.type=15}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}class Pi{}class Wr{constructor(g){this.url=g}}class vi{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new Kr,this.attachRef=null}}let Kr=(()=>{class f{constructor(){this.contexts=new Map}onChildOutletCreated(u,m){const w=this.getOrCreateContext(u);w.outlet=m,this.contexts.set(u,w)}onChildOutletDestroyed(u){const m=this.getContext(u);m&&(m.outlet=null,m.attachRef=null)}onOutletDeactivated(){const u=this.contexts;return this.contexts=new Map,u}onOutletReAttached(u){this.contexts=u}getOrCreateContext(u){let m=this.getContext(u);return m||(m=new vi,this.contexts.set(u,m)),m}getContext(u){return this.contexts.get(u)||null}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();class Nt{constructor(g){this._root=g}get root(){return this._root.value}parent(g){const u=this.pathFromRoot(g);return u.length>1?u[u.length-2]:null}children(g){const u=gn(g,this._root);return u?u.children.map(m=>m.value):[]}firstChild(g){const u=gn(g,this._root);return u&&u.children.length>0?u.children[0].value:null}siblings(g){const u=Ln(g,this._root);return u.length<2?[]:u[u.length-2].children.map(w=>w.value).filter(w=>w!==g)}pathFromRoot(g){return Ln(g,this._root).map(u=>u.value)}}function gn(f,g){if(f===g.value)return g;for(const u of g.children){const m=gn(f,u);if(m)return m}return null}function Ln(f,g){if(f===g.value)return[g];for(const u of g.children){const m=Ln(f,u);if(m.length)return m.unshift(g),m}return[]}class bn{constructor(g,u){this.value=g,this.children=u}toString(){return`TreeNode(${this.value})`}}function An(f){const g={};return f&&f.children.forEach(u=>g[u.value.outlet]=u),g}class ii extends Nt{constructor(g,u){super(g),this.snapshot=u,qt(this,g)}toString(){return this.snapshot.toString()}}function Ri(f,g){const u=function Xr(f,g){const F=new je([],{},{},"",{},Re,g,null,{});return new Dt("",new bn(F,[]))}(0,g),m=new ee.t([new dt("",{})]),w=new ee.t({}),N=new ee.t({}),F=new ee.t({}),re=new ee.t(""),Y=new Ur(m,w,F,re,N,Re,g,u.root);return Y.snapshot=u.root,new ii(new bn(Y,[]),u)}class Ur{constructor(g,u,m,w,N,F,re,Y){this.urlSubject=g,this.paramsSubject=u,this.queryParamsSubject=m,this.fragmentSubject=w,this.dataSubject=N,this.outlet=F,this.component=re,this._futureSnapshot=Y,this.title=this.dataSubject?.pipe((0,jt.T)(Be=>Be[Un]))??(0,ne.of)(void 0),this.url=g,this.params=u,this.queryParams=m,this.fragment=w,this.data=N}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe((0,jt.T)(g=>_n(g)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe((0,jt.T)(g=>_n(g)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function ao(f,g="emptyOnly"){const u=f.pathFromRoot;let m=0;if("always"!==g)for(m=u.length-1;m>=1;){const w=u[m],N=u[m-1];if(w.routeConfig&&""===w.routeConfig.path)m--;else{if(N.component)break;m--}}return function rn(f){return f.reduce((g,u)=>({params:{...g.params,...u.params},data:{...g.data,...u.data},resolve:{...u.data,...g.resolve,...u.routeConfig?.data,...u._resolvedData}}),{params:{},data:{},resolve:{}})}(u.slice(m))}class je{get title(){return this.data?.[Un]}constructor(g,u,m,w,N,F,re,Y,Be){this.url=g,this.params=u,this.queryParams=m,this.fragment=w,this.data=N,this.outlet=F,this.component=re,this.routeConfig=Y,this._resolve=Be}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=_n(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=_n(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(m=>m.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class Dt extends Nt{constructor(g,u){super(u),this.url=g,qt(this,u)}toString(){return Zn(this._root)}}function qt(f,g){g.value._routerState=f,g.children.forEach(u=>qt(f,u))}function Zn(f){const g=f.children.length>0?` { ${f.children.map(Zn).join(", ")} } `:"";return`${f.value}${g}`}function Qr(f){if(f.snapshot){const g=f.snapshot,u=f._futureSnapshot;f.snapshot=u,ht(g.queryParams,u.queryParams)||f.queryParamsSubject.next(u.queryParams),g.fragment!==u.fragment&&f.fragmentSubject.next(u.fragment),ht(g.params,u.params)||f.paramsSubject.next(u.params),function vt(f,g){if(f.length!==g.length)return!1;for(let u=0;u<f.length;++u)if(!ht(f[u],g[u]))return!1;return!0}(g.url,u.url)||f.urlSubject.next(u.url),ht(g.data,u.data)||f.dataSubject.next(u.data)}else f.snapshot=f._futureSnapshot,f.dataSubject.next(f._futureSnapshot.data)}function On(f,g){const u=ht(f.params,g.params)&&function pi(f,g){return Kn(f,g)&&f.every((u,m)=>ht(u.parameters,g[m].parameters))}(f.url,g.url);return u&&!(!f.parent!=!g.parent)&&(!f.parent||On(f.parent,g.parent))}let Br=(()=>{class f{constructor(){this.activated=null,this._activatedRoute=null,this.name=Re,this.activateEvents=new c.bkB,this.deactivateEvents=new c.bkB,this.attachEvents=new c.bkB,this.detachEvents=new c.bkB,this.parentContexts=(0,c.WQX)(Kr),this.location=(0,c.WQX)(c.c1b),this.changeDetector=(0,c.WQX)(c.gRc),this.environmentInjector=(0,c.WQX)(c.uvJ),this.inputBinder=(0,c.WQX)(Or,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(u){if(u.name){const{firstChange:m,previousValue:w}=u.name;if(m)return;this.isTrackedInParentContexts(w)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(w)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(u){return this.parentContexts.getContext(u)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const u=this.parentContexts.getContext(this.name);u?.route&&(u.attachRef?this.attach(u.attachRef,u.route):this.activateWith(u.route,u.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new c.wOt(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new c.wOt(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new c.wOt(4012,!1);this.location.detach();const u=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(u.instance),u}attach(u,m){this.activated=u,this._activatedRoute=m,this.location.insert(u.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(u.instance)}deactivate(){if(this.activated){const u=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(u)}}activateWith(u,m){if(this.isActivated)throw new c.wOt(4013,!1);this._activatedRoute=u;const w=this.location,F=u.snapshot.component,re=this.parentContexts.getOrCreateContext(this.name).children,Y=new Dn(u,re,w.injector);this.activated=w.createComponent(F,{index:w.length,injector:Y,environmentInjector:m??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275dir=c.FsC({type:f,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[c.OA$]})}}return f})();class Dn{constructor(g,u,m){this.route=g,this.childContexts=u,this.parent=m}get(g,u){return g===Ur?this.route:g===Kr?this.childContexts:this.parent.get(g,u)}}const Or=new c.nKC("");let Nr=(()=>{class f{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(u){this.unsubscribeFromRouteData(u),this.subscribeToRouteData(u)}unsubscribeFromRouteData(u){this.outletDataSubscriptions.get(u)?.unsubscribe(),this.outletDataSubscriptions.delete(u)}subscribeToRouteData(u){const{activatedRoute:m}=u,w=Ve([m.queryParams,m.params,m.data]).pipe((0,Mn.n)(([N,F,re],Y)=>(re={...N,...F,...re},0===Y?(0,ne.of)(re):Promise.resolve(re)))).subscribe(N=>{if(!u.isActivated||!u.activatedComponentRef||u.activatedRoute!==m||null===m.component)return void this.unsubscribeFromRouteData(u);const F=(0,c.HJs)(m.component);if(F)for(const{templateName:re}of F.inputs)u.activatedComponentRef.setInput(re,N[re]);else this.unsubscribeFromRouteData(u)});this.outletDataSubscriptions.set(u,w)}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac})}}return f})();function vr(f,g,u){if(u&&f.shouldReuseRoute(g.value,u.value.snapshot)){const m=u.value;m._futureSnapshot=g.value;const w=function lo(f,g,u){return g.children.map(m=>{for(const w of u.children)if(f.shouldReuseRoute(m.value,w.value.snapshot))return vr(f,m,w);return vr(f,m)})}(f,g,u);return new bn(m,w)}{if(f.shouldAttach(g.value)){const N=f.retrieve(g.value);if(null!==N){const F=N.route;return F.value._futureSnapshot=g.value,F.children=g.children.map(re=>vr(f,re)),F}}const m=function Dr(f){return new Ur(new ee.t(f.url),new ee.t(f.params),new ee.t(f.queryParams),new ee.t(f.fragment),new ee.t(f.data),f.outlet,f.component,f)}(g.value),w=g.children.map(N=>vr(f,N));return new bn(m,w)}}const Yt="ngNavigationCancelingError";function oi(f,g){const{redirectTo:u,navigationBehaviorOptions:m}=$n(g)?{redirectTo:g,navigationBehaviorOptions:void 0}:g,w=qr(!1,0,g);return w.url=u,w.navigationBehaviorOptions=m,w}function qr(f,g,u){const m=new Error("NavigationCancelingError: "+(f||""));return m[Yt]=!0,m.cancellationCode=g,u&&(m.url=u),m}function Vn(f){return f&&f[Yt]}let Zr=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275cmp=c.VBU({type:f,selectors:[["ng-component"]],standalone:!0,features:[c.aNF],decls:1,vars:0,template:function(m,w){1&m&&c.nrm(0,"router-outlet")},dependencies:[Br],encapsulation:2})}}return f})();function ai(f){const g=f.children&&f.children.map(ai),u=g?{...f,children:g}:{...f};return!u.component&&!u.loadComponent&&(g||u.loadChildren)&&u.outlet&&u.outlet!==Re&&(u.component=Zr),u}function zn(f){return f.outlet||Re}function Lt(f){if(!f)return null;if(f.routeConfig?._injector)return f.routeConfig._injector;for(let g=f.parent;g;g=g.parent){const u=g.routeConfig;if(u?._loadedInjector)return u._loadedInjector;if(u?._injector)return u._injector}return null}class or{constructor(g,u,m,w,N){this.routeReuseStrategy=g,this.futureState=u,this.currState=m,this.forwardEvent=w,this.inputBindingEnabled=N}activate(g){const u=this.futureState._root,m=this.currState?this.currState._root:null;this.deactivateChildRoutes(u,m,g),Qr(this.futureState.root),this.activateChildRoutes(u,m,g)}deactivateChildRoutes(g,u,m){const w=An(u);g.children.forEach(N=>{const F=N.value.outlet;this.deactivateRoutes(N,w[F],m),delete w[F]}),Object.values(w).forEach(N=>{this.deactivateRouteAndItsChildren(N,m)})}deactivateRoutes(g,u,m){const w=g.value,N=u?u.value:null;if(w===N)if(w.component){const F=m.getContext(w.outlet);F&&this.deactivateChildRoutes(g,u,F.children)}else this.deactivateChildRoutes(g,u,m);else N&&this.deactivateRouteAndItsChildren(u,m)}deactivateRouteAndItsChildren(g,u){g.value.component&&this.routeReuseStrategy.shouldDetach(g.value.snapshot)?this.detachAndStoreRouteSubtree(g,u):this.deactivateRouteAndOutlet(g,u)}detachAndStoreRouteSubtree(g,u){const m=u.getContext(g.value.outlet),w=m&&g.value.component?m.children:u,N=An(g);for(const F of Object.keys(N))this.deactivateRouteAndItsChildren(N[F],w);if(m&&m.outlet){const F=m.outlet.detach(),re=m.children.onOutletDeactivated();this.routeReuseStrategy.store(g.value.snapshot,{componentRef:F,route:g,contexts:re})}}deactivateRouteAndOutlet(g,u){const m=u.getContext(g.value.outlet),w=m&&g.value.component?m.children:u,N=An(g);for(const F of Object.keys(N))this.deactivateRouteAndItsChildren(N[F],w);m&&(m.outlet&&(m.outlet.deactivate(),m.children.onOutletDeactivated()),m.attachRef=null,m.route=null)}activateChildRoutes(g,u,m){const w=An(u);g.children.forEach(N=>{this.activateRoutes(N,w[N.value.outlet],m),this.forwardEvent(new $o(N.value.snapshot))}),g.children.length&&this.forwardEvent(new Ji(g.value.snapshot))}activateRoutes(g,u,m){const w=g.value,N=u?u.value:null;if(Qr(w),w===N)if(w.component){const F=m.getOrCreateContext(w.outlet);this.activateChildRoutes(g,u,F.children)}else this.activateChildRoutes(g,u,m);else if(w.component){const F=m.getOrCreateContext(w.outlet);if(this.routeReuseStrategy.shouldAttach(w.snapshot)){const re=this.routeReuseStrategy.retrieve(w.snapshot);this.routeReuseStrategy.store(w.snapshot,null),F.children.onOutletReAttached(re.contexts),F.attachRef=re.componentRef,F.route=re.route.value,F.outlet&&F.outlet.attach(re.componentRef,re.route.value),Qr(re.route.value),this.activateChildRoutes(g,null,F.children)}else{const re=Lt(w.snapshot);F.attachRef=null,F.route=w,F.injector=re,F.outlet&&F.outlet.activateWith(w,F.injector),this.activateChildRoutes(g,null,F.children)}}else this.activateChildRoutes(g,null,m)}}class eo{constructor(g){this.path=g,this.route=this.path[this.path.length-1]}}class Cr{constructor(g,u){this.component=g,this.route=u}}function Li(f,g,u){const m=f._root;return en(m,g?g._root:null,u,[m.value])}function Di(f,g){const u=Symbol(),m=g.get(f,u);return m===u?"function"!=typeof f||(0,c.LfX)(f)?g.get(f):f:m}function en(f,g,u,m,w={canDeactivateChecks:[],canActivateChecks:[]}){const N=An(g);return f.children.forEach(F=>{(function mo(f,g,u,m,w={canDeactivateChecks:[],canActivateChecks:[]}){const N=f.value,F=g?g.value:null,re=u?u.getContext(f.value.outlet):null;if(F&&N.routeConfig===F.routeConfig){const Y=function es(f,g,u){if("function"==typeof u)return u(f,g);switch(u){case"pathParamsChange":return!Kn(f.url,g.url);case"pathParamsOrQueryParamsChange":return!Kn(f.url,g.url)||!ht(f.queryParams,g.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!On(f,g)||!ht(f.queryParams,g.queryParams);default:return!On(f,g)}}(F,N,N.routeConfig.runGuardsAndResolvers);Y?w.canActivateChecks.push(new eo(m)):(N.data=F.data,N._resolvedData=F._resolvedData),en(f,g,N.component?re?re.children:null:u,m,w),Y&&re&&re.outlet&&re.outlet.isActivated&&w.canDeactivateChecks.push(new Cr(re.outlet.component,F))}else F&&b(g,re,w),w.canActivateChecks.push(new eo(m)),en(f,null,N.component?re?re.children:null:u,m,w)})(F,N[F.value.outlet],u,m.concat([F.value]),w),delete N[F.value.outlet]}),Object.entries(N).forEach(([F,re])=>b(re,u.getContext(F),w)),w}function b(f,g,u){const m=An(f),w=f.value;Object.entries(m).forEach(([N,F])=>{b(F,w.component?g?g.children.getContext(N):null:g,u)}),u.canDeactivateChecks.push(new Cr(w.component&&g&&g.outlet&&g.outlet.isActivated?g.outlet.component:null,w))}function S(f){return"function"==typeof f}function ji(f){return f instanceof Rt||"EmptyError"===f?.name}const Ui=Symbol("INITIAL_VALUE");function li(){return(0,Mn.n)(f=>Ve(f.map(g=>g.pipe(Sn(1),function fn(...f){const g=(0,H.lI)(f);return(0,Pe.N)((u,m)=>{(g?Ne(f,u,g):Ne(f,u)).subscribe(m)})}(Ui)))).pipe((0,jt.T)(g=>{for(const u of g)if(!0!==u){if(u===Ui)return Ui;if(!1===u||u instanceof Ir)return u}return!0}),(0,Z.p)(g=>g!==Ui),Sn(1)))}function ha(f){return(0,ke.F)((0,ft.M)(g=>{if($n(g))throw oi(0,g)}),(0,jt.T)(g=>!0===g))}class $i{constructor(g){this.segmentGroup=g||null}}class _r{constructor(g){this.urlTree=g}}function gr(f){return(0,Te.$)(new $i(f))}function Jr(f){return(0,Te.$)(new _r(f))}class xo{constructor(g,u){this.urlSerializer=g,this.urlTree=u}noMatchError(g){return new c.wOt(4002,!1)}lineralizeSegments(g,u){let m=[],w=u.root;for(;;){if(m=m.concat(w.segments),0===w.numberOfChildren)return(0,ne.of)(m);if(w.numberOfChildren>1||!w.children[Re])return(0,Te.$)(new c.wOt(4e3,!1));w=w.children[Re]}}applyRedirectCommands(g,u,m){return this.applyRedirectCreateUrlTree(u,this.urlSerializer.parse(u),g,m)}applyRedirectCreateUrlTree(g,u,m,w){const N=this.createSegmentGroup(g,u.root,m,w);return new Ir(N,this.createQueryParams(u.queryParams,this.urlTree.queryParams),u.fragment)}createQueryParams(g,u){const m={};return Object.entries(g).forEach(([w,N])=>{if("string"==typeof N&&N.startsWith(":")){const re=N.substring(1);m[w]=u[re]}else m[w]=N}),m}createSegmentGroup(g,u,m,w){const N=this.createSegments(g,u.segments,m,w);let F={};return Object.entries(u.children).forEach(([re,Y])=>{F[re]=this.createSegmentGroup(g,Y,m,w)}),new _t(N,F)}createSegments(g,u,m,w){return u.map(N=>N.path.startsWith(":")?this.findPosParam(g,N,w):this.findOrReturn(N,m))}findPosParam(g,u,m){const w=m[u.path.substring(1)];if(!w)throw new c.wOt(4001,!1);return w}findOrReturn(g,u){let m=0;for(const w of u){if(w.path===g.path)return u.splice(m),w;m++}return g}}const uo={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function Fo(f,g,u,m,w){const N=co(f,g,u);return N.matched?(m=function Jn(f,g){return f.providers&&!f._injector&&(f._injector=(0,c.Ol2)(f.providers,g,`Route: ${f.path}`)),f._injector??g}(g,m),function sr(f,g,u,m){const w=g.canMatch;if(!w||0===w.length)return(0,ne.of)(!0);const N=w.map(F=>{const re=Di(F,f);return sn(function er(f){return f&&S(f.canMatch)}(re)?re.canMatch(g,u):f.runInContext(()=>re(g,u)))});return(0,ne.of)(N).pipe(li(),ha())}(m,g,u).pipe((0,jt.T)(F=>!0===F?N:{...uo}))):(0,ne.of)(N)}function co(f,g,u){if(""===g.path)return"full"===g.pathMatch&&(f.hasChildren()||u.length>0)?{...uo}:{matched:!0,consumedSegments:[],remainingSegments:u,parameters:{},positionalParamSegments:{}};const w=(g.matcher||ur)(u,f,g);if(!w)return{...uo};const N={};Object.entries(w.posParams??{}).forEach(([re,Y])=>{N[re]=Y.path});const F=w.consumed.length>0?{...N,...w.consumed[w.consumed.length-1].parameters}:N;return{matched:!0,consumedSegments:w.consumed,remainingSegments:u.slice(w.consumed.length),parameters:F,positionalParamSegments:w.posParams??{}}}function Go(f,g,u,m){return u.length>0&&function ko(f,g,u){return u.some(m=>Ei(f,g,m)&&zn(m)!==Re)}(f,u,m)?{segmentGroup:new _t(g,ls(m,new _t(u,f.children))),slicedSegments:[]}:0===u.length&&function vo(f,g,u){return u.some(m=>Ei(f,g,m))}(f,u,m)?{segmentGroup:new _t(f.segments,as(f,0,u,m,f.children)),slicedSegments:u}:{segmentGroup:new _t(f.segments,f.children),slicedSegments:u}}function as(f,g,u,m,w){const N={};for(const F of m)if(Ei(f,u,F)&&!w[zn(F)]){const re=new _t([],{});N[zn(F)]=re}return{...w,...N}}function ls(f,g){const u={};u[Re]=g;for(const m of f)if(""===m.path&&zn(m)!==Re){const w=new _t([],{});u[zn(m)]=w}return u}function Ei(f,g,u){return(!(f.hasChildren()||g.length>0)||"full"!==u.pathMatch)&&""===u.path}class ma{constructor(g,u,m,w,N,F,re){this.injector=g,this.configLoader=u,this.rootComponentType=m,this.config=w,this.urlTree=N,this.paramsInheritanceStrategy=F,this.urlSerializer=re,this.allowRedirects=!0,this.applyRedirects=new xo(this.urlSerializer,this.urlTree)}noMatchError(g){return new c.wOt(4002,!1)}recognize(){const g=Go(this.urlTree.root,[],[],this.config).segmentGroup;return this.processSegmentGroup(this.injector,this.config,g,Re).pipe((0,Bt.W)(u=>{if(u instanceof _r)return this.allowRedirects=!1,this.urlTree=u.urlTree,this.match(u.urlTree);throw u instanceof $i?this.noMatchError(u):u}),(0,jt.T)(u=>{const m=new je([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,{},Re,this.rootComponentType,null,{}),w=new bn(m,u),N=new Dt("",w),F=function In(f,g,u=null,m=null){return Tr(pn(f),g,u,m)}(m,[],this.urlTree.queryParams,this.urlTree.fragment);return F.queryParams=this.urlTree.queryParams,N.url=this.urlSerializer.serialize(F),this.inheritParamsAndData(N._root),{state:N,tree:F}}))}match(g){return this.processSegmentGroup(this.injector,this.config,g.root,Re).pipe((0,Bt.W)(m=>{throw m instanceof $i?this.noMatchError(m):m}))}inheritParamsAndData(g){const u=g.value,m=ao(u,this.paramsInheritanceStrategy);u.params=Object.freeze(m.params),u.data=Object.freeze(m.data),g.children.forEach(w=>this.inheritParamsAndData(w))}processSegmentGroup(g,u,m,w){return 0===m.segments.length&&m.hasChildren()?this.processChildren(g,u,m):this.processSegment(g,u,m,m.segments,w,!0)}processChildren(g,u,m){const w=[];for(const N of Object.keys(m.children))"primary"===N?w.unshift(N):w.push(N);return(0,X.H)(w).pipe((0,Ge.H)(N=>{const F=m.children[N],re=function Fi(f,g){const u=f.filter(m=>zn(m)===g);return u.push(...f.filter(m=>zn(m)!==g)),u}(u,N);return this.processSegmentGroup(g,re,F,N)}),function xt(f,g){return(0,Pe.N)(function ut(f,g,u,m,w){return(N,F)=>{let re=u,Y=g,Be=0;N.subscribe((0,ve._)(F,Et=>{const un=Be++;Y=re?f(Y,Et,un):(re=!0,Et),m&&F.next(Y)},w&&(()=>{re&&F.next(Y),F.complete()})))}}(f,g,arguments.length>=2,!0))}((N,F)=>(N.push(...F),N)),ie(null),function tn(f,g){const u=arguments.length>=2;return m=>m.pipe(f?(0,Z.p)((w,N)=>f(w,N,m)):le.D,Qt(1),u?ie(g):ae(()=>new Rt))}(),(0,te.Z)(N=>{if(null===N)return gr(m);const F=h(N);return function Do(f){f.sort((g,u)=>g.value.outlet===Re?-1:u.value.outlet===Re?1:g.value.outlet.localeCompare(u.value.outlet))}(F),(0,ne.of)(F)}))}processSegment(g,u,m,w,N,F){return(0,X.H)(u).pipe((0,Ge.H)(re=>this.processSegmentAgainstRoute(re._injector??g,u,re,m,w,N,F).pipe((0,Bt.W)(Y=>{if(Y instanceof $i)return(0,ne.of)(null);throw Y}))),be(re=>!!re),(0,Bt.W)(re=>{if(ji(re))return function pa(f,g,u){return 0===g.length&&!f.children[u]}(m,w,N)?(0,ne.of)([]):gr(m);throw re}))}processSegmentAgainstRoute(g,u,m,w,N,F,re){return function us(f,g,u,m){return!!(zn(f)===m||m!==Re&&Ei(g,u,f))&&("**"===f.path||co(g,f,u).matched)}(m,w,N,F)?void 0===m.redirectTo?this.matchSegmentAgainstRoute(g,w,m,N,F,re):re&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(g,w,u,m,N,F):gr(w):gr(w)}expandSegmentAgainstRouteUsingRedirect(g,u,m,w,N,F){return"**"===w.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(g,m,w,F):this.expandRegularSegmentAgainstRouteUsingRedirect(g,u,m,w,N,F)}expandWildCardWithParamsAgainstRouteUsingRedirect(g,u,m,w){const N=this.applyRedirects.applyRedirectCommands([],m.redirectTo,{});return m.redirectTo.startsWith("/")?Jr(N):this.applyRedirects.lineralizeSegments(m,N).pipe((0,te.Z)(F=>{const re=new _t(F,{});return this.processSegment(g,u,re,F,w,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(g,u,m,w,N,F){const{matched:re,consumedSegments:Y,remainingSegments:Be,positionalParamSegments:Et}=co(u,w,N);if(!re)return gr(u);const un=this.applyRedirects.applyRedirectCommands(Y,w.redirectTo,Et);return w.redirectTo.startsWith("/")?Jr(un):this.applyRedirects.lineralizeSegments(w,un).pipe((0,te.Z)(Kt=>this.processSegment(g,m,u,Kt.concat(Be),F,!1)))}matchSegmentAgainstRoute(g,u,m,w,N,F){let re;if("**"===m.path){const Y=w.length>0?Fn(w).parameters:{},Be=new je(w,Y,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,D(m),zn(m),m.component??m._loadedComponent??null,m,I(m));re=(0,ne.of)({snapshot:Be,consumedSegments:[],remainingSegments:[]}),u.children={}}else re=Fo(u,m,w,g).pipe((0,jt.T)(({matched:Y,consumedSegments:Be,remainingSegments:Et,parameters:un})=>Y?{snapshot:new je(Be,un,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,D(m),zn(m),m.component??m._loadedComponent??null,m,I(m)),consumedSegments:Be,remainingSegments:Et}:null));return re.pipe((0,Mn.n)(Y=>null===Y?gr(u):this.getChildConfig(g=m._injector??g,m,w).pipe((0,Mn.n)(({routes:Be})=>{const Et=m._loadedInjector??g,{snapshot:un,consumedSegments:Kt,remainingSegments:zi}=Y,{segmentGroup:hs,slicedSegments:ps}=Go(u,Kt,zi,Be);if(0===ps.length&&hs.hasChildren())return this.processChildren(Et,Be,hs).pipe((0,jt.T)(Mi=>null===Mi?null:[new bn(un,Mi)]));if(0===Be.length&&0===ps.length)return(0,ne.of)([new bn(un,[])]);const br=zn(m)===N;return this.processSegment(Et,Be,hs,ps,br?Re:N,!0).pipe((0,jt.T)(Mi=>[new bn(un,Mi)]))}))))}getChildConfig(g,u,m){return u.children?(0,ne.of)({routes:u.children,injector:g}):u.loadChildren?void 0!==u._loadedRoutes?(0,ne.of)({routes:u._loadedRoutes,injector:u._loadedInjector}):function ws(f,g,u,m){const w=g.canLoad;if(void 0===w||0===w.length)return(0,ne.of)(!0);const N=w.map(F=>{const re=Di(F,f);return sn(function P(f){return f&&S(f.canLoad)}(re)?re.canLoad(g,u):f.runInContext(()=>re(g,u)))});return(0,ne.of)(N).pipe(li(),ha())}(g,u,m).pipe((0,te.Z)(w=>w?this.configLoader.loadChildren(g,u).pipe((0,ft.M)(N=>{u._loadedRoutes=N.routes,u._loadedInjector=N.injector})):function ss(f){return(0,Te.$)(qr(!1,3))}())):(0,ne.of)({routes:[],injector:g})}}function bs(f){const g=f.value.routeConfig;return g&&""===g.path}function h(f){const g=[],u=new Set;for(const m of f){if(!bs(m)){g.push(m);continue}const w=g.find(N=>m.value.routeConfig===N.value.routeConfig);void 0!==w?(w.children.push(...m.children),u.add(w)):g.push(m)}for(const m of u){const w=h(m.children);g.push(new bn(m.value,w))}return g.filter(m=>!u.has(m))}function D(f){return f.data||{}}function I(f){return f.resolve||{}}function Ht(f){return"string"==typeof f.title||null===f.title}function Tt(f){return(0,Mn.n)(g=>{const u=f(g);return u?(0,X.H)(u).pipe((0,jt.T)(()=>g)):(0,ne.of)(g)})}const on=new c.nKC("ROUTES");let _=(()=>{class f{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=(0,c.WQX)(c.Ql9)}loadComponent(u){if(this.componentLoaders.get(u))return this.componentLoaders.get(u);if(u._loadedComponent)return(0,ne.of)(u._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(u);const m=sn(u.loadComponent()).pipe((0,jt.T)(y),(0,ft.M)(N=>{this.onLoadEndListener&&this.onLoadEndListener(u),u._loadedComponent=N}),(0,hn.j)(()=>{this.componentLoaders.delete(u)})),w=new et(m,()=>new gt.B).pipe(cn());return this.componentLoaders.set(u,w),w}loadChildren(u,m){if(this.childrenLoaders.get(m))return this.childrenLoaders.get(m);if(m._loadedRoutes)return(0,ne.of)({routes:m._loadedRoutes,injector:m._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(m);const N=function a(f,g,u,m){return sn(f.loadChildren()).pipe((0,jt.T)(y),(0,te.Z)(w=>w instanceof c.Co$||Array.isArray(w)?(0,ne.of)(w):(0,X.H)(g.compileModuleAsync(w))),(0,jt.T)(w=>{m&&m(f);let N,F,re=!1;return Array.isArray(w)?(F=w,!0):(N=w.create(u).injector,F=N.get(on,[],{optional:!0,self:!0}).flat()),{routes:F.map(ai),injector:N}}))}(m,this.compiler,u,this.onLoadEndListener).pipe((0,hn.j)(()=>{this.childrenLoaders.delete(m)})),F=new et(N,()=>new gt.B).pipe(cn());return this.childrenLoaders.set(m,F),F}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();function y(f){return function l(f){return f&&"object"==typeof f&&"default"in f}(f)?f.default:f}let M=(()=>{class f{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new gt.B,this.transitionAbortSubject=new gt.B,this.configLoader=(0,c.WQX)(_),this.environmentInjector=(0,c.WQX)(c.uvJ),this.urlSerializer=(0,c.WQX)(rr),this.rootContexts=(0,c.WQX)(Kr),this.inputBindingEnabled=null!==(0,c.WQX)(Or,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>(0,ne.of)(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=w=>this.events.next(new oo(w)),this.configLoader.onLoadStartListener=w=>this.events.next(new mi(w))}complete(){this.transitions?.complete()}handleNavigationRequest(u){const m=++this.navigationId;this.transitions?.next({...this.transitions.value,...u,id:m})}setupNavigations(u,m,w){return this.transitions=new ee.t({id:0,currentUrlTree:m,currentRawUrl:m,currentBrowserUrl:m,extractedUrl:u.urlHandlingStrategy.extract(m),urlAfterRedirects:u.urlHandlingStrategy.extract(m),rawUrl:m,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:Ar,restoredState:null,currentSnapshot:w.snapshot,targetSnapshot:null,currentRouterState:w,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe((0,Z.p)(N=>0!==N.id),(0,jt.T)(N=>({...N,extractedUrl:u.urlHandlingStrategy.extract(N.rawUrl)})),(0,Mn.n)(N=>{this.currentTransition=N;let F=!1,re=!1;return(0,ne.of)(N).pipe((0,ft.M)(Y=>{this.currentNavigation={id:Y.id,initialUrl:Y.rawUrl,extractedUrl:Y.extractedUrl,trigger:Y.source,extras:Y.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null}}),(0,Mn.n)(Y=>{const Be=Y.currentBrowserUrl.toString(),Et=!u.navigated||Y.extractedUrl.toString()!==Be||Be!==Y.currentUrlTree.toString();if(!Et&&"reload"!==(Y.extras.onSameUrlNavigation??u.onSameUrlNavigation)){const Kt="";return this.events.next(new Hn(Y.id,this.urlSerializer.serialize(Y.rawUrl),Kt,0)),Y.resolve(null),ot.w}if(u.urlHandlingStrategy.shouldProcessUrl(Y.rawUrl))return(0,ne.of)(Y).pipe((0,Mn.n)(Kt=>{const zi=this.transitions?.getValue();return this.events.next(new qi(Kt.id,this.urlSerializer.serialize(Kt.extractedUrl),Kt.source,Kt.restoredState)),zi!==this.transitions?.getValue()?ot.w:Promise.resolve(Kt)}),function k(f,g,u,m,w,N){return(0,te.Z)(F=>function ga(f,g,u,m,w,N,F="emptyOnly"){return new ma(f,g,u,m,w,F,N).recognize()}(f,g,u,m,F.extractedUrl,w,N).pipe((0,jt.T)(({state:re,tree:Y})=>({...F,targetSnapshot:re,urlAfterRedirects:Y}))))}(this.environmentInjector,this.configLoader,this.rootComponentType,u.config,this.urlSerializer,u.paramsInheritanceStrategy),(0,ft.M)(Kt=>{N.targetSnapshot=Kt.targetSnapshot,N.urlAfterRedirects=Kt.urlAfterRedirects,this.currentNavigation={...this.currentNavigation,finalUrl:Kt.urlAfterRedirects};const zi=new Oo(Kt.id,this.urlSerializer.serialize(Kt.extractedUrl),this.urlSerializer.serialize(Kt.urlAfterRedirects),Kt.targetSnapshot);this.events.next(zi)}));if(Et&&u.urlHandlingStrategy.shouldProcessUrl(Y.currentRawUrl)){const{id:Kt,extractedUrl:zi,source:hs,restoredState:ps,extras:br}=Y,Mi=new qi(Kt,this.urlSerializer.serialize(zi),hs,ps);this.events.next(Mi);const Rn=Ri(0,this.rootComponentType).snapshot;return this.currentTransition=N={...Y,targetSnapshot:Rn,urlAfterRedirects:zi,extras:{...br,skipLocationChange:!1,replaceUrl:!1}},(0,ne.of)(N)}{const Kt="";return this.events.next(new Hn(Y.id,this.urlSerializer.serialize(Y.extractedUrl),Kt,1)),Y.resolve(null),ot.w}}),(0,ft.M)(Y=>{const Be=new No(Y.id,this.urlSerializer.serialize(Y.extractedUrl),this.urlSerializer.serialize(Y.urlAfterRedirects),Y.targetSnapshot);this.events.next(Be)}),(0,jt.T)(Y=>(this.currentTransition=N={...Y,guards:Li(Y.targetSnapshot,Y.currentSnapshot,this.rootContexts)},N)),function yo(f,g){return(0,te.Z)(u=>{const{targetSnapshot:m,currentSnapshot:w,guards:{canActivateChecks:N,canDeactivateChecks:F}}=u;return 0===F.length&&0===N.length?(0,ne.of)({...u,guardsResult:!0}):function ts(f,g,u,m){return(0,X.H)(f).pipe((0,te.Z)(w=>function _i(f,g,u,m,w){const N=g&&g.routeConfig?g.routeConfig.canDeactivate:null;if(!N||0===N.length)return(0,ne.of)(!0);const F=N.map(re=>{const Y=Lt(g)??w,Be=Di(re,Y);return sn(function Ct(f){return f&&S(f.canDeactivate)}(Be)?Be.canDeactivate(f,g,u,m):Y.runInContext(()=>Be(f,g,u,m))).pipe(be())});return(0,ne.of)(F).pipe(li())}(w.component,w.route,u,g,m)),be(w=>!0!==w,!0))}(F,m,w,f).pipe((0,te.Z)(re=>re&&function v(f){return"boolean"==typeof f}(re)?function ns(f,g,u,m){return(0,X.H)(g).pipe((0,Ge.H)(w=>Ne(function zo(f,g){return null!==f&&g&&g(new Po(f)),(0,ne.of)(!0)}(w.route.parent,m),function rs(f,g){return null!==f&&g&&g(new Ro(f)),(0,ne.of)(!0)}(w.route,m),function Bi(f,g,u){const m=g[g.length-1],N=g.slice(0,g.length-1).reverse().map(F=>function pr(f){const g=f.routeConfig?f.routeConfig.canActivateChild:null;return g&&0!==g.length?{node:f,guards:g}:null}(F)).filter(F=>null!==F).map(F=>we(()=>{const re=F.guards.map(Y=>{const Be=Lt(F.node)??u,Et=Di(Y,Be);return sn(function ze(f){return f&&S(f.canActivateChild)}(Et)?Et.canActivateChild(m,f):Be.runInContext(()=>Et(m,f))).pipe(be())});return(0,ne.of)(re).pipe(li())}));return(0,ne.of)(N).pipe(li())}(f,w.path,u),function is(f,g,u){const m=g.routeConfig?g.routeConfig.canActivate:null;if(!m||0===m.length)return(0,ne.of)(!0);const w=m.map(N=>we(()=>{const F=Lt(g)??u,re=Di(N,F);return sn(function B(f){return f&&S(f.canActivate)}(re)?re.canActivate(g,f):F.runInContext(()=>re(g,f))).pipe(be())}));return(0,ne.of)(w).pipe(li())}(f,w.route,u))),be(w=>!0!==w,!0))}(m,N,f,g):(0,ne.of)(re)),(0,jt.T)(re=>({...u,guardsResult:re})))})}(this.environmentInjector,Y=>this.events.next(Y)),(0,ft.M)(Y=>{if(N.guardsResult=Y.guardsResult,$n(Y.guardsResult))throw oi(0,Y.guardsResult);const Be=new Gr(Y.id,this.urlSerializer.serialize(Y.extractedUrl),this.urlSerializer.serialize(Y.urlAfterRedirects),Y.targetSnapshot,!!Y.guardsResult);this.events.next(Be)}),(0,Z.p)(Y=>!!Y.guardsResult||(this.cancelNavigationTransition(Y,"",3),!1)),Tt(Y=>{if(Y.guards.canActivateChecks.length)return(0,ne.of)(Y).pipe((0,ft.M)(Be=>{const Et=new io(Be.id,this.urlSerializer.serialize(Be.extractedUrl),this.urlSerializer.serialize(Be.urlAfterRedirects),Be.targetSnapshot);this.events.next(Et)}),(0,Mn.n)(Be=>{let Et=!1;return(0,ne.of)(Be).pipe(function oe(f,g){return(0,te.Z)(u=>{const{targetSnapshot:m,guards:{canActivateChecks:w}}=u;if(!w.length)return(0,ne.of)(u);let N=0;return(0,X.H)(w).pipe((0,Ge.H)(F=>function De(f,g,u,m){const w=f.routeConfig,N=f._resolve;return void 0!==w?.title&&!Ht(w)&&(N[Un]=w.title),function Qe(f,g,u,m){const w=function Zt(f){return[...Object.keys(f),...Object.getOwnPropertySymbols(f)]}(f);if(0===w.length)return(0,ne.of)({});const N={};return(0,X.H)(w).pipe((0,te.Z)(F=>function Jt(f,g,u,m){const w=Lt(g)??m,N=Di(f,w);return sn(N.resolve?N.resolve(g,u):w.runInContext(()=>N(g,u)))}(f[F],g,u,m).pipe(be(),(0,ft.M)(re=>{N[F]=re}))),Qt(1),function Ae(f){return(0,jt.T)(()=>f)}(N),(0,Bt.W)(F=>ji(F)?ot.w:(0,Te.$)(F)))}(N,f,g,m).pipe((0,jt.T)(F=>(f._resolvedData=F,f.data=ao(f,u).resolve,w&&Ht(w)&&(f.data[Un]=w.title),null)))}(F.route,m,f,g)),(0,ft.M)(()=>N++),Qt(1),(0,te.Z)(F=>N===w.length?(0,ne.of)(u):ot.w))})}(u.paramsInheritanceStrategy,this.environmentInjector),(0,ft.M)({next:()=>Et=!0,complete:()=>{Et||this.cancelNavigationTransition(Be,"",2)}}))}),(0,ft.M)(Be=>{const Et=new gi(Be.id,this.urlSerializer.serialize(Be.extractedUrl),this.urlSerializer.serialize(Be.urlAfterRedirects),Be.targetSnapshot);this.events.next(Et)}))}),Tt(Y=>{const Be=Et=>{const un=[];Et.routeConfig?.loadComponent&&!Et.routeConfig._loadedComponent&&un.push(this.configLoader.loadComponent(Et.routeConfig).pipe((0,ft.M)(Kt=>{Et.component=Kt}),(0,jt.T)(()=>{})));for(const Kt of Et.children)un.push(...Be(Kt));return un};return Ve(Be(Y.targetSnapshot.root)).pipe(ie(),Sn(1))}),Tt(()=>this.afterPreactivation()),(0,jt.T)(Y=>{const Be=function mt(f,g,u){const m=vr(f,g._root,u?u._root:void 0);return new ii(m,g)}(u.routeReuseStrategy,Y.targetSnapshot,Y.currentRouterState);return this.currentTransition=N={...Y,targetRouterState:Be},N}),(0,ft.M)(()=>{this.events.next(new Pi)}),((f,g,u,m)=>(0,jt.T)(w=>(new or(g,w.targetRouterState,w.currentRouterState,u,m).activate(f),w)))(this.rootContexts,u.routeReuseStrategy,Y=>this.events.next(Y),this.inputBindingEnabled),Sn(1),(0,ft.M)({next:Y=>{F=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new jr(Y.id,this.urlSerializer.serialize(Y.extractedUrl),this.urlSerializer.serialize(Y.urlAfterRedirects))),u.titleStrategy?.updateTitle(Y.targetRouterState.snapshot),Y.resolve(!0)},complete:()=>{F=!0}}),(0,xn.Q)(this.transitionAbortSubject.pipe((0,ft.M)(Y=>{throw Y}))),(0,hn.j)(()=>{F||re||this.cancelNavigationTransition(N,"",1),this.currentNavigation?.id===N.id&&(this.currentNavigation=null)}),(0,Bt.W)(Y=>{if(re=!0,Vn(Y))this.events.next(new ir(N.id,this.urlSerializer.serialize(N.extractedUrl),Y.message,Y.cancellationCode)),function hr(f){return Vn(f)&&$n(f.url)}(Y)?this.events.next(new Wr(Y.url)):N.resolve(!1);else{this.events.next(new Zi(N.id,this.urlSerializer.serialize(N.extractedUrl),Y,N.targetSnapshot??void 0));try{N.resolve(u.errorHandler(Y))}catch(Be){N.reject(Be)}}return ot.w}))}))}cancelNavigationTransition(u,m,w){const N=new ir(u.id,this.urlSerializer.serialize(u.extractedUrl),m,w);this.events.next(N),u.resolve(!1)}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();function R(f){return f!==Ar}let L=(()=>{class f{buildTitle(u){let m,w=u.root;for(;void 0!==w;)m=this.getResolvedTitleForRoute(w)??m,w=w.children.find(N=>N.outlet===Re);return m}getResolvedTitleForRoute(u){return u.data[Un]}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:function(){return(0,c.WQX)(U)},providedIn:"root"})}}return f})(),U=(()=>{class f extends L{constructor(u){super(),this.title=u}updateTitle(u){const m=this.buildTitle(u);void 0!==m&&this.title.setTitle(m)}static{this.\u0275fac=function(m){return new(m||f)(c.KVO(tr.hE))}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})(),J=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:function(){return(0,c.WQX)(Ue)},providedIn:"root"})}}return f})();class ge{shouldDetach(g){return!1}store(g,u){}shouldAttach(g){return!1}retrieve(g){return null}shouldReuseRoute(g,u){return g.routeConfig===u.routeConfig}}let Ue=(()=>{class f extends ge{static{this.\u0275fac=function(){let u;return function(w){return(u||(u=c.xGo(f)))(w||f)}}()}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();const He=new c.nKC("",{providedIn:"root",factory:()=>({})});let Ft=(()=>{class f{static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:function(){return(0,c.WQX)(wt)},providedIn:"root"})}}return f})(),wt=(()=>{class f{shouldProcessUrl(u){return!0}extract(u){return u}merge(u,m){return u}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();var st=function(f){return f[f.COMPLETE=0]="COMPLETE",f[f.FAILED=1]="FAILED",f[f.REDIRECTING=2]="REDIRECTING",f}(st||{});function at(f,g){f.events.pipe((0,Z.p)(u=>u instanceof jr||u instanceof ir||u instanceof Zi||u instanceof Hn),(0,jt.T)(u=>u instanceof jr||u instanceof Hn?st.COMPLETE:u instanceof ir&&(0===u.code||1===u.code)?st.REDIRECTING:st.FAILED),(0,Z.p)(u=>u!==st.REDIRECTING),Sn(1)).subscribe(()=>{g()})}function mn(f){throw f}function jn(f,g,u){return g.parse("/")}const St={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},ar={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let Gt=(()=>{class f{get navigationId(){return this.navigationTransitions.navigationId}get browserPageId(){return"computed"!==this.canceledNavigationResolution?this.currentPageId:this.location.getState()?.\u0275routerPageId??this.currentPageId}get events(){return this._events}constructor(){this.disposed=!1,this.currentPageId=0,this.console=(0,c.WQX)(c.H3F),this.isNgZoneEnabled=!1,this._events=new gt.B,this.options=(0,c.WQX)(He,{optional:!0})||{},this.pendingTasks=(0,c.WQX)(c.$K3),this.errorHandler=this.options.errorHandler||mn,this.malformedUriErrorHandler=this.options.malformedUriErrorHandler||jn,this.navigated=!1,this.lastSuccessfulId=-1,this.urlHandlingStrategy=(0,c.WQX)(Ft),this.routeReuseStrategy=(0,c.WQX)(J),this.titleStrategy=(0,c.WQX)(L),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.config=(0,c.WQX)(on,{optional:!0})?.flat()??[],this.navigationTransitions=(0,c.WQX)(M),this.urlSerializer=(0,c.WQX)(rr),this.location=(0,c.WQX)(dn.aZ),this.componentInputBindingEnabled=!!(0,c.WQX)(Or,{optional:!0}),this.eventsSubscription=new rt.yU,this.isNgZoneEnabled=(0,c.WQX)(c.SKi)instanceof c.SKi&&c.SKi.isInAngularZone(),this.resetConfig(this.config),this.currentUrlTree=new Ir,this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.routerState=Ri(0,null),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe(u=>{this.lastSuccessfulId=u.id,this.currentPageId=this.browserPageId},u=>{this.console.warn(`Unhandled Navigation Error: ${u}`)}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){const u=this.navigationTransitions.events.subscribe(m=>{try{const{currentTransition:w}=this.navigationTransitions;if(null===w)return void(ui(m)&&this._events.next(m));if(m instanceof qi)R(w.source)&&(this.browserUrlTree=w.extractedUrl);else if(m instanceof Hn)this.rawUrlTree=w.rawUrl;else if(m instanceof Oo){if("eager"===this.urlUpdateStrategy){if(!w.extras.skipLocationChange){const N=this.urlHandlingStrategy.merge(w.urlAfterRedirects,w.rawUrl);this.setBrowserUrl(N,w)}this.browserUrlTree=w.urlAfterRedirects}}else if(m instanceof Pi)this.currentUrlTree=w.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(w.urlAfterRedirects,w.rawUrl),this.routerState=w.targetRouterState,"deferred"===this.urlUpdateStrategy&&(w.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,w),this.browserUrlTree=w.urlAfterRedirects);else if(m instanceof ir)0!==m.code&&1!==m.code&&(this.navigated=!0),(3===m.code||2===m.code)&&this.restoreHistory(w);else if(m instanceof Wr){const N=this.urlHandlingStrategy.merge(m.url,w.currentRawUrl),F={skipLocationChange:w.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy||R(w.source)};this.scheduleNavigation(N,Ar,null,F,{resolve:w.resolve,reject:w.reject,promise:w.promise})}m instanceof Zi&&this.restoreHistory(w,!0),m instanceof jr&&(this.navigated=!0),ui(m)&&this._events.next(m)}catch(w){this.navigationTransitions.transitionAbortSubject.next(w)}});this.eventsSubscription.add(u)}resetRootComponentType(u){this.routerState.root.component=u,this.navigationTransitions.rootComponentType=u}initialNavigation(){if(this.setUpLocationChangeListener(),!this.navigationTransitions.hasRequestedNavigation){const u=this.location.getState();this.navigateToSyncWithBrowser(this.location.path(!0),Ar,u)}}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(u=>{const m="popstate"===u.type?"popstate":"hashchange";"popstate"===m&&setTimeout(()=>{this.navigateToSyncWithBrowser(u.url,m,u.state)},0)}))}navigateToSyncWithBrowser(u,m,w){const N={replaceUrl:!0},F=w?.navigationId?w:null;if(w){const Y={...w};delete Y.navigationId,delete Y.\u0275routerPageId,0!==Object.keys(Y).length&&(N.state=Y)}const re=this.parseUrl(u);this.scheduleNavigation(re,m,F,N)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(u){this.config=u.map(ai),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(u,m={}){const{relativeTo:w,queryParams:N,fragment:F,queryParamsHandling:re,preserveFragment:Y}=m,Be=Y?this.currentUrlTree.fragment:F;let un,Et=null;switch(re){case"merge":Et={...this.currentUrlTree.queryParams,...N};break;case"preserve":Et=this.currentUrlTree.queryParams;break;default:Et=N||null}null!==Et&&(Et=this.removeEmptyProps(Et));try{un=pn(w?w.snapshot:this.routerState.snapshot.root)}catch{("string"!=typeof u[0]||!u[0].startsWith("/"))&&(u=[]),un=this.currentUrlTree.root}return Tr(un,u,Et,Be??null)}navigateByUrl(u,m={skipLocationChange:!1}){const w=$n(u)?u:this.parseUrl(u),N=this.urlHandlingStrategy.merge(w,this.rawUrlTree);return this.scheduleNavigation(N,Ar,null,m)}navigate(u,m={skipLocationChange:!1}){return function Gn(f){for(let g=0;g<f.length;g++)if(null==f[g])throw new c.wOt(4008,!1)}(u),this.navigateByUrl(this.createUrlTree(u,m),m)}serializeUrl(u){return this.urlSerializer.serialize(u)}parseUrl(u){let m;try{m=this.urlSerializer.parse(u)}catch(w){m=this.malformedUriErrorHandler(w,this.urlSerializer,u)}return m}isActive(u,m){let w;if(w=!0===m?{...St}:!1===m?{...ar}:m,$n(u))return Mt(this.currentUrlTree,u,w);const N=this.parseUrl(u);return Mt(this.currentUrlTree,N,w)}removeEmptyProps(u){return Object.keys(u).reduce((m,w)=>{const N=u[w];return null!=N&&(m[w]=N),m},{})}scheduleNavigation(u,m,w,N,F){if(this.disposed)return Promise.resolve(!1);let re,Y,Be;F?(re=F.resolve,Y=F.reject,Be=F.promise):Be=new Promise((un,Kt)=>{re=un,Y=Kt});const Et=this.pendingTasks.add();return at(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(Et))}),this.navigationTransitions.handleNavigationRequest({source:m,restoredState:w,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,currentBrowserUrl:this.browserUrlTree,rawUrl:u,extras:N,resolve:re,reject:Y,promise:Be,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),Be.catch(un=>Promise.reject(un))}setBrowserUrl(u,m){const w=this.urlSerializer.serialize(u);if(this.location.isCurrentPathEqualTo(w)||m.extras.replaceUrl){const F={...m.extras.state,...this.generateNgRouterState(m.id,this.browserPageId)};this.location.replaceState(w,"",F)}else{const N={...m.extras.state,...this.generateNgRouterState(m.id,this.browserPageId+1)};this.location.go(w,"",N)}}restoreHistory(u,m=!1){if("computed"===this.canceledNavigationResolution){const N=this.currentPageId-this.browserPageId;0!==N?this.location.historyGo(N):this.currentUrlTree===this.getCurrentNavigation()?.finalUrl&&0===N&&(this.resetState(u),this.browserUrlTree=u.currentUrlTree,this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(m&&this.resetState(u),this.resetUrlToCurrentUrlTree())}resetState(u){this.routerState=u.currentRouterState,this.currentUrlTree=u.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,u.rawUrl)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(u,m){return"computed"===this.canceledNavigationResolution?{navigationId:u,\u0275routerPageId:m}:{navigationId:u}}static{this.\u0275fac=function(m){return new(m||f)}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();function ui(f){return!(f instanceof Pi||f instanceof Wr)}let Wn=(()=>{class f{constructor(u,m,w,N,F,re){this.router=u,this.route=m,this.tabIndexAttribute=w,this.renderer=N,this.el=F,this.locationStrategy=re,this.href=null,this.commands=null,this.onChanges=new gt.B,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;const Y=F.nativeElement.tagName?.toLowerCase();this.isAnchorElement="a"===Y||"area"===Y,this.isAnchorElement?this.subscription=u.events.subscribe(Be=>{Be instanceof jr&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(u){null!=this.tabIndexAttribute||this.isAnchorElement||this.applyAttributeValue("tabindex",u)}ngOnChanges(u){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(u){null!=u?(this.commands=Array.isArray(u)?u:[u],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(u,m,w,N,F){return!!(null===this.urlTree||this.isAnchorElement&&(0!==u||m||w||N||F||"string"==typeof this.target&&"_self"!=this.target))||(this.router.navigateByUrl(this.urlTree,{skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state}),!this.isAnchorElement)}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){this.href=null!==this.urlTree&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(this.urlTree)):null;const u=null===this.href?null:(0,c.n$t)(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",u)}applyAttributeValue(u,m){const w=this.renderer,N=this.el.nativeElement;null!==m?w.setAttribute(N,u,m):w.removeAttribute(N,u)}get urlTree(){return null===this.commands?null:this.router.createUrlTree(this.commands,{relativeTo:void 0!==this.relativeTo?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(m){return new(m||f)(c.rXU(Gt),c.rXU(Ur),c.kS0("tabindex"),c.rXU(c.sFG),c.rXU(c.aKT),c.rXU(dn.hb))}}static{this.\u0275dir=c.FsC({type:f,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(m,w){1&m&&c.bIt("click",function(F){return w.onClick(F.button,F.ctrlKey,F.shiftKey,F.altKey,F.metaKey)}),2&m&&c.BMQ("target",w.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",relativeTo:"relativeTo",preserveFragment:["preserveFragment","preserveFragment",c.L39],skipLocationChange:["skipLocationChange","skipLocationChange",c.L39],replaceUrl:["replaceUrl","replaceUrl",c.L39],routerLink:"routerLink"},standalone:!0,features:[c.GFd,c.OA$]})}}return f})(),Er=(()=>{class f{get isActive(){return this._isActive}constructor(u,m,w,N,F){this.router=u,this.element=m,this.renderer=w,this.cdr=N,this.link=F,this.classes=[],this._isActive=!1,this.routerLinkActiveOptions={exact:!1},this.isActiveChange=new c.bkB,this.routerEventsSubscription=u.events.subscribe(re=>{re instanceof jr&&this.update()})}ngAfterContentInit(){(0,ne.of)(this.links.changes,(0,ne.of)(null)).pipe((0,yt.U)()).subscribe(u=>{this.update(),this.subscribeToEachLinkOnChanges()})}subscribeToEachLinkOnChanges(){this.linkInputChangesSubscription?.unsubscribe();const u=[...this.links.toArray(),this.link].filter(m=>!!m).map(m=>m.onChanges);this.linkInputChangesSubscription=(0,X.H)(u).pipe((0,yt.U)()).subscribe(m=>{this._isActive!==this.isLinkActive(this.router)(m)&&this.update()})}set routerLinkActive(u){const m=Array.isArray(u)?u:u.split(" ");this.classes=m.filter(w=>!!w)}ngOnChanges(u){this.update()}ngOnDestroy(){this.routerEventsSubscription.unsubscribe(),this.linkInputChangesSubscription?.unsubscribe()}update(){!this.links||!this.router.navigated||queueMicrotask(()=>{const u=this.hasActiveLinks();this._isActive!==u&&(this._isActive=u,this.cdr.markForCheck(),this.classes.forEach(m=>{u?this.renderer.addClass(this.element.nativeElement,m):this.renderer.removeClass(this.element.nativeElement,m)}),u&&void 0!==this.ariaCurrentWhenActive?this.renderer.setAttribute(this.element.nativeElement,"aria-current",this.ariaCurrentWhenActive.toString()):this.renderer.removeAttribute(this.element.nativeElement,"aria-current"),this.isActiveChange.emit(u))})}isLinkActive(u){const m=function Nn(f){return!!f.paths}(this.routerLinkActiveOptions)?this.routerLinkActiveOptions:this.routerLinkActiveOptions.exact||!1;return w=>!!w.urlTree&&u.isActive(w.urlTree,m)}hasActiveLinks(){const u=this.isLinkActive(this.router);return this.link&&u(this.link)||this.links.some(u)}static{this.\u0275fac=function(m){return new(m||f)(c.rXU(Gt),c.rXU(c.aKT),c.rXU(c.sFG),c.rXU(c.gRc),c.rXU(Wn,8))}}static{this.\u0275dir=c.FsC({type:f,selectors:[["","routerLinkActive",""]],contentQueries:function(m,w,N){if(1&m&&c.wni(N,Wn,5),2&m){let F;c.mGM(F=c.lsd())&&(w.links=F)}},inputs:{routerLinkActiveOptions:"routerLinkActiveOptions",ariaCurrentWhenActive:"ariaCurrentWhenActive",routerLinkActive:"routerLinkActive"},outputs:{isActiveChange:"isActiveChange"},exportAs:["routerLinkActive"],standalone:!0,features:[c.OA$]})}}return f})();class wi{}let xr=(()=>{class f{constructor(u,m,w,N,F){this.router=u,this.injector=w,this.preloadingStrategy=N,this.loader=F}setUpPreloading(){this.subscription=this.router.events.pipe((0,Z.p)(u=>u instanceof jr),(0,Ge.H)(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(u,m){const w=[];for(const N of m){N.providers&&!N._injector&&(N._injector=(0,c.Ol2)(N.providers,u,`Route: ${N.path}`));const F=N._injector??u,re=N._loadedInjector??F;(N.loadChildren&&!N._loadedRoutes&&void 0===N.canLoad||N.loadComponent&&!N._loadedComponent)&&w.push(this.preloadConfig(F,N)),(N.children||N._loadedRoutes)&&w.push(this.processRoutes(re,N.children??N._loadedRoutes))}return(0,X.H)(w).pipe((0,yt.U)())}preloadConfig(u,m){return this.preloadingStrategy.preload(m,()=>{let w;w=m.loadChildren&&void 0===m.canLoad?this.loader.loadChildren(u,m):(0,ne.of)(null);const N=w.pipe((0,te.Z)(F=>null===F?(0,ne.of)(void 0):(m._loadedRoutes=F.routes,m._loadedInjector=F.injector,this.processRoutes(F.injector??u,F.routes))));if(m.loadComponent&&!m._loadedComponent){const F=this.loader.loadComponent(m);return(0,X.H)([N,F]).pipe((0,yt.U)())}return N})}static{this.\u0275fac=function(m){return new(m||f)(c.KVO(Gt),c.KVO(c.Ql9),c.KVO(c.uvJ),c.KVO(wi),c.KVO(_))}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac,providedIn:"root"})}}return f})();const Lo=new c.nKC("");let cs=(()=>{class f{constructor(u,m,w,N,F={}){this.urlSerializer=u,this.transitions=m,this.viewportScroller=w,this.zone=N,this.options=F,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},F.scrollPositionRestoration=F.scrollPositionRestoration||"disabled",F.anchorScrolling=F.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof qi?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=u.navigationTrigger,this.restoredId=u.restoredState?u.restoredState.navigationId:0):u instanceof jr?(this.lastId=u.id,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.urlAfterRedirects).fragment)):u instanceof Hn&&0===u.code&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(u,this.urlSerializer.parse(u.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(u=>{u instanceof yi&&(u.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(u.position):u.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(u.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(u,m){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new yi(u,"popstate"===this.lastSource?this.store[this.restoredId]:null,m))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(m){c.QTQ()}}static{this.\u0275prov=c.jDH({token:f,factory:f.\u0275fac})}}return f})();function it(f,g){return{\u0275kind:f,\u0275providers:g}}function Hi(){const f=(0,c.WQX)(c.zZn);return g=>{const u=f.get(c.o8S);if(g!==u.components[0])return;const m=f.get(Gt),w=f.get(Cn);1===f.get(Ko)&&m.initialNavigation(),f.get($r,null,c.$GK.Optional)?.setUpPreloading(),f.get(Lo,null,c.$GK.Optional)?.init(),m.resetRootComponentType(u.componentTypes[0]),w.closed||(w.next(),w.complete(),w.unsubscribe())}}const Cn=new c.nKC("",{factory:()=>new gt.B}),Ko=new c.nKC("",{providedIn:"root",factory:()=>1}),$r=new c.nKC("");function Xo(f){return it(0,[{provide:$r,useExisting:xr},{provide:wi,useExisting:f}])}const va=new c.nKC("ROUTER_FORROOT_GUARD"),wr=[dn.aZ,{provide:rr,useClass:cr},Gt,Kr,{provide:Ur,useFactory:function lt(f){return f.routerState.root},deps:[Gt]},_,[]];function sl(){return new c.NEm("Router",Gt)}let ds=(()=>{class f{constructor(u){}static forRoot(u,m){return{ngModule:f,providers:[wr,[],{provide:on,multi:!0,useValue:u},{provide:va,useFactory:Mu,deps:[[Gt,new c.Xx1,new c.kdw]]},{provide:He,useValue:m||{}},m?.useHash?{provide:dn.hb,useClass:dn.fw}:{provide:dn.hb,useClass:dn.Sm},{provide:Lo,useFactory:()=>{const f=(0,c.WQX)(dn.Xr),g=(0,c.WQX)(c.SKi),u=(0,c.WQX)(He),m=(0,c.WQX)(M),w=(0,c.WQX)(rr);return u.scrollOffset&&f.setOffset(u.scrollOffset),new cs(w,m,f,g,u)}},m?.preloadingStrategy?Xo(m.preloadingStrategy).\u0275providers:[],{provide:c.NEm,multi:!0,useFactory:sl},m?.initialNavigation?Zd(m):[],m?.bindToComponentInputs?it(8,[Nr,{provide:Or,useExisting:Nr}]).\u0275providers:[],[{provide:_a,useFactory:Hi},{provide:c.iLQ,multi:!0,useExisting:_a}]]}}static forChild(u){return{ngModule:f,providers:[{provide:on,multi:!0,useValue:u}]}}static{this.\u0275fac=function(m){return new(m||f)(c.KVO(va,8))}}static{this.\u0275mod=c.$C({type:f})}static{this.\u0275inj=c.G2t({})}}return f})();function Mu(f){return"guarded"}function Zd(f){return["disabled"===f.initialNavigation?it(3,[{provide:c.hnV,multi:!0,useFactory:()=>{const g=(0,c.WQX)(Gt);return()=>{g.setUpLocationChangeListener()}}},{provide:Ko,useValue:2}]).\u0275providers:[],"enabledBlocking"===f.initialNavigation?it(2,[{provide:Ko,useValue:0},{provide:c.hnV,multi:!0,deps:[c.zZn],useFactory:g=>{const u=g.get(dn.hj,Promise.resolve());return()=>u.then(()=>new Promise(m=>{const w=g.get(Gt),N=g.get(Cn);at(w,()=>{m(!0)}),g.get(M).afterPreactivation=()=>(m(!0),N.closed?(0,ne.of)(void 0):N),w.initialNavigation()}))}}]).\u0275providers:[]]}const _a=new c.nKC("")},467:(qe,ye,O)=>{function c(ce,se,X,ne,ee,de,le){try{var fe=ce[de](le),H=fe.value}catch(G){return void X(G)}fe.done?se(H):Promise.resolve(H).then(ne,ee)}function C(ce){return function(){var se=this,X=arguments;return new Promise(function(ne,ee){var de=ce.apply(se,X);function le(H){c(de,ne,ee,le,fe,"next",H)}function fe(H){c(de,ne,ee,le,fe,"throw",H)}le(void 0)})}}O.d(ye,{A:()=>C})},1635:(qe,ye,O)=>{function G(Z,te,ie,ae){return new(ie||(ie=Promise))(function(be,Ge){function ft(xt){try{ut(ae.next(xt))}catch(Qt){Ge(Qt)}}function Bt(xt){try{ut(ae.throw(xt))}catch(Qt){Ge(Qt)}}function ut(xt){xt.done?be(xt.value):function Ie(be){return be instanceof ie?be:new ie(function(Ge){Ge(be)})}(xt.value).then(ft,Bt)}ut((ae=ae.apply(Z,te||[])).next())})}function xe(Z){return this instanceof xe?(this.v=Z,this):new xe(Z)}function Ne(Z,te,ie){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var Ie,ae=ie.apply(Z,te||[]),be=[];return Ie=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),ft("next"),ft("throw"),ft("return",function Ge(Ae){return function(hn){return Promise.resolve(hn).then(Ae,Qt)}}),Ie[Symbol.asyncIterator]=function(){return this},Ie;function ft(Ae,hn){ae[Ae]&&(Ie[Ae]=function(xn){return new Promise(function(tr,Re){be.push([Ae,xn,tr,Re])>1||Bt(Ae,xn)})},hn&&(Ie[Ae]=hn(Ie[Ae])))}function Bt(Ae,hn){try{!function ut(Ae){Ae.value instanceof xe?Promise.resolve(Ae.value.v).then(xt,Qt):tn(be[0][2],Ae)}(ae[Ae](hn))}catch(xn){tn(be[0][3],xn)}}function xt(Ae){Bt("next",Ae)}function Qt(Ae){Bt("throw",Ae)}function tn(Ae,hn){Ae(hn),be.shift(),be.length&&Bt(be[0][0],be[0][1])}}function we(Z){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var ie,te=Z[Symbol.asyncIterator];return te?te.call(Z):(Z=function Vt(Z){var te="function"==typeof Symbol&&Symbol.iterator,ie=te&&Z[te],ae=0;if(ie)return ie.call(Z);if(Z&&"number"==typeof Z.length)return{next:function(){return Z&&ae>=Z.length&&(Z=void 0),{value:Z&&Z[ae++],done:!Z}}};throw new TypeError(te?"Object is not iterable.":"Symbol.iterator is not defined.")}(Z),ie={},ae("next"),ae("throw"),ae("return"),ie[Symbol.asyncIterator]=function(){return this},ie);function ae(be){ie[be]=Z[be]&&function(Ge){return new Promise(function(ft,Bt){!function Ie(be,Ge,ft,Bt){Promise.resolve(Bt).then(function(ut){be({value:ut,done:ft})},Ge)}(ft,Bt,(Ge=Z[be](Ge)).done,Ge.value)})}}}O.d(ye,{AQ:()=>Ne,N3:()=>xe,sH:()=>G,xN:()=>we}),"function"==typeof SuppressedError&&SuppressedError}},qe=>{qe(qe.s=1413)}]);