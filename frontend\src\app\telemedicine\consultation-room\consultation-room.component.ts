import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ElementRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { VideoConsultationService, VideoConsultation } from '../../core/services/video-consultation.service';
import { WebRTCService } from '../../core/services/webrtc.service';
import { ChatService } from '../../core/services/chat.service';
import { PresenceService } from '../../core/services/presence.service';
import { AuthService } from '../../core/services/auth.service';
import { NotificationService } from '../../core/services/notification.service';
import { CallRecordingService } from '../../core/services/call-recording.service';

@Component({
  selector: 'app-consultation-room',
  templateUrl: './consultation-room.component.html',
  styleUrls: ['./consultation-room.component.scss']
})
export class ConsultationRoomComponent implements <PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy {
  @ViewChild('localVideo', { static: false }) localVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('remoteVideo', { static: false }) remoteVideo!: ElementRef<HTMLVideoElement>;

  consultation: VideoConsultation | null = null;
  roomId: string = '';
  currentUser: any;
  
  // Video call state
  isVideoEnabled = true;
  isAudioEnabled = true;
  isScreenSharing = false;
  isCallActive = false;
  isConnecting = false;
  
  // UI state
  isChatOpen = false;
  isControlsVisible = true;
  showParticipants = false;

  // Recording state
  isRecording = false;
  recordingDuration = 0;
  recordingConsent = false;
  
  // Chat
  messages: any[] = [];
  newMessage = '';
  
  // Error handling
  error: string | null = null;
  connectionStatus = 'Connecting...';
  
  private subscriptions: Subscription[] = [];
  private controlsTimeout: any;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private videoConsultationService: VideoConsultationService,
    private webRTCService: WebRTCService,
    private chatService: ChatService,
    private presenceService: PresenceService,
    private authService: AuthService,
    private notificationService: NotificationService,
    private recordingService: CallRecordingService
  ) {
    this.currentUser = this.authService.getCurrentUser();
  }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.roomId = params['roomId'];
      if (this.roomId) {
        this.initializeConsultation();
      }
    });

    // Auto-hide controls after 5 seconds of inactivity
    this.resetControlsTimeout();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.webRTCService.leaveRoom();
    if (this.controlsTimeout) {
      clearTimeout(this.controlsTimeout);
    }
  }

  public async initializeConsultation(): Promise<void> {
    try {
      this.isConnecting = true;
      this.connectionStatus = 'Loading consultation...';

      // Get consultation details
      const sub = this.videoConsultationService.getConsultationByRoomId(this.roomId).subscribe({
        next: (consultation) => {
          this.consultation = consultation;
          this.continueInitialization();
        },
        error: (error) => {
          console.error('Failed to get consultation:', error);
          this.error = 'Failed to load consultation details';
          this.isConnecting = false;
        }
      });
      this.subscriptions.push(sub);
    } catch (error) {
      console.error('Failed to initialize consultation:', error);
      this.error = 'Failed to join the consultation. Please try again.';
      this.isConnecting = false;
    }
  }

  private async continueInitialization(): Promise<void> {
    try {
      // Check if user is authorized
      if (!this.isAuthorizedUser()) {
        this.error = 'You are not authorized to join this consultation';
        return;
      }

      // Initialize WebRTC
      this.connectionStatus = 'Connecting to video call...';
      await this.initializeWebRTC();

      this.isConnecting = false;
      this.isCallActive = true;
      this.connectionStatus = 'Connected';
    } catch (error) {
      console.error('Failed to continue initialization:', error);
      this.error = 'Failed to initialize video consultation';
      this.isConnecting = false;
    }
  }

  private async initializeWebRTC(): Promise<void> {
    try {
      // Set user as busy during video call
      this.presenceService.updatePresence('BUSY', 'In video consultation');

      // Initialize WebRTC with STOMP client
      const stompClient = this.chatService['stompClient']; // Access private property
      await this.webRTCService.initializeWebRTC(
        this.roomId,
        this.currentUser.id,
        this.currentUser.role.toLowerCase(),
        stompClient
      );

      // Initialize local media
      await this.webRTCService.initializeLocalMedia();

      // Join the room
      await this.webRTCService.joinRoom(this.roomId, this.currentUser.id, this.currentUser.role.toLowerCase());

      // Set up event listeners
      this.setupWebRTCEventListeners();

      // Display local video
      if (this.localVideo) {
        this.localVideo.nativeElement.srcObject = this.webRTCService.getLocalStreamValue();
      }

      // Add connection timeout
      setTimeout(() => {
        if (this.connectionStatus === 'Connecting...') {
          this.connectionStatus = 'Connection timeout. Please check your internet connection.';
          this.error = 'Connection timeout. Please try again.';
        }
      }, 30000); // 30 second timeout

    } catch (error) {
      console.error('WebRTC initialization failed:', error);
      this.presenceService.updatePresence('ONLINE'); // Reset presence on error

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes('Permission denied')) {
          this.error = 'Camera/microphone access denied. Please allow permissions and try again.';
        } else if (error.message.includes('NotFoundError')) {
          this.error = 'No camera or microphone found. Please check your devices.';
        } else {
          this.error = error.message || 'Failed to initialize video call';
        }
      } else {
        this.error = 'Failed to initialize video call. Please try again.';
      }

      throw error;
    }
  }

  private setupWebRTCEventListeners(): void {
    // Remote stream received
    this.webRTCService.onRemoteStream().subscribe(stream => {
      if (this.remoteVideo) {
        this.remoteVideo.nativeElement.srcObject = stream;
      }
    });

    // Connection state changes
    this.webRTCService.onConnectionStateChange().subscribe(state => {
      this.connectionStatus = state;
      if (state === 'disconnected' || state === 'failed') {
        this.handleConnectionError();
      }
    });

    // Participant events
    this.webRTCService.onParticipantJoined().subscribe(participant => {
      this.notificationService.addNotification({
        type: 'system',
        title: 'Participant Joined',
        message: `${participant.name} joined the consultation`,
        priority: 'low'
      });
    });

    this.webRTCService.onParticipantLeft().subscribe(participant => {
      this.notificationService.addNotification({
        type: 'system',
        title: 'Participant Left',
        message: `${participant.name} left the consultation`,
        priority: 'low'
      });
    });
  }

  private isAuthorizedUser(): boolean {
    if (!this.consultation) return false;
    
    return this.consultation.doctor.id === this.currentUser.id || 
           this.consultation.patient.id === this.currentUser.id;
  }

  private handleConnectionError(): void {
    this.error = 'Connection lost. Attempting to reconnect...';
    // Implement reconnection logic here
  }

  // Video controls
  toggleVideo(): void {
    this.isVideoEnabled = !this.isVideoEnabled;
    this.webRTCService.toggleVideo(this.isVideoEnabled);
    this.resetControlsTimeout();
  }

  toggleAudio(): void {
    this.isAudioEnabled = !this.isAudioEnabled;
    this.webRTCService.toggleAudio(this.isAudioEnabled);
    this.resetControlsTimeout();
  }

  async toggleScreenShare(): Promise<void> {
    try {
      if (this.isScreenSharing) {
        await this.webRTCService.stopScreenShare();
        this.isScreenSharing = false;
      } else {
        await this.webRTCService.startScreenShare();
        this.isScreenSharing = true;
      }
      this.resetControlsTimeout();
    } catch (error) {
      console.error('Screen share toggle failed:', error);
      this.notificationService.addNotification({
        type: 'system',
        title: 'Screen Share Error',
        message: 'Failed to toggle screen sharing',
        priority: 'medium'
      });
    }
  }

  // UI controls
  toggleChat(): void {
    this.isChatOpen = !this.isChatOpen;
    if (this.isChatOpen) {
      this.loadChatMessages();
    }
  }

  toggleParticipants(): void {
    this.showParticipants = !this.showParticipants;
  }

  onMouseMove(): void {
    this.isControlsVisible = true;
    this.resetControlsTimeout();
  }

  private resetControlsTimeout(): void {
    if (this.controlsTimeout) {
      clearTimeout(this.controlsTimeout);
    }
    this.controlsTimeout = setTimeout(() => {
      this.isControlsVisible = false;
    }, 5000);
  }

  // Chat functionality
  private loadChatMessages(): void {
    if (!this.consultation?.appointment?.id) return;

    // For now, initialize empty messages array
    // In a full implementation, this would load appointment-specific chat
    this.messages = [];

    // Subscribe to new messages
    const sub = this.chatService.messages$.subscribe({
      next: (message: any) => {
        this.messages.push(message);
      },
      error: (error: any) => {
        console.error('Failed to load chat messages:', error);
      }
    });
    this.subscriptions.push(sub);
  }

  sendMessage(): void {
    if (!this.newMessage.trim()) return;

    // For video consultation chat, we'll use a simple approach
    // In a full implementation, this would integrate with the chat system
    const message = {
      id: Date.now(),
      content: this.newMessage.trim(),
      sender: this.currentUser,
      timestamp: new Date().toISOString(),
      status: 'SENT'
    };

    this.messages.push(message);
    this.newMessage = '';

    // In a real implementation, this would send via WebSocket or HTTP
    console.log('Video consultation message sent:', message);
  }

  // End consultation
  async endConsultation(): Promise<void> {
    if (this.currentUser.role !== 'DOCTOR') {
      this.leaveConsultation();
      return;
    }

    const confirmed = confirm('Are you sure you want to end this consultation?');
    if (!confirmed) return;

    try {
      // End WebRTC session
      this.isCallActive = false;
      this.webRTCService.endSession();

      // Reset presence to online
      this.presenceService.updatePresence('ONLINE');

      if (this.consultation?.id) {
        const sub = this.videoConsultationService.endConsultation(
          this.consultation.id,
          '', // notes - could be collected via modal
          '', // diagnosis
          ''  // recommendations
        ).subscribe({
          next: () => {
            this.notificationService.addNotification({
              type: 'system',
              title: 'Consultation Ended',
              message: 'The consultation has been ended successfully.',
              priority: 'medium'
            });
            this.router.navigate(['/telemedicine/consultations']);
          },
          error: (error) => {
            console.error('Failed to end consultation:', error);
            this.notificationService.addNotification({
              type: 'system',
              title: 'Error',
              message: 'Failed to end the consultation.',
              priority: 'high'
            });
          }
        });
        this.subscriptions.push(sub);
      }
    } catch (error) {
      console.error('Failed to end consultation:', error);
    }
  }

  leaveConsultation(): void {
    const confirmed = confirm('Are you sure you want to leave this consultation?');
    if (confirmed) {
      // End WebRTC session
      this.isCallActive = false;
      this.webRTCService.leaveRoom();

      // Reset presence to online
      this.presenceService.updatePresence('ONLINE');

      this.router.navigate(['/telemedicine/consultations']);
    }
  }

  formatTime(timestamp: string): string {
    return new Date(timestamp).toLocaleTimeString();
  }

  // Recording functionality
  async toggleRecording(): Promise<void> {
    if (!this.isRecording) {
      await this.startRecording();
    } else {
      await this.stopRecording();
    }
  }

  private async startRecording(): Promise<void> {
    try {
      // Check if user has consent to record
      if (!this.recordingConsent) {
        const consent = confirm('Do you consent to recording this consultation? The recording will be saved for medical records.');
        if (!consent) {
          return;
        }
        this.recordingConsent = true;
      }

      // Get the combined stream (local + remote)
      const localStream = this.webRTCService.getLocalStreamValue();
      if (!localStream) {
        throw new Error('No local stream available for recording');
      }

      await this.recordingService.startRecording(localStream);
      this.isRecording = true;

      // Subscribe to recording duration
      this.recordingService.getRecordingDuration().subscribe(duration => {
        this.recordingDuration = duration;
      });

      this.notificationService.addNotification({
        type: 'system',
        title: 'Recording Started',
        message: 'Consultation recording has started',
        priority: 'medium'
      });

    } catch (error) {
      console.error('Failed to start recording:', error);
      this.notificationService.addNotification({
        type: 'system',
        title: 'Recording Failed',
        message: 'Failed to start recording. Please try again.',
        priority: 'high'
      });
    }
  }

  private async stopRecording(): Promise<void> {
    try {
      const recordingBlob = await this.recordingService.stopRecording();
      this.isRecording = false;
      this.recordingDuration = 0;

      // Convert to base64 for upload
      const base64Data = await this.recordingService.blobToBase64(recordingBlob);

      // Here you would typically upload to your backend
      // For now, we'll just download it locally
      const filename = `consultation-${this.roomId}-${new Date().toISOString().split('T')[0]}.webm`;
      this.recordingService.downloadRecording(recordingBlob, filename);

      this.notificationService.addNotification({
        type: 'system',
        title: 'Recording Saved',
        message: 'Consultation recording has been saved',
        priority: 'medium'
      });

    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.notificationService.addNotification({
        type: 'system',
        title: 'Recording Error',
        message: 'Failed to save recording. Please try again.',
        priority: 'high'
      });
    }
  }

  formatRecordingDuration(): string {
    return this.recordingService.formatDuration(this.recordingDuration);
  }
}
