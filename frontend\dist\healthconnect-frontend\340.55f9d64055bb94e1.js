"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[340],{340:(_t,p,s)=>{s.r(p),s.d(p,{PatientModule:()=>Ct});var m=s(2434),f=s(3887),C=s(1713),t=s(6276),h=s(8010),_=s(3443),u=s(9545),v=s(5538),d=s(177),b=s(3285);function P(n,a){if(1&n&&(t.j41(0,"span",30),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ",e.lastMessage.content.length>30?e.lastMessage.content.substring(0,30)+"...":e.lastMessage.content," ")}}function M(n,a){1&n&&(t.j41(0,"span",30),t.EFF(1,"No messages yet"),t.k0s())}function k(n,a){if(1&n&&(t.j41(0,"div",32),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ",e.unreadCount," ")}}function x(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",22),t.bIt("click",function(){const r=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.navigateToChat(r.id))}),t.j41(1,"div",23),t.nrm(2,"img",24),t.k0s(),t.j41(3,"div",25)(4,"div",26),t.EFF(5),t.k0s(),t.j41(6,"div",27),t.DNE(7,P,2,1,"span",28),t.DNE(8,M,2,0,"span",28),t.k0s()(),t.j41(9,"div",29)(10,"small",30),t.EFF(11),t.nI1(12,"date"),t.k0s(),t.DNE(13,k,2,1,"div",31),t.k0s()()}if(2&n){const e=a.$implicit,i=t.XpG(2);let o,r,c;t.R7$(2),t.Y8G("src",(null==(o=i.getOtherParticipant(e))?null:o.avatar)||"/assets/images/default-avatar.png",t.B4B)("alt",null==(r=i.getOtherParticipant(e))?null:r.fullName),t.R7$(3),t.JRh(null==(c=i.getOtherParticipant(e))?null:c.fullName),t.R7$(2),t.Y8G("ngIf",e.lastMessage),t.R7$(1),t.Y8G("ngIf",!e.lastMessage),t.R7$(3),t.SpI(" ",t.i5U(12,7,e.updatedAt,"short")," "),t.R7$(2),t.Y8G("ngIf",e.unreadCount>0)}}function O(n,a){if(1&n&&(t.j41(0,"div",17)(1,"h6",18),t.nrm(2,"i",19),t.EFF(3,"Recent Conversations "),t.k0s(),t.j41(4,"div",20),t.DNE(5,x,14,10,"div",21),t.k0s()()),2&n){const e=t.XpG();t.R7$(5),t.Y8G("ngForOf",e.recentChats)}}const y=function(n,a){return{appointmentId:n,doctorId:a,chatType:"PRE_APPOINTMENT",buttonText:"Pre-Chat",buttonClass:"btn-outline-info",size:"sm",showIcon:!1}},F=function(n){return{doctorId:n,chatType:"GENERAL",buttonText:"General",buttonClass:"btn-outline-primary",size:"sm",showIcon:!1}};function w(n,a){if(1&n&&(t.j41(0,"div",36)(1,"div",37)(2,"div",38)(3,"strong"),t.EFF(4),t.k0s()(),t.j41(5,"div",39)(6,"small",30),t.EFF(7),t.nI1(8,"date"),t.k0s()(),t.j41(9,"div",40)(10,"span",41),t.EFF(11),t.k0s()()(),t.j41(12,"div",42)(13,"div",43),t.nrm(14,"app-chat-access",44)(15,"app-chat-access",44),t.k0s()()()),2&n){const e=a.$implicit,i=t.XpG(2);t.R7$(4),t.JRh(e.doctor.fullName),t.R7$(3),t.Lme(" ",t.i5U(8,6,e.date,"mediumDate")," at ",e.startTime," "),t.R7$(4),t.SpI(" In ",i.getTimeUntilAppointment(e)," "),t.R7$(3),t.Y8G("config",t.l_i(9,y,e.id,e.doctor.id)),t.R7$(1),t.Y8G("config",t.eq3(12,F,e.doctor.id))}}function j(n,a){if(1&n&&(t.j41(0,"div")(1,"h6",18),t.nrm(2,"i",33),t.EFF(3,"Upcoming Appointments "),t.k0s(),t.j41(4,"div",34),t.DNE(5,w,16,14,"div",35),t.k0s()()),2&n){const e=t.XpG();t.R7$(5),t.Y8G("ngForOf",e.upcomingAppointments)}}function $(n,a){1&n&&(t.j41(0,"div",45)(1,"div",46),t.nrm(2,"i",47),t.j41(3,"h6",30),t.EFF(4,"No recent conversations"),t.k0s(),t.j41(5,"p",18),t.EFF(6,"Start chatting with your healthcare providers"),t.k0s(),t.j41(7,"button",48),t.nrm(8,"i",49),t.EFF(9," Book Appointment "),t.k0s()()())}const R=function(){return{chatType:"URGENT",buttonText:"Urgent",buttonClass:"btn-outline-danger",size:"sm"}};let E=(()=>{class n{constructor(e,i,o,r){this.chatService=e,this.appointmentService=i,this.authService=o,this.router=r,this.recentChats=[],this.upcomingAppointments=[],this.loading=!1}ngOnInit(){this.currentUser=this.authService.getCurrentUser(),this.loadRecentChats(),this.loadUpcomingAppointments()}loadRecentChats(){this.chatService.getUserChats().subscribe({next:e=>{this.recentChats=e.slice(0,3)},error:e=>{console.error("Error loading recent chats:",e)}})}loadUpcomingAppointments(){"PATIENT"===this.currentUser?.role&&this.appointmentService.getPatientAppointments().subscribe({next:e=>{const i=new Date;this.upcomingAppointments=e.filter(o=>new Date(`${o.date}T${o.startTime}`)>i).slice(0,3)},error:e=>{console.error("Error loading appointments:",e)}})}navigateToChat(e){e?this.router.navigate(["/chat"],{queryParams:{chatId:e}}):this.router.navigate(["/chat"])}startAppointmentChat(e,i){this.appointmentService.createAppointmentChat(e.id,"PATIENT"===this.currentUser.role?e.doctor.id:e.patient.id,i,`${i.replace("_"," ")} discussion for appointment on ${e.date}`).subscribe({next:r=>{this.router.navigate(["/chat"],{queryParams:{chatId:r.id,appointmentId:e.id}})},error:r=>{console.error("Error creating appointment chat:",r)}})}isBeforeAppointment(e){return new Date(`${e.date}T${e.startTime}`)>new Date}getTimeUntilAppointment(e){const i=new Date(`${e.date}T${e.startTime}`),o=new Date,r=i.getTime()-o.getTime(),c=Math.floor(r/36e5),g=Math.floor(c/24);return g>0?`${g} day${g>1?"s":""}`:c>0?`${c} hour${c>1?"s":""}`:"Soon"}getOtherParticipant(e){return"PATIENT"===this.currentUser.role?e.doctor:e.patient}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(v.m),t.rXU(u.h),t.rXU(h.u),t.rXU(m.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-quick-chat-widget"]],decls:21,vars:5,consts:[[1,"quick-chat-widget"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"fas","fa-comments","text-primary","me-2"],["type","button",1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"fas","fa-external-link-alt","me-1"],[1,"card-body"],["class","mb-4",4,"ngIf"],[4,"ngIf"],["class","no-data",4,"ngIf"],[1,"quick-actions","mt-3","pt-3","border-top"],[1,"row","g-2"],[1,"col-6"],["type","button",1,"btn","btn-outline-success","btn-sm","w-100",3,"click"],[1,"fas","fa-plus","me-1"],[1,"w-100",3,"config"],[1,"mb-4"],[1,"text-muted","mb-3"],[1,"fas","fa-clock","me-2"],[1,"chat-list"],["class","chat-item",3,"click",4,"ngFor","ngForOf"],[1,"chat-item",3,"click"],[1,"chat-avatar"],[1,"rounded-circle",3,"src","alt"],[1,"chat-info"],[1,"chat-name"],[1,"chat-preview"],["class","text-muted",4,"ngIf"],[1,"chat-meta"],[1,"text-muted"],["class","badge bg-primary",4,"ngIf"],[1,"badge","bg-primary"],[1,"fas","fa-calendar-alt","me-2"],[1,"appointment-list"],["class","appointment-item",4,"ngFor","ngForOf"],[1,"appointment-item"],[1,"appointment-info"],[1,"appointment-doctor"],[1,"appointment-details"],[1,"appointment-time-left"],[1,"badge","bg-info"],[1,"appointment-actions"],["role","group",1,"btn-group-vertical"],[3,"config"],[1,"no-data"],[1,"text-center","py-4"],[1,"fas","fa-comments","fa-3x","text-muted","mb-3"],["type","button","routerLink","/appointments/book",1,"btn","btn-primary","btn-sm"],[1,"fas","fa-calendar-plus","me-2"]],template:function(i,o){1&i&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"h6",3),t.nrm(4,"i",4),t.EFF(5," Quick Chat Access "),t.k0s(),t.j41(6,"button",5),t.bIt("click",function(){return o.navigateToChat()}),t.nrm(7,"i",6),t.EFF(8," View All "),t.k0s()(),t.j41(9,"div",7),t.DNE(10,O,6,1,"div",8),t.DNE(11,j,6,1,"div",9),t.DNE(12,$,10,0,"div",10),t.j41(13,"div",11)(14,"div",12)(15,"div",13)(16,"button",14),t.bIt("click",function(){return o.navigateToChat()}),t.nrm(17,"i",15),t.EFF(18," New Chat "),t.k0s()(),t.j41(19,"div",13),t.nrm(20,"app-chat-access",16),t.k0s()()()()()()),2&i&&(t.R7$(10),t.Y8G("ngIf",o.recentChats.length>0),t.R7$(1),t.Y8G("ngIf",o.upcomingAppointments.length>0),t.R7$(1),t.Y8G("ngIf",0===o.recentChats.length&&0===o.upcomingAppointments.length),t.R7$(8),t.Y8G("config",t.lJ4(4,R)))},dependencies:[d.Sq,d.bT,m.Wk,b.Q,d.vh],styles:[".quick-chat-widget[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%]{border:none;box-shadow:0 2px 8px #0000001a;border-radius:12px;overflow:hidden}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{background:linear-gradient(135deg,#007bff 0%,#0056b3 100%);color:#fff;border-bottom:none;padding:1rem 1.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]{border-color:#ffffff80;color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]   .btn-outline-primary[_ngcontent-%COMP%]:hover{background-color:#ffffff1a;border-color:#fff}.quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{display:flex;align-items:center;padding:.75rem;border-radius:8px;cursor:pointer;transition:all .2s ease;margin-bottom:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;transform:translate(2px)}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]{flex-shrink:0;margin-right:.75rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:cover}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]{flex:1;min-width:0}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-name[_ngcontent-%COMP%]{font-weight:500;color:#212529;margin-bottom:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-info[_ngcontent-%COMP%]   .chat-preview[_ngcontent-%COMP%]{font-size:.875rem;color:#6c757d;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]{flex-shrink:0;text-align:right}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-meta[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem;margin-top:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;padding:1rem;border:1px solid #e9ecef;border-radius:8px;margin-bottom:.75rem;background:#f8f9fa}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]{flex:1}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-doctor[_ngcontent-%COMP%]{margin-bottom:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-details[_ngcontent-%COMP%]{margin-bottom:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-info[_ngcontent-%COMP%]   .appointment-time-left[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%]{font-size:.75rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]{flex-shrink:0;margin-left:1rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{margin-bottom:.25rem;font-size:.75rem;padding:.25rem .5rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-bottom:0}.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]{text-align:center;padding:2rem 1rem}.quick-chat-widget[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.quick-chat-widget[_ngcontent-%COMP%]   .quick-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem;padding:.5rem .75rem}.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%]{font-size:.875rem;font-weight:600;text-transform:uppercase;letter-spacing:.5px;margin-bottom:1rem}.quick-chat-widget[_ngcontent-%COMP%]   h6.text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.7}@media (max-width: 768px){.quick-chat-widget[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]{padding:.5rem}.quick-chat-widget[_ngcontent-%COMP%]   .chat-list[_ngcontent-%COMP%]   .chat-item[_ngcontent-%COMP%]   .chat-avatar[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;height:32px}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]{margin-left:0;margin-top:.75rem;width:100%}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]{flex-direction:row;width:100%}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{flex:1;margin-bottom:0;margin-right:.25rem}.quick-chat-widget[_ngcontent-%COMP%]   .appointment-list[_ngcontent-%COMP%]   .appointment-item[_ngcontent-%COMP%]   .appointment-actions[_ngcontent-%COMP%]   .btn-group-vertical[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:last-child{margin-right:0}}.quick-chat-widget[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_slideInUp .3s ease-out}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}"]})}}return n})();var I=s(3794),S=s(6977),T=s(980),D=s(4412),l=s(9437),G=s(6354),N=s(5312),A=s(1626);let U=(()=>{class n{constructor(e){this.http=e,this.apiUrl=`${N.c.apiUrl}/api/insurance`,this.currentCoverage$=new D.t(null)}checkEligibility(e){return this.http.get(`${this.apiUrl}/eligibility/${e}`).pipe((0,l.W)(i=>{throw console.error("Error checking insurance eligibility:",i),i}))}checkPatientEligibility(e,i){return this.http.get(`${this.apiUrl}/eligibility/${e}/patient/${i}`).pipe((0,l.W)(o=>{throw console.error("Error checking patient insurance eligibility:",o),o}))}getSupportedProviders(){return this.http.get(`${this.apiUrl}/providers`).pipe((0,l.W)(e=>{throw console.error("Error getting supported providers:",e),e}))}verifyCoverage(e,i){const o={serviceType:e};return i&&(o.patientId=i),this.http.post(`${this.apiUrl}/verify-coverage`,o).pipe((0,l.W)(r=>{throw console.error("Error verifying coverage:",r),r}))}getCoverageSummary(){return this.http.get(`${this.apiUrl}/coverage-summary`).pipe((0,G.T)(e=>(this.currentCoverage$.next(e),e)),(0,l.W)(e=>{throw console.error("Error getting coverage summary:",e),e}))}getCurrentCoverage(){return this.currentCoverage$.asObservable()}estimateCost(e,i,o){const r={serviceType:e,baseCost:i};return o&&(r.patientId=o),this.http.post(`${this.apiUrl}/estimate-cost`,r).pipe((0,l.W)(c=>{throw console.error("Error estimating cost:",c),c}))}getProviderInfo(e){return this.http.get(`${this.apiUrl}/provider-info/${e}`).pipe((0,l.W)(i=>{throw console.error("Error getting provider info:",i),i}))}healthCheck(){return this.http.get(`${this.apiUrl}/health`).pipe((0,l.W)(e=>{throw console.error("Insurance service health check failed:",e),e}))}formatCoverage(e){return`${Math.round(100*e)}%`}formatCurrency(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(e)}getCoverageLevel(e){return e>=.8?"excellent":e>=.6?"good":e>=.4?"fair":e>=.2?"poor":"none"}getCoverageLevelColor(e){switch(this.getCoverageLevel(e)){case"excellent":return"success";case"good":return"info";case"fair":return"warning";case"poor":return"danger";default:return"secondary"}}isEligibleForService(e){return e.eligible&&e.coveragePercentage>0}calculateSavings(e,i){return e-i}getRecommendation(e){if(!e.eligible)return"This service is not covered by your insurance plan.";const i=e.coveragePercentage;return i>=.8?"Excellent coverage! Most costs will be covered.":i>=.6?"Good coverage. You'll have moderate out-of-pocket costs.":i>=.4?"Fair coverage. Consider budgeting for significant out-of-pocket costs.":i>0?"Limited coverage. Most costs will be out-of-pocket.":"No coverage available for this service."}getMockCoverageSummary(){return{patientId:1,patientName:"John Doe",prescriptionCoverage:{eligible:!0,coveragePercentage:.75,reason:"Coverage verified",effectiveDate:"2024-01-01T00:00:00",expirationDate:"2024-12-31T23:59:59"},consultationCoverage:{eligible:!0,coveragePercentage:.8,reason:"Coverage verified",effectiveDate:"2024-01-01T00:00:00",expirationDate:"2024-12-31T23:59:59"},appointmentCoverage:{eligible:!0,coveragePercentage:.85,reason:"Coverage verified",effectiveDate:"2024-01-01T00:00:00",expirationDate:"2024-12-31T23:59:59"},lastUpdated:Date.now()}}getMockProviders(){return[{name:"Blue Cross Blue Shield",consultationCoverage:.8,prescriptionCoverage:.7,appointmentCoverage:.9},{name:"Aetna",consultationCoverage:.75,prescriptionCoverage:.65,appointmentCoverage:.85},{name:"Cigna",consultationCoverage:.78,prescriptionCoverage:.68,appointmentCoverage:.88},{name:"United Healthcare",consultationCoverage:.82,prescriptionCoverage:.72,appointmentCoverage:.92},{name:"Humana",consultationCoverage:.76,prescriptionCoverage:.66,appointmentCoverage:.86}]}static{this.\u0275fac=function(i){return new(i||n)(t.KVO(A.Qq))}}static{this.\u0275prov=t.jDH({token:n,factory:n.\u0275fac,providedIn:"root"})}}return n})();var Y=s(7436);function X(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",5)(1,"h5",6),t.nrm(2,"i",7),t.EFF(3),t.k0s(),t.j41(4,"button",8),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.refresh())}),t.nrm(5,"i",9),t.k0s()()}if(2&n){const e=t.XpG();t.R7$(3),t.Lme(" ",e.i18nService.translateSync("insurance")," ",e.i18nService.translateSync("coverage")," "),t.R7$(1),t.Y8G("disabled",e.loading),t.R7$(1),t.AVh("fa-spin",e.loading)}}function B(n,a){if(1&n&&(t.j41(0,"div",10)(1,"div",11)(2,"span",12),t.EFF(3),t.k0s()(),t.j41(4,"p",13),t.EFF(5),t.k0s()()),2&n){const e=t.XpG();t.R7$(3),t.JRh(e.i18nService.translateSync("loading")),t.R7$(2),t.JRh(e.i18nService.translateSync("loading"))}}function L(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",14),t.nrm(1,"i",15),t.EFF(2),t.j41(3,"button",16),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.refresh())}),t.EFF(4),t.k0s()()}if(2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," "),t.R7$(2),t.SpI(" ",e.i18nService.translateSync("retry")," ")}}function H(n,a){if(1&n&&(t.j41(0,"div",34)(1,"div",35),t.nrm(2,"i",36),t.j41(3,"div")(4,"strong"),t.EFF(5),t.k0s(),t.j41(6,"small",37),t.EFF(7),t.k0s()()()()),2&n){const e=t.XpG(2);t.R7$(5),t.JRh(e.coverageSummary.patientName),t.R7$(2),t.Lme(" ",e.i18nService.translateSync("patient")," ID: ",e.coverageSummary.patientId," ")}}function W(n,a){if(1&n&&(t.j41(0,"small",38),t.EFF(1),t.k0s()),2&n){const e=t.XpG(2);t.R7$(1),t.SpI(" ",e.getRecommendation(e.coverageSummary.prescriptionCoverage)," ")}}function q(n,a){if(1&n&&(t.j41(0,"small",38),t.EFF(1),t.k0s()),2&n){const e=t.XpG(2);t.R7$(1),t.SpI(" ",e.getRecommendation(e.coverageSummary.consultationCoverage)," ")}}function V(n,a){if(1&n&&(t.j41(0,"small",38),t.EFF(1),t.k0s()),2&n){const e=t.XpG(2);t.R7$(1),t.SpI(" ",e.getRecommendation(e.coverageSummary.appointmentCoverage)," ")}}function J(n,a){if(1&n&&(t.j41(0,"div",39)(1,"small",38),t.nrm(2,"i",40),t.EFF(3),t.nI1(4,"date"),t.k0s()()),2&n){const e=t.XpG(2);t.R7$(3),t.Lme(" ",e.i18nService.translateSync("last_updated"),": ",t.i5U(4,2,e.coverageSummary.lastUpdated,"medium")," ")}}function Q(n,a){if(1&n&&(t.j41(0,"div",17),t.DNE(1,H,8,3,"div",18),t.j41(2,"div",19)(3,"div",20)(4,"div",21)(5,"div",22)(6,"div",23),t.nrm(7,"i",24),t.j41(8,"h6",6),t.EFF(9),t.k0s()(),t.j41(10,"div",25)(11,"div",26)(12,"span",27),t.EFF(13),t.k0s(),t.nrm(14,"i"),t.k0s(),t.j41(15,"div",28),t.nrm(16,"div",29),t.k0s(),t.DNE(17,W,2,1,"small",30),t.k0s()()()(),t.j41(18,"div",20)(19,"div",21)(20,"div",22)(21,"div",23),t.nrm(22,"i",31),t.j41(23,"h6",6),t.EFF(24),t.k0s()(),t.j41(25,"div",25)(26,"div",26)(27,"span",27),t.EFF(28),t.k0s(),t.nrm(29,"i"),t.k0s(),t.j41(30,"div",28),t.nrm(31,"div",29),t.k0s(),t.DNE(32,q,2,1,"small",30),t.k0s()()()(),t.j41(33,"div",20)(34,"div",21)(35,"div",22)(36,"div",23),t.nrm(37,"i",32),t.j41(38,"h6",6),t.EFF(39),t.k0s()(),t.j41(40,"div",25)(41,"div",26)(42,"span",27),t.EFF(43),t.k0s(),t.nrm(44,"i"),t.k0s(),t.j41(45,"div",28),t.nrm(46,"div",29),t.k0s(),t.DNE(47,V,2,1,"small",30),t.k0s()()()()(),t.DNE(48,J,5,5,"div",33),t.k0s()),2&n){const e=t.XpG();t.R7$(1),t.Y8G("ngIf",!e.compact),t.R7$(8),t.JRh(e.getCoverageText("prescription")),t.R7$(3),t.HbH("text-"+e.getCoverageLevelColor(e.coverageSummary.prescriptionCoverage)),t.R7$(1),t.SpI(" ",e.formatCoverage(e.coverageSummary.prescriptionCoverage)," "),t.R7$(1),t.HbH("text-"+e.getCoverageLevelColor(e.coverageSummary.prescriptionCoverage)),t.R7$(2),t.HbH(e.getProgressBarClass(e.coverageSummary.prescriptionCoverage)),t.xc7("width",e.getProgressBarWidth(e.coverageSummary.prescriptionCoverage),"%"),t.R7$(1),t.Y8G("ngIf",!e.compact),t.R7$(7),t.JRh(e.getCoverageText("consultation")),t.R7$(3),t.HbH("text-"+e.getCoverageLevelColor(e.coverageSummary.consultationCoverage)),t.R7$(1),t.SpI(" ",e.formatCoverage(e.coverageSummary.consultationCoverage)," "),t.R7$(1),t.HbH("text-"+e.getCoverageLevelColor(e.coverageSummary.consultationCoverage)),t.R7$(2),t.HbH(e.getProgressBarClass(e.coverageSummary.consultationCoverage)),t.xc7("width",e.getProgressBarWidth(e.coverageSummary.consultationCoverage),"%"),t.R7$(1),t.Y8G("ngIf",!e.compact),t.R7$(7),t.JRh(e.getCoverageText("appointment")),t.R7$(3),t.HbH("text-"+e.getCoverageLevelColor(e.coverageSummary.appointmentCoverage)),t.R7$(1),t.SpI(" ",e.formatCoverage(e.coverageSummary.appointmentCoverage)," "),t.R7$(1),t.HbH("text-"+e.getCoverageLevelColor(e.coverageSummary.appointmentCoverage)),t.R7$(2),t.HbH(e.getProgressBarClass(e.coverageSummary.appointmentCoverage)),t.xc7("width",e.getProgressBarWidth(e.coverageSummary.appointmentCoverage),"%"),t.R7$(1),t.Y8G("ngIf",!e.compact),t.R7$(1),t.Y8G("ngIf",!e.compact)}}let z=(()=>{class n{constructor(e,i){this.insuranceService=e,this.i18nService=i,this.destroy$=new I.B,this.showTitle=!0,this.compact=!1,this.coverageSummary=null,this.loading=!1,this.error=null}ngOnInit(){this.loadCoverageSummary()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}loadCoverageSummary(){this.loading=!0,this.error=null,this.insuranceService.getCoverageSummary().pipe((0,S.Q)(this.destroy$),(0,T.j)(()=>this.loading=!1)).subscribe({next:e=>{this.coverageSummary=e},error:e=>{console.error("Error loading coverage summary:",e),this.error="Failed to load insurance coverage information",this.coverageSummary=this.insuranceService.getMockCoverageSummary()}})}getCoverageLevel(e){return this.insuranceService.getCoverageLevel(e.coveragePercentage)}getCoverageLevelColor(e){return this.insuranceService.getCoverageLevelColor(e.coveragePercentage)}formatCoverage(e){return this.insuranceService.formatCoverage(e.coveragePercentage)}getRecommendation(e){return this.insuranceService.getRecommendation(e)}isEligible(e){return this.insuranceService.isEligibleForService(e)}refresh(){this.loadCoverageSummary()}getCoverageIcon(e){if(!e.eligible)return"fas fa-times-circle";switch(this.getCoverageLevel(e)){case"excellent":case"good":return"fas fa-check-circle";case"fair":case"poor":return"fas fa-exclamation-triangle";default:return"fas fa-times-circle"}}getCoverageText(e){switch(e){case"prescription":return this.i18nService.translateSync("prescriptions");case"consultation":return this.i18nService.translateSync("video_consultation");case"appointment":return this.i18nService.translateSync("appointments");default:return e}}getProgressBarClass(e){switch(this.getCoverageLevel(e)){case"excellent":return"bg-success";case"good":return"bg-info";case"fair":return"bg-warning";case"poor":return"bg-danger";default:return"bg-secondary"}}getProgressBarWidth(e){return Math.round(100*e.coveragePercentage)}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(U),t.rXU(Y.s))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-insurance-coverage"]],inputs:{patientId:"patientId",showTitle:"showTitle",compact:"compact"},decls:5,vars:6,consts:[[1,"insurance-coverage"],["class","d-flex justify-content-between align-items-center mb-3",4,"ngIf"],["class","text-center py-4",4,"ngIf"],["class","alert alert-warning",4,"ngIf"],["class","coverage-summary",4,"ngIf"],[1,"d-flex","justify-content-between","align-items-center","mb-3"],[1,"mb-0"],[1,"fas","fa-shield-alt","text-primary","me-2"],["title","Refresh coverage information",1,"btn","btn-sm","btn-outline-secondary",3,"disabled","click"],[1,"fas","fa-sync-alt"],[1,"text-center","py-4"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-2","text-muted"],[1,"alert","alert-warning"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"btn","btn-sm","btn-outline-warning","ms-2",3,"click"],[1,"coverage-summary"],["class","patient-info mb-3",4,"ngIf"],[1,"row","g-3"],[1,"col-md-4"],[1,"coverage-card","card","h-100"],[1,"card-body"],[1,"d-flex","align-items-center","mb-2"],[1,"fas","fa-pills","text-primary","me-2"],[1,"coverage-details"],[1,"d-flex","justify-content-between","align-items-center","mb-2"],[1,"coverage-percentage","h5","mb-0"],[1,"progress","mb-2",2,"height","6px"],[1,"progress-bar"],["class","text-muted",4,"ngIf"],[1,"fas","fa-video","text-success","me-2"],[1,"fas","fa-calendar-check","text-info","me-2"],["class","text-center mt-3",4,"ngIf"],[1,"patient-info","mb-3"],[1,"d-flex","align-items-center"],[1,"fas","fa-user-circle","text-secondary","me-2"],[1,"text-muted","d-block"],[1,"text-muted"],[1,"text-center","mt-3"],[1,"fas","fa-clock","me-1"]],template:function(i,o){1&i&&(t.j41(0,"div",0),t.DNE(1,X,6,5,"div",1),t.DNE(2,B,6,2,"div",2),t.DNE(3,L,5,2,"div",3),t.DNE(4,Q,49,35,"div",4),t.k0s()),2&i&&(t.AVh("compact",o.compact),t.R7$(1),t.Y8G("ngIf",o.showTitle),t.R7$(1),t.Y8G("ngIf",o.loading&&!o.coverageSummary),t.R7$(1),t.Y8G("ngIf",o.error&&!o.coverageSummary),t.R7$(1),t.Y8G("ngIf",o.coverageSummary))},dependencies:[d.bT,d.vh],styles:['.insurance-coverage[_ngcontent-%COMP%]{background:#f8f9fa;border-radius:.5rem;padding:1.5rem}.insurance-coverage.compact[_ngcontent-%COMP%]{padding:1rem;background:transparent}.coverage-card[_ngcontent-%COMP%]{border:none;box-shadow:0 .125rem .25rem #00000013;transition:transform .15s ease-in-out,box-shadow .15s ease-in-out}.coverage-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 .5rem 1rem #00000026}.coverage-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:1rem}.coverage-percentage[_ngcontent-%COMP%]{font-weight:600;font-size:1.25rem}.coverage-details[_ngcontent-%COMP%]{margin-top:.5rem}.progress[_ngcontent-%COMP%]{background-color:#e9ecef;border-radius:.25rem}.progress-bar[_ngcontent-%COMP%]{transition:width .6s ease}.patient-info[_ngcontent-%COMP%]{background:white;border-radius:.375rem;padding:1rem;border:1px solid #dee2e6}.text-excellent[_ngcontent-%COMP%]{color:#28a745!important}.text-good[_ngcontent-%COMP%]{color:#17a2b8!important}.text-fair[_ngcontent-%COMP%]{color:#ffc107!important}.text-poor[_ngcontent-%COMP%]{color:#dc3545!important}.text-none[_ngcontent-%COMP%]{color:#6c757d!important}@media (max-width: 768px){.insurance-coverage[_ngcontent-%COMP%]{padding:1rem}.coverage-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:.75rem}.coverage-percentage[_ngcontent-%COMP%]{font-size:1.1rem}.row.g-3[_ngcontent-%COMP%]{--bs-gutter-x: .75rem;--bs-gutter-y: .75rem}}.spinner-border[_ngcontent-%COMP%]{width:2rem;height:2rem}.insurance-coverage.compact[_ngcontent-%COMP%]   .coverage-card[_ngcontent-%COMP%]{margin-bottom:.5rem}.insurance-coverage.compact[_ngcontent-%COMP%]   .coverage-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%]{padding:.75rem}.insurance-coverage.compact[_ngcontent-%COMP%]   .coverage-percentage[_ngcontent-%COMP%]{font-size:1rem}.insurance-coverage.compact[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:.875rem}.fas[_ngcontent-%COMP%]{width:1.2em;text-align:center}.alert[_ngcontent-%COMP%]{border:none;border-radius:.375rem}.btn-outline-secondary[_ngcontent-%COMP%]{border-color:#dee2e6}.btn-outline-secondary[_ngcontent-%COMP%]:hover{background-color:#f8f9fa;border-color:#adb5bd}@keyframes _ngcontent-%COMP%_progressAnimation{0%{width:0%}to{width:var(--progress-width)}}.progress-bar[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_progressAnimation 1s ease-in-out}.coverage-card[_ngcontent-%COMP%]{cursor:default;position:relative;overflow:hidden}.coverage-card[_ngcontent-%COMP%]:before{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);transition:left .5s}.coverage-card[_ngcontent-%COMP%]:hover:before{left:100%}']})}}return n})();var Z=s(9339),K=s(628);const tt=["chatWindow"];function et(n,a){1&n&&(t.j41(0,"div",17)(1,"div",18)(2,"span",19),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",20),t.EFF(5,"Loading your dashboard..."),t.k0s()())}function nt(n,a){if(1&n&&(t.j41(0,"div",21),t.nrm(1,"i",22),t.EFF(2),t.k0s()),2&n){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function it(n,a){if(1&n&&(t.j41(0,"div",57)(1,"div",48)(2,"div",58),t.nrm(3,"i"),t.j41(4,"h6",59),t.EFF(5),t.k0s(),t.j41(6,"h4",60),t.EFF(7),t.j41(8,"small",61),t.EFF(9),t.k0s()(),t.j41(10,"span"),t.EFF(11),t.nI1(12,"titlecase"),t.k0s()()()()),2&n){const e=a.$implicit,i=t.XpG(2);t.R7$(3),t.STu("bi bi-",e.icon," display-6 ",e.color," mb-2"),t.R7$(2),t.JRh(e.name),t.R7$(2),t.SpI("",e.value," "),t.R7$(2),t.JRh(e.unit),t.R7$(1),t.HbH(i.getStatusBadgeClass(e.status)),t.R7$(1),t.JRh(t.bMT(12,10,e.status))}}function ot(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",57)(1,"div",62),t.bIt("click",function(){const r=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.navigateTo(r.route))}),t.j41(2,"div",58)(3,"div",63),t.nrm(4,"i"),t.k0s(),t.j41(5,"h6",59),t.EFF(6),t.k0s(),t.j41(7,"p",64),t.EFF(8),t.k0s()()()()}if(2&n){const e=a.$implicit;t.R7$(3),t.HbH(e.color),t.R7$(1),t.ZvI("bi bi-",e.icon," text-white fs-4"),t.R7$(2),t.JRh(e.title),t.R7$(2),t.JRh(e.description)}}function at(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",65),t.nrm(1,"i",66),t.j41(2,"p"),t.EFF(3,"No upcoming appointments"),t.k0s(),t.j41(4,"button",67),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.navigateTo("/appointments/book"))}),t.nrm(5,"i",68),t.EFF(6,"Book Appointment "),t.k0s()()}}function rt(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",69)(1,"div",70)(2,"div",71)(3,"div",72),t.nrm(4,"i"),t.k0s()(),t.j41(5,"div")(6,"h6",73),t.EFF(7),t.k0s(),t.j41(8,"p",74),t.nrm(9,"i",75),t.EFF(10),t.nrm(11,"i",76),t.EFF(12),t.k0s(),t.j41(13,"span"),t.EFF(14),t.nI1(15,"titlecase"),t.k0s()()(),t.j41(16,"div",77)(17,"button",78),t.bIt("click",function(){const r=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.navigateTo("/appointments/"+r.id))}),t.nrm(18,"i",79),t.EFF(19,"View Details "),t.k0s(),t.j41(20,"button",80),t.bIt("click",function(){const r=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.startChatWithDoctor(r.doctor.id))}),t.nrm(21,"i",81),t.EFF(22,"Chat "),t.k0s()()()}if(2&n){const e=a.$implicit,i=t.XpG(2);t.R7$(4),t.ZvI("bi bi-","VIDEO_CALL"===e.type?"camera-video":"geo-alt"," text-primary"),t.R7$(3),t.SpI("Dr. ",e.doctor.fullName,""),t.R7$(3),t.SpI("",e.date," "),t.R7$(2),t.SpI("",e.startTime," "),t.R7$(1),t.HbH(i.getStatusBadgeClass(e.status)),t.R7$(1),t.SpI(" ",t.bMT(15,10,e.status)," "),t.R7$(6),t.Y8G("disabled","CONFIRMED"!==e.status)}}function ct(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",65),t.nrm(1,"i",82),t.j41(2,"p"),t.EFF(3,"No messages yet"),t.k0s(),t.j41(4,"button",67),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.openChatModal())}),t.nrm(5,"i",83),t.EFF(6,"Start Conversation "),t.k0s()()}}function st(n,a){if(1&n&&(t.j41(0,"p",74),t.EFF(1),t.nI1(2,"slice"),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.Lme(" ",t.brH(2,2,e.lastMessage.content,0,50),"",e.lastMessage.content.length>50?"...":""," ")}}function dt(n,a){if(1&n&&(t.j41(0,"small",61),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit,i=t.XpG(2);t.R7$(1),t.SpI(" ",i.formatChatTime(e.lastMessage.createdAt)," ")}}function lt(n,a){if(1&n&&(t.j41(0,"span",89),t.EFF(1),t.k0s()),2&n){const e=t.XpG().$implicit;t.R7$(1),t.SpI(" ",e.unreadCount," ")}}function mt(n,a){if(1&n){const e=t.RV6();t.j41(0,"div",84)(1,"div",70)(2,"div",71),t.nrm(3,"img",85),t.k0s(),t.j41(4,"div")(5,"h6",73),t.EFF(6),t.k0s(),t.DNE(7,st,3,6,"p",86),t.DNE(8,dt,2,1,"small",87),t.k0s()(),t.j41(9,"div",77),t.DNE(10,lt,2,1,"span",88),t.j41(11,"button",39),t.bIt("click",function(){const r=t.eBV(e).$implicit,c=t.XpG(2);return t.Njj(c.openChat(r))}),t.nrm(12,"i",81),t.EFF(13,"Open "),t.k0s()()()}if(2&n){const e=a.$implicit;t.R7$(3),t.Y8G("src",e.doctor.avatar||"/assets/images/default-avatar.png",t.B4B)("alt",e.doctor.fullName),t.R7$(3),t.SpI("Dr. ",e.doctor.fullName,""),t.R7$(1),t.Y8G("ngIf",e.lastMessage),t.R7$(1),t.Y8G("ngIf",e.lastMessage),t.R7$(2),t.Y8G("ngIf",e.unreadCount>0)}}function gt(n,a){1&n&&t.nrm(0,"hr",94)}function pt(n,a){if(1&n&&(t.j41(0,"div",90)(1,"div",71)(2,"div",91),t.nrm(3,"i"),t.k0s()(),t.j41(4,"div",92)(5,"h6",73),t.EFF(6),t.k0s(),t.j41(7,"p",74),t.EFF(8),t.k0s(),t.j41(9,"small",61),t.EFF(10),t.k0s()(),t.DNE(11,gt,1,0,"hr",93),t.k0s()),2&n){const e=a.$implicit,i=a.last;t.R7$(3),t.STu("bi bi-",e.icon," ",e.color,""),t.R7$(3),t.JRh(e.title),t.R7$(2),t.JRh(e.description),t.R7$(2),t.JRh(e.time),t.R7$(1),t.Y8G("ngIf",!i)}}function ht(n,a){1&n&&t.nrm(0,"hr",94)}function ut(n,a){if(1&n&&(t.j41(0,"div",95)(1,"div",71)(2,"div",96),t.nrm(3,"i"),t.k0s()(),t.j41(4,"div",92)(5,"h6",73),t.EFF(6),t.k0s(),t.j41(7,"p",97),t.EFF(8),t.k0s()(),t.DNE(9,ht,1,0,"hr",93),t.k0s()),2&n){const e=a.$implicit,i=a.last;t.R7$(3),t.ZvI("bi bi-",e.icon," text-primary"),t.R7$(3),t.JRh(e.title),t.R7$(2),t.JRh(e.description),t.R7$(1),t.Y8G("ngIf",!i)}}function vt(n,a){if(1&n){const e=t.RV6();t.j41(0,"div")(1,"div",23)(2,"div",24)(3,"div",25)(4,"div")(5,"h1",26),t.EFF(6),t.k0s(),t.j41(7,"p",27),t.EFF(8,"Here's your health overview for today"),t.k0s()(),t.j41(9,"button",28),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.refreshData())}),t.nrm(10,"i",29),t.EFF(11,"Refresh "),t.k0s()()()(),t.j41(12,"div",23)(13,"div",24)(14,"h5",30),t.nrm(15,"i",31),t.EFF(16,"Health Metrics "),t.k0s()(),t.DNE(17,it,13,12,"div",32),t.k0s(),t.j41(18,"div",23)(19,"div",24),t.nrm(20,"app-insurance-coverage",33),t.k0s()(),t.j41(21,"div",23)(22,"div",24)(23,"h5",30),t.nrm(24,"i",34),t.EFF(25,"Quick Actions "),t.k0s()(),t.DNE(26,ot,9,7,"div",32),t.k0s(),t.j41(27,"div",23)(28,"div",24)(29,"div",35)(30,"div",36)(31,"h6",37),t.nrm(32,"i",38),t.EFF(33,"Upcoming Appointments "),t.k0s(),t.j41(34,"button",39),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.navigateTo("/appointments"))}),t.EFF(35," View All "),t.k0s()(),t.j41(36,"div",40),t.DNE(37,at,7,0,"div",41),t.DNE(38,rt,23,12,"div",42),t.k0s()()()(),t.j41(39,"div",23)(40,"div",24)(41,"div",35)(42,"div",36)(43,"h6",37),t.nrm(44,"i",9),t.EFF(45,"Recent Messages "),t.k0s(),t.j41(46,"button",39),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.openChatModal())}),t.nrm(47,"i",43),t.EFF(48,"New Message "),t.k0s()(),t.j41(49,"div",40),t.DNE(50,ct,7,0,"div",41),t.DNE(51,mt,14,6,"div",44),t.k0s()()()(),t.j41(52,"div",45)(53,"div",46),t.nrm(54,"app-quick-chat-widget"),t.k0s(),t.j41(55,"div",47)(56,"div",48)(57,"div",49)(58,"h6",37),t.nrm(59,"i",50),t.EFF(60,"Recent Activities "),t.k0s()(),t.j41(61,"div",40),t.DNE(62,pt,12,8,"div",51),t.k0s()()(),t.j41(63,"div",47)(64,"div",48)(65,"div",49)(66,"h6",37),t.nrm(67,"i",52),t.EFF(68,"Health Tips "),t.k0s()(),t.j41(69,"div",40),t.DNE(70,ut,10,6,"div",53),t.k0s()()()(),t.j41(71,"div",45)(72,"div",24)(73,"div",54),t.nrm(74,"i",55),t.j41(75,"div")(76,"h6",56),t.EFF(77,"Emergency Contact"),t.k0s(),t.j41(78,"p",37),t.EFF(79,"For medical emergencies, call "),t.j41(80,"strong"),t.EFF(81,"911"),t.k0s(),t.EFF(82," or visit your nearest emergency room."),t.k0s()()()()()()}if(2&n){const e=t.XpG();t.R7$(6),t.Lme("",e.getGreeting(),", ",null==e.currentUser?null:e.currentUser.fullName,"!"),t.R7$(11),t.Y8G("ngForOf",e.healthMetrics),t.R7$(3),t.Y8G("compact",!1),t.R7$(6),t.Y8G("ngForOf",e.quickActions),t.R7$(11),t.Y8G("ngIf",0===e.upcomingAppointments.length),t.R7$(1),t.Y8G("ngForOf",e.upcomingAppointments),t.R7$(12),t.Y8G("ngIf",0===e.recentChats.length),t.R7$(1),t.Y8G("ngForOf",e.recentChats),t.R7$(11),t.Y8G("ngForOf",e.recentActivities),t.R7$(8),t.Y8G("ngForOf",e.healthTips)}}const ft=[{path:"",redirectTo:"dashboard",pathMatch:"full"},{path:"dashboard",component:(()=>{class n{constructor(e,i,o,r,c){this.authService=e,this.userService=i,this.appointmentService=o,this.chatService=r,this.router=c,this.currentUser=null,this.isLoading=!0,this.error="",this.appointments=[],this.upcomingAppointments=[],this.recentChats=[],this.healthMetrics=[{name:"Heart Rate",value:"72",unit:"bpm",status:"normal",icon:"heart-pulse",color:"text-success"},{name:"Blood Pressure",value:"120/80",unit:"mmHg",status:"normal",icon:"activity",color:"text-success"},{name:"Weight",value:"70",unit:"kg",status:"normal",icon:"speedometer2",color:"text-info"},{name:"Temperature",value:"98.6",unit:"\xb0F",status:"normal",icon:"thermometer-half",color:"text-success"}],this.quickActions=[{title:"Book Appointment",description:"Schedule a consultation with a doctor",icon:"calendar-plus",color:"bg-primary",route:"/appointments/book"},{title:"Find Doctors",description:"Browse available healthcare providers",icon:"search",color:"bg-info",route:"/appointments/doctors"},{title:"AI Health Assistant",description:"Get AI-powered health guidance and symptom analysis",icon:"robot",color:"bg-success",route:"/ai-health-bot"},{title:"Messages",description:"Chat with your healthcare providers",icon:"chat-dots",color:"bg-warning",route:"/chat"}],this.recentActivities=[{title:"Appointment Scheduled",description:"Consultation with Dr. Smith on Dec 15, 2024",time:"2 hours ago",icon:"calendar-check",color:"text-primary"},{title:"Health Metrics Updated",description:"Blood pressure and weight recorded",time:"1 day ago",icon:"graph-up",color:"text-success"},{title:"Message Received",description:"New message from Dr. Johnson",time:"2 days ago",icon:"envelope",color:"text-info"}],this.healthTips=[{title:"Stay Hydrated",description:"Drink at least 8 glasses of water daily for optimal health.",icon:"droplet"},{title:"Regular Exercise",description:"Aim for 30 minutes of moderate exercise 5 days a week.",icon:"bicycle"},{title:"Healthy Sleep",description:"Get 7-9 hours of quality sleep each night.",icon:"moon"}]}ngOnInit(){this.loadUserData(),this.loadAppointments(),this.loadRecentChats()}loadUserData(){this.authService.currentUser$.subscribe({next:e=>{this.currentUser=e,this.isLoading=!1},error:e=>{this.error="Failed to load user data",this.isLoading=!1}})}loadAppointments(){this.appointmentService.getPatientAppointments().subscribe({next:e=>{this.appointments=e,this.upcomingAppointments=e.filter(i=>new Date(i.date)>=new Date).slice(0,3),this.updateRecentActivities()},error:e=>{console.error("Failed to load appointments:",e)}})}updateRecentActivities(){const e=this.appointments.filter(i=>"SCHEDULED"===i.status||"CONFIRMED"===i.status).slice(0,2);this.recentActivities=[...e.map(i=>({title:"Appointment Scheduled",description:`Consultation with Dr. ${i.doctor?.fullName} on ${i.date}`,time:this.getTimeAgo(i.createdAt),icon:"calendar-check",color:"text-primary"})),...this.recentActivities.slice(e.length)]}getTimeAgo(e){const i=new Date,o=new Date(e),r=Math.floor((i.getTime()-o.getTime())/36e5);return r<1?"Just now":r<24?`${r} hours ago`:`${Math.floor(r/24)} days ago`}navigateTo(e){this.router.navigate([e])}getGreeting(){const e=(new Date).getHours();return e<12?"Good morning":e<18?"Good afternoon":"Good evening"}getStatusBadgeClass(e){switch(e){case"normal":return"badge bg-success";case"warning":return"badge bg-warning";case"danger":return"badge bg-danger";default:return"badge bg-secondary"}}refreshData(){this.isLoading=!0,this.loadAppointments(),this.loadRecentChats(),setTimeout(()=>{this.isLoading=!1},1e3)}loadRecentChats(){this.chatService.getUserChats().subscribe({next:e=>{this.recentChats=e.slice(0,3)},error:e=>{console.error("Failed to load chats:",e)}})}openChatModal(){const e=document.getElementById("chatModal");e&&new window.bootstrap.Modal(e).show()}openChat(e){this.openChatModal(),setTimeout(()=>{this.onChatSelected(e)},300)}onChatSelected(e){this.chatWindow&&this.chatWindow.loadChat(e)}startChatWithDoctor(e){console.log("Starting chat with doctor ID:",e),this.authService.isAuthenticated()?this.chatService.createOrGetChat(e).subscribe({next:i=>{console.log("Chat created/retrieved:",i),this.openChatModal(),setTimeout(()=>{this.onChatSelected(i)},500)},error:i=>{console.error("Failed to create chat:",i);let o="Failed to start chat. ";401===i.status?(o+="Please log in again.",this.authService.logout()):o+=403===i.status?"You do not have permission to start this chat.":i.error&&i.error.message?i.error.message:"Please try again.",alert(o)}}):alert("Please log in to start a chat.")}formatChatTime(e){const i=new Date(e),r=((new Date).getTime()-i.getTime())/36e5;return r<1?"Just now":r<24?i.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}):i.toLocaleDateString()}static{this.\u0275fac=function(i){return new(i||n)(t.rXU(h.u),t.rXU(_.D),t.rXU(u.h),t.rXU(v.m),t.rXU(m.Ix))}}static{this.\u0275cmp=t.VBU({type:n,selectors:[["app-patient-dashboard"]],viewQuery:function(i,o){if(1&i&&t.GBs(tt,5),2&i){let r;t.mGM(r=t.lsd())&&(o.chatWindow=r.first)}},decls:19,vars:3,consts:[[1,"container-fluid","py-4"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],[4,"ngIf"],["id","chatModal","tabindex","-1","aria-labelledby","chatModalLabel","aria-hidden","true",1,"modal","fade"],[1,"modal-dialog","modal-xl"],[1,"modal-content"],[1,"modal-header"],["id","chatModalLabel",1,"modal-title"],[1,"bi","bi-chat-dots","me-2"],["type","button","data-bs-dismiss","modal","aria-label","Close",1,"btn-close"],[1,"modal-body","p-0",2,"height","600px"],[1,"row","h-100","g-0"],[1,"col-md-4","border-end"],[3,"chatSelected"],[1,"col-md-8"],["chatWindow",""],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3","text-muted"],["role","alert",1,"alert","alert-danger"],[1,"bi","bi-exclamation-triangle","me-2"],[1,"row","mb-4"],[1,"col-12"],[1,"d-flex","justify-content-between","align-items-center"],[1,"h3","mb-1"],[1,"text-muted","mb-0"],[1,"btn","btn-outline-primary",3,"click"],[1,"bi","bi-arrow-clockwise","me-2"],[1,"mb-3"],[1,"bi","bi-heart-pulse","me-2","text-primary"],["class","col-md-3 col-sm-6 mb-3",4,"ngFor","ngForOf"],[3,"compact"],[1,"bi","bi-lightning","me-2","text-primary"],[1,"card"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"bi","bi-calendar-check","me-2"],[1,"btn","btn-sm","btn-outline-primary",3,"click"],[1,"card-body"],["class","text-center py-4 text-muted",4,"ngIf"],["class","appointment-item d-flex align-items-center justify-content-between p-3 mb-2 rounded border",4,"ngFor","ngForOf"],[1,"bi","bi-chat-plus","me-1"],["class","chat-preview d-flex align-items-center justify-content-between p-3 mb-2 rounded border",4,"ngFor","ngForOf"],[1,"row"],[1,"col-lg-4","mb-4"],[1,"col-md-4","mb-4"],[1,"card","h-100"],[1,"card-header"],[1,"bi","bi-clock-history","me-2"],["class","activity-item d-flex align-items-start mb-3",4,"ngFor","ngForOf"],[1,"bi","bi-lightbulb","me-2"],["class","tip-item d-flex align-items-start mb-3",4,"ngFor","ngForOf"],["role","alert",1,"alert","alert-info","d-flex","align-items-center"],[1,"bi","bi-info-circle","me-3","fs-4"],[1,"alert-heading","mb-1"],[1,"col-md-3","col-sm-6","mb-3"],[1,"card-body","text-center"],[1,"card-title"],[1,"mb-2"],[1,"text-muted"],[1,"card","h-100","action-card",3,"click"],[1,"rounded-circle","d-inline-flex","align-items-center","justify-content-center","mb-3",2,"width","60px","height","60px"],[1,"card-text","text-muted","small"],[1,"text-center","py-4","text-muted"],[1,"bi","bi-calendar-x","display-6","mb-3"],[1,"btn","btn-primary",3,"click"],[1,"bi","bi-calendar-plus","me-2"],[1,"appointment-item","d-flex","align-items-center","justify-content-between","p-3","mb-2","rounded","border"],[1,"d-flex","align-items-center"],[1,"flex-shrink-0","me-3"],[1,"rounded-circle","bg-light","d-flex","align-items-center","justify-content-center",2,"width","45px","height","45px"],[1,"mb-1"],[1,"mb-1","text-muted","small"],[1,"bi","bi-calendar","me-1"],[1,"bi","bi-clock","ms-2","me-1"],[1,"flex-shrink-0"],[1,"btn","btn-sm","btn-outline-primary","me-2",3,"click"],[1,"bi","bi-eye","me-1"],[1,"btn","btn-sm","btn-primary",3,"disabled","click"],[1,"bi","bi-chat","me-1"],[1,"bi","bi-chat-square-text","display-6","mb-3"],[1,"bi","bi-chat-plus","me-2"],[1,"chat-preview","d-flex","align-items-center","justify-content-between","p-3","mb-2","rounded","border"],[1,"rounded-circle",2,"width","45px","height","45px","object-fit","cover",3,"src","alt"],["class","mb-1 text-muted small",4,"ngIf"],["class","text-muted",4,"ngIf"],["class","badge bg-primary rounded-pill me-2",4,"ngIf"],[1,"badge","bg-primary","rounded-pill","me-2"],[1,"activity-item","d-flex","align-items-start","mb-3"],[1,"rounded-circle","bg-light","d-flex","align-items-center","justify-content-center",2,"width","40px","height","40px"],[1,"flex-grow-1"],["class","my-3",4,"ngIf"],[1,"my-3"],[1,"tip-item","d-flex","align-items-start","mb-3"],[1,"rounded-circle","bg-primary","bg-opacity-10","d-flex","align-items-center","justify-content-center",2,"width","40px","height","40px"],[1,"mb-0","text-muted","small"]],template:function(i,o){1&i&&(t.j41(0,"div",0),t.DNE(1,et,6,0,"div",1),t.DNE(2,nt,3,1,"div",2),t.DNE(3,vt,83,11,"div",3),t.k0s(),t.j41(4,"div",4)(5,"div",5)(6,"div",6)(7,"div",7)(8,"h5",8),t.nrm(9,"i",9),t.EFF(10,"Messages "),t.k0s(),t.nrm(11,"button",10),t.k0s(),t.j41(12,"div",11)(13,"div",12)(14,"div",13)(15,"app-chat-list",14),t.bIt("chatSelected",function(c){return o.onChatSelected(c)}),t.k0s()(),t.j41(16,"div",15),t.nrm(17,"app-chat-window",null,16),t.k0s()()()()()()),2&i&&(t.R7$(1),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error&&!o.isLoading),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error))},dependencies:[d.Sq,d.bT,E,z,Z.q,K.E,d.P9,d.PV],styles:[".action-card[_ngcontent-%COMP%]{cursor:pointer;transition:all .3s ease;border:none;box-shadow:0 2px 4px #0000001a}.action-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 8px #00000026}.card[_ngcontent-%COMP%]{border:none;border-radius:12px;box-shadow:0 2px 4px #0000001a}.card-header[_ngcontent-%COMP%]{background-color:transparent;border-bottom:1px solid #e9ecef;font-weight:600}.activity-item[_ngcontent-%COMP%], .tip-item[_ngcontent-%COMP%]{border-bottom:1px solid #f8f9fa;padding-bottom:1rem}.activity-item[_ngcontent-%COMP%]:last-child, .tip-item[_ngcontent-%COMP%]:last-child{border-bottom:none;padding-bottom:0}.bg-primary[_ngcontent-%COMP%]{background-color:#0d6efd!important}.bg-info[_ngcontent-%COMP%]{background-color:#0dcaf0!important}.bg-success[_ngcontent-%COMP%]{background-color:#198754!important}.bg-warning[_ngcontent-%COMP%]{background-color:#ffc107!important}.text-primary[_ngcontent-%COMP%]{color:#0d6efd!important}.badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding-left:1rem;padding-right:1rem}.card-body[_ngcontent-%COMP%]{padding:1rem}.display-6[_ngcontent-%COMP%]{font-size:2rem}}"]})}}return n})()}];let Ct=(()=>{class n{static{this.\u0275fac=function(i){return new(i||n)}}static{this.\u0275mod=t.$C({type:n})}static{this.\u0275inj=t.G2t({imports:[f.G,C.ChatModule,m.iI.forChild(ft)]})}}return n})()}}]);