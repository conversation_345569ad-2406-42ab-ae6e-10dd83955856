"use strict";(self.webpackChunkhealthconnect_frontend=self.webpackChunkhealthconnect_frontend||[]).push([[614],{1129:($,P,u)=>{u.d(P,{B:()=>E});var h=u(4412),S=u(3794),b=u(5877),T=u(2869),t=u(25),O=u.n(t),x=u(5312),R=u(6276),M=u(8010);let E=(()=>{class y{constructor(a){this.authService=a,this.stompClient=null,this.wsUrl=`${x.c.apiUrl}/ws`,this.userPresences$=new h.t(new Map),this.currentUserPresence$=new h.t(null),this.typingNotifications$=new S.B,this.connectionStatus$=new h.t(!1),this.typingTimeouts=new Map,setTimeout(()=>{this.initializeConnection()},100)}initializeConnection(){if(!this.authService.isAuthenticated())return;const a=this.authService.getToken();a&&(this.stompClient=new T.K({webSocketFactory:()=>new(O())(this.wsUrl),connectHeaders:{Authorization:`Bearer ${a}`},debug:c=>{console.log("Presence STOMP Debug:",c)},onConnect:()=>{this.connectionStatus$.next(!0),console.log("Presence WebSocket connected"),this.subscribeToPresenceUpdates(),this.startHeartbeat(),this.updatePresence("ONLINE")},onWebSocketClose:()=>{this.connectionStatus$.next(!1),console.log("Presence WebSocket disconnected"),this.stopHeartbeat(),setTimeout(()=>{this.authService.isAuthenticated()&&this.connect()},5e3)},onStompError:c=>{console.error("Presence STOMP error:",c),this.connectionStatus$.next(!1)}}),this.stompClient.activate())}subscribeToPresenceUpdates(){this.stompClient?.connected&&(this.stompClient.subscribe("/topic/presence",a=>{const c=JSON.parse(a.body);this.handlePresenceUpdate(c)}),this.stompClient.subscribe("/topic/chat/+/typing",a=>{const c=JSON.parse(a.body);this.handleTypingNotification(c)}))}handlePresenceUpdate(a){const c={userId:a.userId,userName:a.userName,status:a.status,statusMessage:a.statusMessage,lastSeen:new Date(a.lastSeen)},p=this.userPresences$.value;p.set(c.userId,c),this.userPresences$.next(new Map(p));const f=this.authService.getCurrentUser();f&&f.id===c.userId&&this.currentUserPresence$.next(c)}handleTypingNotification(a){const c={userId:a.userId,chatId:a.chatId,isTyping:a.isTyping,timestamp:new Date(a.timestamp)};this.typingNotifications$.next(c);const p=this.userPresences$.value,f=p.get(c.userId);f&&(f.isTyping=c.isTyping,f.typingInChatId=c.isTyping?c.chatId:void 0,p.set(c.userId,f),this.userPresences$.next(new Map(p)))}startHeartbeat(){this.heartbeatInterval=(0,b.Y)(3e4).subscribe(()=>{this.sendHeartbeat()})}stopHeartbeat(){this.heartbeatInterval&&(this.heartbeatInterval.unsubscribe(),this.heartbeatInterval=null)}sendHeartbeat(){this.stompClient?.connected&&this.stompClient.publish({destination:"/app/presence/heartbeat",headers:{Authorization:`Bearer ${this.authService.getToken()}`}})}connect(){this.stompClient?.connected||this.initializeConnection()}disconnect(){this.updatePresence("OFFLINE"),this.stopHeartbeat(),this.stompClient&&(this.stompClient.deactivate(),this.connectionStatus$.next(!1))}updatePresence(a,c){if(!this.stompClient?.connected)return;const p=this.authService.getToken();if(!p)return;const f={status:a,statusMessage:c,deviceInfo:navigator.userAgent,ipAddress:""};this.stompClient.publish({destination:"/app/presence/update",body:JSON.stringify(f),headers:{Authorization:`Bearer ${p}`}})}startTyping(a){if(!this.stompClient?.connected)return;const c=this.authService.getToken();if(!c)return;this.stompClient.publish({destination:`/app/chat/${a}/typing`,body:"typing",headers:{Authorization:`Bearer ${c}`}});const p=this.typingTimeouts.get(a);p&&clearTimeout(p);const f=setTimeout(()=>{this.stopTyping(a)},3e3);this.typingTimeouts.set(a,f)}stopTyping(a){if(!this.stompClient?.connected)return;const c=this.authService.getToken();if(!c)return;this.stompClient.publish({destination:`/app/chat/${a}/typing`,body:"stopped",headers:{Authorization:`Bearer ${c}`}});const p=this.typingTimeouts.get(a);p&&(clearTimeout(p),this.typingTimeouts.delete(a))}getUserPresence(a){return this.userPresences$.value.get(a)||null}isUserOnline(a){const c=this.getUserPresence(a);return!!c&&["ONLINE","BUSY","AWAY"].includes(c.status)}isUserTyping(a,c){const p=this.getUserPresence(a);return!(!p||!p.isTyping||c&&p.typingInChatId!==c)}getUserPresences(){return this.userPresences$.asObservable()}getCurrentUserPresence(){return this.currentUserPresence$.asObservable()}getTypingNotifications(){return this.typingNotifications$.asObservable()}getConnectionStatus(){return this.connectionStatus$.asObservable()}getOnlineUsers(){return Array.from(this.userPresences$.value.values()).filter(c=>["ONLINE","BUSY","AWAY"].includes(c.status))}static{this.\u0275fac=function(c){return new(c||y)(R.KVO(M.u))}}static{this.\u0275prov=R.jDH({token:y,factory:y.\u0275fac,providedIn:"root"})}}return y})()},614:($,P,u)=>{u.r(P),u.d(P,{TelemedicineModule:()=>_t});var h=u(177),S=u(4341),b=u(2434),T=u(4978),t=u(6276),O=u(3351),x=u(8010),R=u(5567);function M(i,l){1&i&&(t.j41(0,"div",4)(1,"div",5)(2,"span",6),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",7),t.EFF(5,"Loading consultation details..."),t.k0s()())}function E(i,l){if(1&i){const e=t.RV6();t.j41(0,"div",8),t.nrm(1,"i",9),t.EFF(2),t.j41(3,"button",10),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.loadConsultation())}),t.nrm(4,"i",11),t.EFF(5," Retry "),t.k0s()()}if(2&i){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function y(i,l){if(1&i&&(t.j41(0,"div",22)(1,"div",23)(2,"label",24),t.EFF(3,"Started At"),t.k0s(),t.j41(4,"p",25),t.EFF(5),t.k0s()()()),2&i){const e=t.XpG(2);t.R7$(5),t.JRh(e.formatDateTime(e.consultation.actualStartTime))}}function I(i,l){if(1&i&&(t.j41(0,"div",22)(1,"div",23)(2,"label",24),t.EFF(3,"Duration"),t.k0s(),t.j41(4,"p",25),t.EFF(5),t.k0s()()()),2&i){const e=t.XpG(3);t.R7$(5),t.JRh(e.formatDuration(e.consultation.durationMinutes))}}function a(i,l){if(1&i&&(t.j41(0,"div",21)(1,"div",22)(2,"div",23)(3,"label",24),t.EFF(4,"Ended At"),t.k0s(),t.j41(5,"p",25),t.EFF(6),t.k0s()()(),t.DNE(7,I,6,1,"div",27),t.k0s()),2&i){const e=t.XpG(2);t.R7$(6),t.JRh(e.formatDateTime(e.consultation.endTime)),t.R7$(1),t.Y8G("ngIf",e.consultation.durationMinutes)}}function c(i,l){1&i&&(t.j41(0,"span",53),t.nrm(1,"i",54),t.EFF(2," Chat Enabled "),t.k0s())}function p(i,l){1&i&&(t.j41(0,"span",55),t.nrm(1,"i",56),t.EFF(2," Screen Sharing "),t.k0s())}function f(i,l){1&i&&(t.j41(0,"span",57),t.nrm(1,"i",58),t.EFF(2," Recording Enabled "),t.k0s())}function F(i,l){if(1&i){const e=t.RV6();t.j41(0,"button",59),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.onStartConsultation())}),t.nrm(1,"i",60),t.EFF(2," Start Consultation "),t.k0s()}}function w(i,l){if(1&i){const e=t.RV6();t.j41(0,"button",61),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.onJoinConsultation())}),t.nrm(1,"i",18),t.EFF(2," Join Consultation "),t.k0s()}}function j(i,l){if(1&i){const e=t.RV6();t.j41(0,"button",62),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.onEndConsultation())}),t.nrm(1,"i",63),t.EFF(2," End Consultation "),t.k0s()}}function D(i,l){if(1&i&&(t.j41(0,"p",25)(1,"strong"),t.EFF(2,"Reason:"),t.k0s(),t.EFF(3),t.k0s()),2&i){const e=t.XpG(3);t.R7$(3),t.SpI(" ",e.consultation.appointment.reasonForVisit," ")}}const N=function(i){return["/appointments",i]};function V(i,l){if(1&i&&(t.j41(0,"div",14)(1,"div",42)(2,"h6",17),t.nrm(3,"i",64),t.EFF(4," Related Appointment "),t.k0s()(),t.j41(5,"div",20)(6,"p",25)(7,"strong"),t.EFF(8,"Date:"),t.k0s(),t.EFF(9),t.nI1(10,"date"),t.k0s(),t.j41(11,"p",25)(12,"strong"),t.EFF(13,"Time:"),t.k0s(),t.EFF(14),t.k0s(),t.DNE(15,D,4,1,"p",65),t.j41(16,"button",66),t.nrm(17,"i",67),t.EFF(18," View Appointment "),t.k0s()()()),2&i){const e=t.XpG(2);t.R7$(9),t.SpI(" ",t.bMT(10,5,e.consultation.appointment.date)," "),t.R7$(5),t.Lme(" ",e.consultation.appointment.startTime," - ",e.consultation.appointment.endTime," "),t.R7$(1),t.Y8G("ngIf",e.consultation.appointment.reasonForVisit),t.R7$(1),t.Y8G("routerLink",t.eq3(7,N,e.consultation.appointment.id))}}function A(i,l){if(1&i&&(t.j41(0,"div",12)(1,"div",13)(2,"div",14)(3,"div",15)(4,"div",16)(5,"h5",17),t.nrm(6,"i",18),t.EFF(7," Video Consultation "),t.k0s(),t.j41(8,"span",19),t.EFF(9),t.nI1(10,"titlecase"),t.k0s()()(),t.j41(11,"div",20)(12,"div",21)(13,"div",22)(14,"div",23)(15,"label",24),t.EFF(16,"Consultation Type"),t.k0s(),t.j41(17,"p",25),t.EFF(18),t.k0s()()(),t.j41(19,"div",22)(20,"div",23)(21,"label",24),t.EFF(22,"Room ID"),t.k0s(),t.j41(23,"p",26),t.EFF(24),t.k0s()()()(),t.j41(25,"div",21)(26,"div",22)(27,"div",23)(28,"label",24),t.EFF(29,"Scheduled Time"),t.k0s(),t.j41(30,"p",25),t.EFF(31),t.k0s()()(),t.DNE(32,y,6,1,"div",27),t.k0s(),t.DNE(33,a,8,2,"div",28),t.j41(34,"div",21)(35,"div",29)(36,"label",24),t.EFF(37,"Available Features"),t.k0s(),t.j41(38,"div",30),t.DNE(39,c,3,0,"span",31),t.DNE(40,p,3,0,"span",32),t.DNE(41,f,3,0,"span",33),t.k0s()()(),t.j41(42,"div",34),t.DNE(43,F,3,0,"button",35),t.DNE(44,w,3,0,"button",36),t.DNE(45,j,3,0,"button",37),t.j41(46,"button",38),t.nrm(47,"i",39),t.EFF(48," Back to Consultations "),t.k0s()()()()(),t.j41(49,"div",40)(50,"div",41)(51,"div",42)(52,"h6",17),t.nrm(53,"i",43),t.EFF(54," Participants "),t.k0s()(),t.j41(55,"div",20)(56,"div",44)(57,"div",45)(58,"div",46),t.nrm(59,"i",47),t.k0s(),t.j41(60,"div")(61,"h6",17),t.EFF(62),t.k0s(),t.j41(63,"small",48),t.EFF(64),t.k0s()()()(),t.j41(65,"div",49)(66,"div",45)(67,"div",50),t.nrm(68,"i",51),t.k0s(),t.j41(69,"div")(70,"h6",17),t.EFF(71),t.k0s(),t.j41(72,"small",48),t.EFF(73,"Patient"),t.k0s()()()()()(),t.DNE(74,V,19,9,"div",52),t.k0s()()),2&i){const e=t.XpG();t.R7$(8),t.Y8G("ngClass","bg-"+e.getStatusColor(e.consultation.status)),t.R7$(1),t.SpI(" ",t.bMT(10,19,e.consultation.status)," "),t.R7$(9),t.JRh(e.getTypeLabel(e.consultation.type)),t.R7$(6),t.JRh(e.consultation.roomId),t.R7$(7),t.JRh(e.formatDateTime(e.consultation.scheduledStartTime)),t.R7$(1),t.Y8G("ngIf",e.consultation.actualStartTime),t.R7$(1),t.Y8G("ngIf",e.consultation.endTime),t.R7$(6),t.Y8G("ngIf",e.consultation.chatEnabled),t.R7$(1),t.Y8G("ngIf",e.consultation.screenSharingEnabled),t.R7$(1),t.Y8G("ngIf",e.consultation.recordingEnabled),t.R7$(2),t.Y8G("ngIf",e.canStart()),t.R7$(1),t.Y8G("ngIf",e.canJoin()&&!e.canStart()),t.R7$(1),t.Y8G("ngIf",e.canEnd()),t.R7$(17),t.Lme("Dr. ",e.consultation.doctor.firstName," ",e.consultation.doctor.lastName,""),t.R7$(2),t.JRh(e.consultation.doctor.specialization),t.R7$(7),t.Lme("",e.consultation.patient.firstName," ",e.consultation.patient.lastName,""),t.R7$(3),t.Y8G("ngIf",e.consultation.appointment)}}let U=(()=>{class i{constructor(e,n,o,s,r){this.route=e,this.router=n,this.videoConsultationService=o,this.authService=s,this.notificationService=r,this.consultation=null,this.isLoading=!1,this.error=null,this.consultationId=null,this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){this.route.params.subscribe(e=>{this.consultationId=+e.id,this.consultationId&&this.loadConsultation()})}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}loadConsultation(){if(!this.consultationId)return;this.isLoading=!0,this.error=null;const e=this.videoConsultationService.getConsultation(this.consultationId).subscribe({next:n=>{this.consultation=n,this.isLoading=!1},error:n=>{console.error("Failed to load consultation:",n),this.error="Failed to load consultation details",this.isLoading=!1}});this.subscriptions.push(e)}onJoinConsultation(){this.consultation&&(this.videoConsultationService.canJoinConsultation(this.consultation)?this.router.navigate(["/telemedicine/room",this.consultation.roomId]):this.notificationService.addNotification({type:"system",title:"Cannot Join Consultation",message:"The consultation is not available for joining at this time.",priority:"medium"}))}onStartConsultation(){if(this.consultation&&this.consultationId&&"DOCTOR"===this.currentUser?.role){const e=this.videoConsultationService.startConsultation(this.consultationId).subscribe({next:n=>{this.consultation=n,this.notificationService.addNotification({type:"system",title:"Consultation Started",message:"The consultation has been started successfully.",priority:"medium"}),this.router.navigate(["/telemedicine/room",n.roomId])},error:n=>{console.error("Failed to start consultation:",n),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to start the consultation. Please try again.",priority:"high"})}});this.subscriptions.push(e)}}onEndConsultation(){if(this.consultation&&this.consultationId&&"DOCTOR"===this.currentUser?.role){const e=prompt("Enter consultation notes (optional):")||"",n=prompt("Enter diagnosis (optional):")||"",o=prompt("Enter recommendations (optional):")||"",s=this.videoConsultationService.endConsultation(this.consultationId,e,n,o).subscribe({next:r=>{this.consultation=r,this.notificationService.addNotification({type:"system",title:"Consultation Ended",message:"The consultation has been ended successfully.",priority:"medium"})},error:r=>{console.error("Failed to end consultation:",r),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to end the consultation. Please try again.",priority:"high"})}});this.subscriptions.push(s)}}getStatusColor(e){return this.videoConsultationService.getConsultationStatusColor(e)}getTypeLabel(e){return this.videoConsultationService.getConsultationTypeLabel(e)}canJoin(){return!!this.consultation&&this.videoConsultationService.canJoinConsultation(this.consultation)}canStart(){return"DOCTOR"===this.currentUser?.role&&"SCHEDULED"===this.consultation?.status&&this.canJoin()}canEnd(){return"DOCTOR"===this.currentUser?.role&&"IN_PROGRESS"===this.consultation?.status}isActive(){return!!this.consultation&&this.videoConsultationService.isConsultationActive(this.consultation)}formatDateTime(e){return new Date(e).toLocaleString()}formatDuration(e){const n=Math.floor(e/60),o=e%60;return n>0?`${n}h ${o}m`:`${o}m`}static{this.\u0275fac=function(n){return new(n||i)(t.rXU(b.nX),t.rXU(b.Ix),t.rXU(O.i),t.rXU(x.u),t.rXU(R.J))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-video-consultation"]],decls:4,vars:3,consts:[[1,"container-fluid","py-4"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","row",4,"ngIf"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3","text-muted"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"btn","btn-outline-danger","btn-sm","ms-3",3,"click"],[1,"fas","fa-redo","me-1"],[1,"row"],[1,"col-lg-8"],[1,"card","shadow-sm"],[1,"card-header","bg-primary","text-white"],[1,"d-flex","justify-content-between","align-items-center"],[1,"mb-0"],[1,"fas","fa-video","me-2"],[1,"badge",3,"ngClass"],[1,"card-body"],[1,"row","mb-4"],[1,"col-md-6"],[1,"info-item"],[1,"text-muted","small"],[1,"mb-2"],[1,"mb-2","font-monospace"],["class","col-md-6",4,"ngIf"],["class","row mb-4",4,"ngIf"],[1,"col-12"],[1,"d-flex","flex-wrap","gap-2","mt-2"],["class","badge bg-success",4,"ngIf"],["class","badge bg-info",4,"ngIf"],["class","badge bg-warning",4,"ngIf"],[1,"d-flex","gap-3","flex-wrap"],["class","btn btn-success btn-lg",3,"click",4,"ngIf"],["class","btn btn-primary btn-lg",3,"click",4,"ngIf"],["class","btn btn-danger",3,"click",4,"ngIf"],["routerLink","/telemedicine/consultations",1,"btn","btn-outline-secondary"],[1,"fas","fa-arrow-left","me-2"],[1,"col-lg-4"],[1,"card","shadow-sm","mb-4"],[1,"card-header"],[1,"fas","fa-users","me-2"],[1,"participant-item","mb-3"],[1,"d-flex","align-items-center"],[1,"avatar-circle","bg-primary","text-white","me-3"],[1,"fas","fa-user-md"],[1,"text-muted"],[1,"participant-item"],[1,"avatar-circle","bg-info","text-white","me-3"],[1,"fas","fa-user"],["class","card shadow-sm",4,"ngIf"],[1,"badge","bg-success"],[1,"fas","fa-comments","me-1"],[1,"badge","bg-info"],[1,"fas","fa-desktop","me-1"],[1,"badge","bg-warning"],[1,"fas","fa-record-vinyl","me-1"],[1,"btn","btn-success","btn-lg",3,"click"],[1,"fas","fa-play","me-2"],[1,"btn","btn-primary","btn-lg",3,"click"],[1,"btn","btn-danger",3,"click"],[1,"fas","fa-stop","me-2"],[1,"fas","fa-calendar","me-2"],["class","mb-2",4,"ngIf"],[1,"btn","btn-outline-primary","btn-sm",3,"routerLink"],[1,"fas","fa-external-link-alt","me-1"]],template:function(n,o){1&n&&(t.j41(0,"div",0),t.DNE(1,M,6,0,"div",1),t.DNE(2,E,6,1,"div",2),t.DNE(3,A,75,21,"div",3),t.k0s()),2&n&&(t.R7$(1),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error&&!o.isLoading),t.R7$(1),t.Y8G("ngIf",o.consultation&&!o.isLoading))},dependencies:[h.YU,h.bT,b.Wk,h.PV,h.vh],styles:[".info-item[_ngcontent-%COMP%]{margin-bottom:1rem}.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-weight:600;text-transform:uppercase;letter-spacing:.5px;font-size:.75rem}.info-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1rem;color:#333}.participant-item[_ngcontent-%COMP%]{padding:.75rem 0;border-bottom:1px solid #eee}.participant-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.avatar-circle[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.2rem}.card[_ngcontent-%COMP%]{border:none;border-radius:12px}.card[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%]{border-radius:12px 12px 0 0;border-bottom:1px solid rgba(255,255,255,.2)}.btn[_ngcontent-%COMP%]{border-radius:8px;font-weight:500}.btn.btn-lg[_ngcontent-%COMP%]{padding:.75rem 2rem;font-size:1.1rem}.badge[_ngcontent-%COMP%]{font-size:.8rem;padding:.5rem .75rem;border-radius:6px}.font-monospace[_ngcontent-%COMP%]{font-family:Courier New,monospace;background-color:#f8f9fa;padding:.25rem .5rem;border-radius:4px;font-size:.9rem}.spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}.bg-scheduled[_ngcontent-%COMP%]{background-color:#6c757d!important}.bg-waiting_for_doctor[_ngcontent-%COMP%]{background-color:#ffc107!important}.bg-waiting_for_patient[_ngcontent-%COMP%]{background-color:#fd7e14!important}.bg-in_progress[_ngcontent-%COMP%]{background-color:#198754!important}.bg-completed[_ngcontent-%COMP%]{background-color:#0d6efd!important}.bg-cancelled[_ngcontent-%COMP%]{background-color:#dc3545!important}.bg-no_show[_ngcontent-%COMP%]{background-color:#6f42c1!important}.bg-technical_issues[_ngcontent-%COMP%]{background-color:#d63384!important}@media (max-width: 768px){.container-fluid[_ngcontent-%COMP%]{padding:1rem}.btn-lg[_ngcontent-%COMP%]{width:100%;margin-bottom:.5rem}.d-flex.gap-3[_ngcontent-%COMP%]{flex-direction:column;gap:.5rem!important}}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.loading-pulse[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}"]})}}return i})();function L(i,l){1&i&&(t.j41(0,"div",12)(1,"div",13)(2,"span",14),t.EFF(3,"Loading..."),t.k0s()(),t.j41(4,"p",15),t.EFF(5,"Loading consultations..."),t.k0s()())}function G(i,l){if(1&i&&(t.j41(0,"div",16),t.nrm(1,"i",17),t.EFF(2),t.k0s()),2&i){const e=t.XpG();t.R7$(2),t.SpI(" ",e.error," ")}}function X(i,l){1&i&&(t.j41(0,"div",12),t.nrm(1,"i",18),t.j41(2,"h4",19),t.EFF(3,"No Video Consultations"),t.k0s(),t.j41(4,"p",19),t.EFF(5,"You don't have any video consultations scheduled."),t.k0s(),t.j41(6,"a",20),t.nrm(7,"i",21),t.EFF(8," Schedule Appointment "),t.k0s()())}function z(i,l){if(1&i&&(t.j41(0,"p",30),t.nrm(1,"i",40),t.EFF(2),t.k0s()),2&i){const e=t.XpG().$implicit,n=t.XpG(2);t.R7$(2),t.SpI(" ",n.getTypeLabel(e.type)," ")}}function B(i,l){if(1&i){const e=t.RV6();t.j41(0,"button",41),t.bIt("click",function(){t.eBV(e);const o=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onStartConsultation(o))}),t.nrm(1,"i",42),t.EFF(2," Start "),t.k0s()}}function Y(i,l){if(1&i){const e=t.RV6();t.j41(0,"button",43),t.bIt("click",function(){t.eBV(e);const o=t.XpG().$implicit,s=t.XpG(2);return t.Njj(s.onJoinConsultation(o))}),t.nrm(1,"i",44),t.EFF(2," Join "),t.k0s()}}function J(i,l){if(1&i){const e=t.RV6();t.j41(0,"div",23)(1,"div",24)(2,"div",25)(3,"span",26),t.EFF(4),t.k0s(),t.j41(5,"small",19),t.EFF(6),t.k0s()(),t.j41(7,"div",27)(8,"h6",28),t.nrm(9,"i",29),t.EFF(10),t.k0s(),t.j41(11,"p",30),t.nrm(12,"i",31),t.EFF(13),t.k0s(),t.j41(14,"p",30),t.nrm(15,"i",32),t.EFF(16),t.k0s(),t.DNE(17,z,3,1,"p",33),t.k0s(),t.j41(18,"div",34)(19,"div",35),t.DNE(20,B,3,0,"button",36),t.DNE(21,Y,3,0,"button",37),t.j41(22,"button",38),t.bIt("click",function(){const s=t.eBV(e).$implicit,r=t.XpG(2);return t.Njj(r.onViewConsultation(s))}),t.nrm(23,"i",39),t.EFF(24," Details "),t.k0s()()()()()}if(2&i){const e=l.$implicit,n=t.XpG(2);t.R7$(3),t.HbH("bg-"+n.getStatusColor(e.status)),t.R7$(1),t.SpI(" ",n.getStatusLabel(e.status)," "),t.R7$(2),t.SpI(" ",n.formatDateTime(e.scheduledStartTime)," "),t.R7$(4),t.SpI(" ",n.getOtherPartyName(e)," "),t.R7$(3),t.SpI(" ",n.formatDate(e.scheduledStartTime)," "),t.R7$(3),t.SpI(" ",n.formatTime(e.scheduledStartTime)," "),t.R7$(1),t.Y8G("ngIf",e.type),t.R7$(3),t.Y8G("ngIf",n.canStart(e)),t.R7$(1),t.Y8G("ngIf",n.canJoin(e))}}function W(i,l){if(1&i&&(t.j41(0,"div",1),t.DNE(1,J,25,10,"div",22),t.k0s()),2&i){const e=t.XpG();t.R7$(1),t.Y8G("ngForOf",e.consultations)}}let H=(()=>{class i{constructor(e,n,o,s){this.videoConsultationService=e,this.authService=n,this.notificationService=o,this.router=s,this.consultations=[],this.upcomingConsultations=[],this.isLoading=!1,this.error=null,this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){this.loadConsultations(),this.loadUpcomingConsultations()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe())}loadConsultations(){this.isLoading=!0,this.error=null;const e=this.videoConsultationService.getUserConsultations().subscribe({next:n=>{this.consultations=n,this.isLoading=!1},error:n=>{console.error("Failed to load consultations:",n),this.error="Failed to load consultations",this.isLoading=!1}});this.subscriptions.push(e)}loadUpcomingConsultations(){const e=this.videoConsultationService.getUpcomingConsultations().subscribe({next:n=>{this.upcomingConsultations=n},error:n=>{console.error("Failed to load upcoming consultations:",n)}});this.subscriptions.push(e)}onJoinConsultation(e){this.videoConsultationService.canJoinConsultation(e)?this.router.navigate(["/telemedicine/room",e.roomId]):this.notificationService.addNotification({type:"system",title:"Cannot Join Consultation",message:"The consultation is not available for joining at this time.",priority:"medium"})}onViewConsultation(e){this.router.navigate(["/telemedicine/consultation",e.id])}onStartConsultation(e){if("DOCTOR"===this.currentUser?.role){const n=this.videoConsultationService.startConsultation(e.id).subscribe({next:o=>{this.notificationService.addNotification({type:"system",title:"Consultation Started",message:"The consultation has been started successfully.",priority:"medium"}),this.router.navigate(["/telemedicine/room",o.roomId])},error:o=>{console.error("Failed to start consultation:",o),this.notificationService.addNotification({type:"system",title:"Error",message:"Failed to start the consultation. Please try again.",priority:"high"})}});this.subscriptions.push(n)}}getStatusColor(e){return this.videoConsultationService.getConsultationStatusColor(e)}getTypeLabel(e){return this.videoConsultationService.getConsultationTypeLabel(e)}canJoin(e){return this.videoConsultationService.canJoinConsultation(e)}canStart(e){return"DOCTOR"===this.currentUser?.role&&"SCHEDULED"===e.status&&this.videoConsultationService.canJoinConsultation(e)}isActive(e){return this.videoConsultationService.isConsultationActive(e)}formatDateTime(e){return new Date(e).toLocaleString()}formatDuration(e){const n=Math.floor(e/60),o=e%60;return n>0?`${n}h ${o}m`:`${o}m`}getParticipantName(e){return"DOCTOR"===this.currentUser?.role?`${e.patient.firstName} ${e.patient.lastName}`:`Dr. ${e.doctor.firstName} ${e.doctor.lastName}`}getParticipantRole(e){return"DOCTOR"===this.currentUser?.role?"Patient":"Doctor"}onRefresh(){this.loadConsultations(),this.loadUpcomingConsultations()}onCreateConsultation(){this.router.navigate(["/appointments/book"])}getTimeUntilConsultation(e){const n=new Date,s=new Date(e.scheduledStartTime).getTime()-n.getTime();if(s<=0)return"Now";const r=Math.floor(s/6e4),d=Math.floor(r/60),g=Math.floor(d/24);return g>0?`in ${g} day${g>1?"s":""}`:d>0?`in ${d} hour${d>1?"s":""}`:`in ${r} minute${r>1?"s":""}`}isConsultationSoon(e){const n=new Date,r=(new Date(e.scheduledStartTime).getTime()-n.getTime())/6e4;return r<=15&&r>0}getOtherPartyName(e){return this.getParticipantName(e)}getStatusLabel(e){switch(e){case"SCHEDULED":return"Scheduled";case"WAITING_FOR_DOCTOR":return"Waiting for Doctor";case"WAITING_FOR_PATIENT":return"Waiting for Patient";case"IN_PROGRESS":return"In Progress";case"COMPLETED":return"Completed";case"CANCELLED":return"Cancelled";case"NO_SHOW":return"No Show";default:return e}}formatDate(e){return new Date(e).toLocaleDateString()}formatTime(e){return new Date(e).toLocaleTimeString()}static{this.\u0275fac=function(n){return new(n||i)(t.rXU(O.i),t.rXU(x.u),t.rXU(R.J),t.rXU(b.Ix))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-consultation-list"]],decls:15,vars:4,consts:[[1,"container-fluid","py-4"],[1,"row"],[1,"col-12"],[1,"d-flex","justify-content-between","align-items-center","mb-4"],[1,"mb-0"],[1,"fas","fa-video","me-2","text-primary"],[1,"d-flex","gap-2"],[1,"btn","btn-outline-primary",3,"click"],[1,"fas","fa-sync-alt","me-2"],["class","text-center py-5",4,"ngIf"],["class","alert alert-danger","role","alert",4,"ngIf"],["class","row",4,"ngIf"],[1,"text-center","py-5"],["role","status",1,"spinner-border","text-primary"],[1,"visually-hidden"],[1,"mt-3","text-muted"],["role","alert",1,"alert","alert-danger"],[1,"fas","fa-exclamation-triangle","me-2"],[1,"fas","fa-video","fa-3x","text-muted","mb-3"],[1,"text-muted"],["routerLink","/appointments",1,"btn","btn-primary"],[1,"fas","fa-plus","me-2"],["class","col-md-6 col-lg-4 mb-4",4,"ngFor","ngForOf"],[1,"col-md-6","col-lg-4","mb-4"],[1,"card","h-100","shadow-sm"],[1,"card-header","d-flex","justify-content-between","align-items-center"],[1,"badge"],[1,"card-body"],[1,"card-title"],[1,"fas","fa-user-md","me-2"],[1,"card-text","text-muted","small"],[1,"fas","fa-calendar","me-2"],[1,"fas","fa-clock","me-2"],["class","card-text text-muted small",4,"ngIf"],[1,"card-footer","bg-transparent"],[1,"d-flex","gap-2","flex-wrap"],["class","btn btn-success btn-sm",3,"click",4,"ngIf"],["class","btn btn-primary btn-sm",3,"click",4,"ngIf"],[1,"btn","btn-outline-secondary","btn-sm",3,"click"],[1,"fas","fa-eye","me-1"],[1,"fas","fa-tag","me-2"],[1,"btn","btn-success","btn-sm",3,"click"],[1,"fas","fa-play","me-1"],[1,"btn","btn-primary","btn-sm",3,"click"],[1,"fas","fa-video","me-1"]],template:function(n,o){1&n&&(t.j41(0,"div",0)(1,"div",1)(2,"div",2)(3,"div",3)(4,"h2",4),t.nrm(5,"i",5),t.EFF(6," Video Consultations "),t.k0s(),t.j41(7,"div",6)(8,"button",7),t.bIt("click",function(){return o.loadConsultations()}),t.nrm(9,"i",8),t.EFF(10," Refresh "),t.k0s()()(),t.DNE(11,L,6,0,"div",9),t.DNE(12,G,3,1,"div",10),t.DNE(13,X,9,0,"div",9),t.DNE(14,W,2,1,"div",11),t.k0s()()()),2&n&&(t.R7$(11),t.Y8G("ngIf",o.isLoading),t.R7$(1),t.Y8G("ngIf",o.error),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error&&0===o.consultations.length),t.R7$(1),t.Y8G("ngIf",!o.isLoading&&!o.error&&o.consultations.length>0))},dependencies:[h.Sq,h.bT,b.Wk],styles:[".consultation-card[_ngcontent-%COMP%]{transition:transform .2s ease,box-shadow .2s ease}.consultation-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:0 4px 12px #00000026}.status-badge[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem}.consultation-time[_ngcontent-%COMP%]{font-size:.9rem;color:#6c757d}.participant-info[_ngcontent-%COMP%]   .participant-name[_ngcontent-%COMP%]{font-weight:600;color:#495057}.participant-info[_ngcontent-%COMP%]   .participant-role[_ngcontent-%COMP%]{font-size:.85rem;color:#6c757d}.action-buttons[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{font-size:.875rem}.empty-state[_ngcontent-%COMP%]{padding:3rem 1rem}.empty-state[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{opacity:.5}.loading-state[_ngcontent-%COMP%]{padding:3rem 1rem}.consultation-type[_ngcontent-%COMP%]{font-size:.8rem;background-color:#f8f9fa;color:#495057;padding:.2rem .5rem;border-radius:.25rem;display:inline-block}.time-indicator.soon[_ngcontent-%COMP%]{color:#ffc107;font-weight:600}.time-indicator.now[_ngcontent-%COMP%]{color:#dc3545;font-weight:600;animation:_ngcontent-%COMP%_pulse 1s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.card-header[_ngcontent-%COMP%]{background-color:#f8f9fa;border-bottom:1px solid #dee2e6}.card-footer[_ngcontent-%COMP%]{background-color:transparent;border-top:1px solid #dee2e6}"]})}}return i})();var m=u(467),v=u(4412),k=u(3794);let K=(()=>{class i{constructor(){this.localStream$=new v.t(null),this.remoteStreams$=new v.t(new Map),this.peers$=new v.t(new Map),this.connectionStatus$=new v.t("disconnected"),this.screenShareStream$=new v.t(null),this.isScreenSharing$=new v.t(!1),this.localPeerId="",this.roomId="",this.configuration={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"}]},this.participantJoined$=new k.B,this.participantLeft$=new k.B,this.connectionStateChange$=new k.B,this.remoteStream$=new k.B,this.networkQuality$=new k.B}initializeWebRTC(e,n,o,s){var r=this;return(0,m.A)(function*(){r.roomId=e,r.localPeerId=`peer_${n}_${Date.now()}`,r.stompClient=s,r.subscribeToWebRTCMessages(),yield r.getUserMedia(),r.connectionStatus$.next("connecting")})()}getUserMedia(){var e=this;return(0,m.A)(function*(n={video:!0,audio:!0}){try{const o=yield navigator.mediaDevices.getUserMedia(n);return e.localStream$.next(o),e.connectionStatus$.next("connected"),o}catch(o){throw console.error("Error accessing media devices:",o),e.connectionStatus$.next("error"),o}}).apply(this,arguments)}startScreenShare(){var e=this;return(0,m.A)(function*(){try{const n=yield navigator.mediaDevices.getDisplayMedia({video:!0,audio:!0});e.screenShareStream$.next(n),e.isScreenSharing$.next(!0);const o=e.peers$.value,s=n.getVideoTracks()[0];for(const[r,d]of o)if(d.connection){const g=d.connection.getSenders().find(C=>C.track&&"video"===C.track.kind);g&&(yield g.replaceTrack(s))}e.sendWebRTCSignal({type:"SCREEN_SHARE_START"}),s.onended=()=>{e.stopScreenShare()}}catch(n){throw console.error("Error starting screen share:",n),n}})()}stopScreenShare(){var e=this;return(0,m.A)(function*(){const n=e.screenShareStream$.value;if(n){n.getTracks().forEach(s=>s.stop()),e.screenShareStream$.next(null),e.isScreenSharing$.next(!1);const o=e.localStream$.value;if(o){const s=e.peers$.value,r=o.getVideoTracks()[0];for(const[d,g]of s)if(g.connection){const C=g.connection.getSenders().find(_=>_.track&&"video"===_.track.kind);C&&r&&(yield C.replaceTrack(r))}}e.sendWebRTCSignal({type:"SCREEN_SHARE_STOP"})}})()}createPeerConnection(e){const n=new RTCPeerConnection(this.configuration),o=this.localStream$.value;return o&&o.getTracks().forEach(s=>{n.addTrack(s,o)}),n.ontrack=s=>{const r=s.streams[0],d=this.remoteStreams$.value;d.set(e,r),this.remoteStreams$.next(new Map(d)),this.remoteStream$.next(r)},n.onicecandidate=s=>{s.candidate&&this.sendWebRTCSignal({type:"ICE_CANDIDATE",targetPeerId:e,data:s.candidate})},n.onconnectionstatechange=()=>{console.log(`Connection state with ${e}:`,n.connectionState)},n}createOffer(e){var n=this;return(0,m.A)(function*(){const o=n.peers$.value;let s=o.get(e);if(s||(s={userId:0,userRole:"",peerId:e,connection:n.createPeerConnection(e)},o.set(e,s),n.peers$.next(new Map(o))),s.connection){const r=yield s.connection.createOffer();yield s.connection.setLocalDescription(r),n.sendWebRTCSignal({type:"OFFER",targetPeerId:e,data:r})}})()}handleOffer(e,n){var o=this;return(0,m.A)(function*(){const s=o.peers$.value;let r=s.get(e);if(r||(r={userId:0,userRole:"",peerId:e,connection:o.createPeerConnection(e)},s.set(e,r),o.peers$.next(new Map(s))),r.connection){yield r.connection.setRemoteDescription(n);const d=yield r.connection.createAnswer();yield r.connection.setLocalDescription(d),o.sendWebRTCSignal({type:"ANSWER",targetPeerId:e,data:d})}})()}handleAnswer(e,n){var o=this;return(0,m.A)(function*(){const r=o.peers$.value.get(e);r&&r.connection&&(yield r.connection.setRemoteDescription(n))})()}handleIceCandidate(e,n){var o=this;return(0,m.A)(function*(){const r=o.peers$.value.get(e);r&&r.connection&&(yield r.connection.addIceCandidate(n))})()}sendWebRTCSignal(e){this.stompClient&&this.stompClient.connected&&this.stompClient.send(`/app/webrtc/${this.roomId}/signal`,{},JSON.stringify(e))}subscribeToWebRTCMessages(){this.stompClient&&this.stompClient.connected&&this.stompClient.subscribe(`/topic/webrtc/${this.roomId}/${this.localPeerId}`,e=>{const n=JSON.parse(e.body);this.handleWebRTCMessage(n)})}handleWebRTCMessage(e){var n=this;return(0,m.A)(function*(){switch(e.type){case"OFFER":e.fromPeerId&&e.data&&(yield n.handleOffer(e.fromPeerId,e.data));break;case"ANSWER":e.fromPeerId&&e.data&&(yield n.handleAnswer(e.fromPeerId,e.data));break;case"ICE_CANDIDATE":e.fromPeerId&&e.data&&(yield n.handleIceCandidate(e.fromPeerId,e.data));break;case"USER_JOINED":e.fromPeerId&&(yield n.createOffer(e.fromPeerId));break;case"USER_LEFT":e.fromPeerId&&n.removePeer(e.fromPeerId);break;case"EXISTING_PEER":case"SCREEN_SHARE_START":case"SCREEN_SHARE_STOP":break;case"SESSION_END":n.endSession()}})()}removePeer(e){const n=this.peers$.value,o=n.get(e);o&&o.connection&&o.connection.close(),n.delete(e),this.peers$.next(new Map(n));const s=this.remoteStreams$.value;s.delete(e),this.remoteStreams$.next(new Map(s))}endSession(){const e=this.peers$.value;for(const[s,r]of e)r.connection&&r.connection.close();const n=this.localStream$.value;n&&n.getTracks().forEach(s=>s.stop());const o=this.screenShareStream$.value;o&&o.getTracks().forEach(s=>s.stop()),this.localStream$.next(null),this.remoteStreams$.next(new Map),this.peers$.next(new Map),this.screenShareStream$.next(null),this.isScreenSharing$.next(!1),this.connectionStatus$.next("disconnected")}getLocalStream(){return this.localStream$.asObservable()}getRemoteStreams(){return this.remoteStreams$.asObservable()}getPeers(){return this.peers$.asObservable()}getConnectionStatus(){return this.connectionStatus$.asObservable()}getScreenShareStream(){return this.screenShareStream$.asObservable()}getIsScreenSharing(){return this.isScreenSharing$.asObservable()}isAudioEnabled(){const e=this.localStream$.value;if(e){const n=e.getAudioTracks()[0];return!!n&&n.enabled}return!1}isVideoEnabled(){const e=this.localStream$.value;if(e){const n=e.getVideoTracks()[0];return!!n&&n.enabled}return!1}initializeLocalMedia(){var e=this;return(0,m.A)(function*(){yield e.getUserMedia()})()}joinRoom(e,n){var o=this;return(0,m.A)(function*(s,r,d="participant"){o.roomId=s,o.localPeerId=`peer_${r}_${Date.now()}`,o.connectionStatus$.next("connecting"),o.stompClient&&o.stompClient.connected&&(o.stompClient.send(`/app/webrtc/${s}/join`,{},JSON.stringify({userRole:d})),o.connectionStatus$.next("connected"),o.connectionStateChange$.next("connected"))}).apply(this,arguments)}leaveRoom(){this.stompClient&&this.stompClient.connected&&this.stompClient.send(`/app/webrtc/${this.roomId}/leave`,{},"{}"),this.endSession()}getLocalStreamValue(){return this.localStream$.value}toggleVideo(e){const n=this.localStream$.value;if(n){const o=n.getVideoTracks()[0];o&&(o.enabled=e)}}toggleAudio(e){const n=this.localStream$.value;if(n){const o=n.getAudioTracks()[0];o&&(o.enabled=e)}}onRemoteStream(){return this.remoteStream$.asObservable()}onConnectionStateChange(){return this.connectionStateChange$.asObservable()}onParticipantJoined(){return this.participantJoined$.asObservable()}onParticipantLeft(){return this.participantLeft$.asObservable()}onNetworkQuality(){return this.networkQuality$.asObservable()}monitorNetworkQuality(){setInterval(()=>{const e=this.peers$.value;for(const[n,o]of e)o.connection&&o.connection.getStats().then(s=>{s.forEach(r=>{if("inbound-rtp"===r.type&&"video"===r.kind){const d=r.packetsLost||0,C=d/(d+(r.packetsReceived||0));let _="excellent";C>.05?_="poor":C>.02?_="fair":C>.01&&(_="good"),this.networkQuality$.next(_)}})}).catch(s=>{console.warn("Failed to get connection stats:",s)})},5e3)}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275prov=t.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var Q=u(5538),Z=u(1129);let q=(()=>{class i{constructor(){this.mediaRecorder=null,this.recordedChunks=[],this.isRecording$=new v.t(!1),this.recordingDuration$=new v.t(0),this.recordingStartTime=0}startRecording(e){var n=this;return(0,m.A)(function*(o,s={}){try{const d={videoBitsPerSecond:25e5,audioBitsPerSecond:128e3,mimeType:"video/webm;codecs=vp9,opus",...s};MediaRecorder.isTypeSupported(d.mimeType)||(d.mimeType="video/webm",MediaRecorder.isTypeSupported(d.mimeType)||(d.mimeType="video/mp4")),n.mediaRecorder=new MediaRecorder(o,d),n.recordedChunks=[],n.mediaRecorder.ondataavailable=g=>{g.data.size>0&&n.recordedChunks.push(g.data)},n.mediaRecorder.onstop=()=>{n.isRecording$.next(!1),n.stopDurationTimer()},n.mediaRecorder.onerror=g=>{console.error("MediaRecorder error:",g),n.isRecording$.next(!1),n.stopDurationTimer()},n.mediaRecorder.start(1e3),n.isRecording$.next(!0),n.startDurationTimer()}catch(r){throw console.error("Failed to start recording:",r),new Error("Failed to start recording. Please check your browser permissions.")}}).apply(this,arguments)}stopRecording(){return new Promise((e,n)=>{this.mediaRecorder&&"inactive"!==this.mediaRecorder.state?(this.mediaRecorder.onstop=()=>{const o=new Blob(this.recordedChunks,{type:this.mediaRecorder?.mimeType||"video/webm"});this.isRecording$.next(!1),this.stopDurationTimer(),e(o)},this.mediaRecorder.stop()):n(new Error("No active recording to stop"))})}pauseRecording(){this.mediaRecorder&&"recording"===this.mediaRecorder.state&&(this.mediaRecorder.pause(),this.stopDurationTimer())}resumeRecording(){this.mediaRecorder&&"paused"===this.mediaRecorder.state&&(this.mediaRecorder.resume(),this.startDurationTimer())}isRecording(){return this.isRecording$.asObservable()}getRecordingDuration(){return this.recordingDuration$.asObservable()}getRecordingState(){return this.mediaRecorder?.state||"inactive"}downloadRecording(e,n="consultation-recording.webm"){const o=URL.createObjectURL(e),s=document.createElement("a");s.href=o,s.download=n,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(o)}blobToBase64(e){return new Promise((n,o)=>{const s=new FileReader;s.onload=()=>{const d=s.result.split(",")[1];n(d)},s.onerror=o,s.readAsDataURL(e)})}formatDuration(e){const n=Math.floor(e/3600),o=Math.floor(e%3600/60),s=Math.floor(e%60);return n>0?`${n.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`:`${o.toString().padStart(2,"0")}:${s.toString().padStart(2,"0")}`}startDurationTimer(){this.recordingStartTime=Date.now(),this.durationInterval=setInterval(()=>{const e=(Date.now()-this.recordingStartTime)/1e3;this.recordingDuration$.next(e)},1e3)}stopDurationTimer(){this.durationInterval&&(clearInterval(this.durationInterval),this.durationInterval=null),this.recordingDuration$.next(0)}cleanup(){this.mediaRecorder&&"inactive"!==this.mediaRecorder.state&&this.mediaRecorder.stop(),this.stopDurationTimer(),this.recordedChunks=[],this.isRecording$.next(!1)}static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275prov=t.jDH({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();const tt=["localVideo"],et=["remoteVideo"];function nt(i,l){if(1&i&&(t.j41(0,"div",5)(1,"div",6)(2,"div",7)(3,"span",8),t.EFF(4,"Connecting..."),t.k0s()(),t.j41(5,"h4"),t.EFF(6),t.k0s(),t.j41(7,"p",9),t.EFF(8,"Please wait while we connect you to the consultation..."),t.k0s()()()),2&i){const e=t.XpG();t.R7$(6),t.JRh(e.connectionStatus)}}function ot(i,l){if(1&i){const e=t.RV6();t.j41(0,"div",10)(1,"div",11),t.nrm(2,"i",12),t.j41(3,"h4"),t.EFF(4,"Connection Error"),t.k0s(),t.j41(5,"p"),t.EFF(6),t.k0s(),t.j41(7,"div",13)(8,"button",14),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.initializeConsultation())}),t.nrm(9,"i",15),t.EFF(10," Retry "),t.k0s(),t.j41(11,"button",16),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.leaveConsultation())}),t.nrm(12,"i",17),t.EFF(13," Leave "),t.k0s()()()()}if(2&i){const e=t.XpG();t.R7$(6),t.JRh(e.error)}}function it(i,l){if(1&i&&(t.j41(0,"div",18),t.nrm(1,"div",19),t.j41(2,"span"),t.EFF(3),t.k0s()()),2&i){const e=t.XpG();t.R7$(3),t.SpI("REC ",e.formatRecordingDuration(),"")}}function st(i,l){if(1&i&&(t.j41(0,"div",70)(1,"div",71),t.nrm(2,"i",72),t.j41(3,"p"),t.EFF(4),t.k0s()()()),2&i){const e=t.XpG(2);t.R7$(4),t.SpI("Waiting for ","DOCTOR"===(null==e.currentUser?null:e.currentUser.role)?"patient":"doctor","...")}}function rt(i,l){1&i&&(t.j41(0,"div",73),t.nrm(1,"i",74),t.k0s())}function at(i,l){1&i&&(t.j41(0,"span",75),t.nrm(1,"i",76),t.k0s())}function ct(i,l){1&i&&(t.j41(0,"span",77),t.nrm(1,"i",78),t.k0s())}function lt(i,l){if(1&i&&(t.j41(0,"h6",57),t.nrm(1,"i",79),t.EFF(2),t.k0s()),2&i){const e=t.XpG(2);t.R7$(2),t.SpI(" Consultation with ","DOCTOR"===(null==e.currentUser?null:e.currentUser.role)?e.consultation.patient.firstName+" "+e.consultation.patient.lastName:"Dr. "+e.consultation.doctor.firstName+" "+e.consultation.doctor.lastName," ")}}function dt(i,l){if(1&i&&(t.j41(0,"span",80),t.EFF(1),t.k0s()),2&i){const e=t.XpG(2);t.R7$(1),t.JRh(e.messages.length)}}function ut(i,l){if(1&i){const e=t.RV6();t.j41(0,"div",81)(1,"div",56)(2,"h6",57),t.nrm(3,"i",82),t.EFF(4," Participants "),t.k0s(),t.j41(5,"button",59),t.bIt("click",function(){t.eBV(e);const o=t.XpG(2);return t.Njj(o.toggleParticipants())}),t.nrm(6,"i",60),t.k0s()(),t.j41(7,"div",61)(8,"div",83)(9,"div",84),t.nrm(10,"i",85),t.k0s(),t.j41(11,"div",86)(12,"div",87),t.EFF(13),t.k0s(),t.j41(14,"div",88),t.EFF(15),t.k0s()(),t.j41(16,"div",89),t.nrm(17,"i",90),t.k0s()(),t.j41(18,"div",83)(19,"div",91),t.nrm(20,"i",74),t.k0s(),t.j41(21,"div",86)(22,"div",87),t.EFF(23),t.k0s(),t.j41(24,"div",88),t.EFF(25,"Patient"),t.k0s()(),t.j41(26,"div",89),t.nrm(27,"i",90),t.k0s()()()()}if(2&i){const e=t.XpG(2);t.AVh("open",e.showParticipants),t.R7$(13),t.Lme("Dr. ",e.consultation.doctor.firstName," ",e.consultation.doctor.lastName,""),t.R7$(2),t.JRh(e.consultation.doctor.specialization),t.R7$(8),t.Lme("",e.consultation.patient.firstName," ",e.consultation.patient.lastName,"")}}function pt(i,l){if(1&i&&(t.j41(0,"div",92)(1,"div",93)(2,"div",94),t.EFF(3),t.k0s(),t.j41(4,"div",95),t.EFF(5),t.k0s()()()),2&i){const e=l.$implicit,n=t.XpG(2);t.AVh("own-message",e.senderId===(null==n.currentUser?null:n.currentUser.id)),t.R7$(3),t.JRh(e.content),t.R7$(2),t.JRh(n.formatTime(e.timestamp))}}function mt(i,l){1&i&&(t.j41(0,"div",96),t.nrm(1,"i",97),t.j41(2,"p",9),t.EFF(3,"No messages yet. Start the conversation!"),t.k0s()())}function gt(i,l){if(1&i){const e=t.RV6();t.j41(0,"div",20)(1,"div",21),t.nrm(2,"video",22,23),t.DNE(4,st,5,1,"div",24),t.k0s(),t.j41(5,"div",25),t.nrm(6,"video",26,27),t.DNE(8,rt,2,0,"div",28),t.j41(9,"div",29),t.DNE(10,at,2,0,"span",30),t.DNE(11,ct,2,0,"span",31),t.k0s()(),t.j41(12,"div",32)(13,"div",33),t.DNE(14,lt,3,1,"h6",34),t.j41(15,"small",9),t.EFF(16),t.k0s()(),t.j41(17,"div",35)(18,"button",36),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleParticipants())}),t.nrm(19,"i",37),t.k0s(),t.j41(20,"button",38),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleChat())}),t.nrm(21,"i",39),t.DNE(22,dt,2,1,"span",40),t.k0s()()(),t.j41(23,"div",41)(24,"div",42)(25,"button",43),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleAudio())}),t.nrm(26,"i",44),t.k0s(),t.j41(27,"button",45),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleVideo())}),t.nrm(28,"i",44),t.k0s(),t.j41(29,"button",46),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleScreenShare())}),t.nrm(30,"i",47),t.k0s(),t.j41(31,"button",48),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleChat())}),t.nrm(32,"i",39),t.k0s(),t.j41(33,"button",49),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.endConsultation())}),t.nrm(34,"i",50),t.k0s()(),t.j41(35,"div",51)(36,"span",52),t.EFF(37),t.k0s(),t.nrm(38,"div",53),t.k0s()(),t.DNE(39,ut,28,7,"div",54),t.j41(40,"div",55)(41,"div",56)(42,"h6",57),t.nrm(43,"i",58),t.EFF(44," Consultation Chat "),t.k0s(),t.j41(45,"button",59),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.toggleChat())}),t.nrm(46,"i",60),t.k0s()(),t.j41(47,"div",61)(48,"div",62),t.DNE(49,pt,6,4,"div",63),t.DNE(50,mt,4,0,"div",64),t.k0s(),t.j41(51,"div",65)(52,"div",66)(53,"input",67),t.bIt("ngModelChange",function(o){t.eBV(e);const s=t.XpG();return t.Njj(s.newMessage=o)})("keyup.enter",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.sendMessage())}),t.k0s(),t.j41(54,"button",68),t.bIt("click",function(){t.eBV(e);const o=t.XpG();return t.Njj(o.sendMessage())}),t.nrm(55,"i",69),t.k0s()()()()()()}if(2&i){const e=t.sdS(3),n=t.sdS(7),o=t.XpG();t.R7$(4),t.Y8G("ngIf",!(null!=e&&e.srcObject)),t.R7$(1),t.AVh("screen-sharing",o.isScreenSharing),t.R7$(3),t.Y8G("ngIf",!(null!=n&&n.srcObject)),t.R7$(2),t.Y8G("ngIf",!o.isVideoEnabled),t.R7$(1),t.Y8G("ngIf",!o.isAudioEnabled),t.R7$(1),t.AVh("visible",o.isControlsVisible),t.R7$(2),t.Y8G("ngIf",o.consultation),t.R7$(2),t.SpI("Room: ",o.roomId,""),t.R7$(2),t.AVh("active",o.showParticipants),t.R7$(2),t.AVh("active",o.isChatOpen),t.R7$(2),t.Y8G("ngIf",o.messages.length>0),t.R7$(1),t.AVh("visible",o.isControlsVisible),t.R7$(2),t.AVh("btn-danger",!o.isAudioEnabled)("btn-success",o.isAudioEnabled),t.R7$(1),t.AVh("fa-microphone",o.isAudioEnabled)("fa-microphone-slash",!o.isAudioEnabled),t.R7$(1),t.AVh("btn-danger",!o.isVideoEnabled)("btn-success",o.isVideoEnabled),t.R7$(1),t.AVh("fa-video",o.isVideoEnabled)("fa-video-slash",!o.isVideoEnabled),t.R7$(1),t.AVh("btn-primary",o.isScreenSharing)("btn-outline-light",!o.isScreenSharing),t.R7$(2),t.AVh("active",o.isChatOpen),t.R7$(6),t.JRh(o.connectionStatus),t.R7$(1),t.AVh("connected","Connected"===o.connectionStatus)("connecting",o.connectionStatus.includes("Connecting"))("error",o.connectionStatus.includes("failed")||o.connectionStatus.includes("disconnected")),t.R7$(1),t.Y8G("ngIf",o.consultation),t.R7$(1),t.AVh("open",o.isChatOpen),t.R7$(9),t.Y8G("ngForOf",o.messages),t.R7$(1),t.Y8G("ngIf",0===o.messages.length),t.R7$(3),t.Y8G("ngModel",o.newMessage),t.R7$(1),t.Y8G("disabled",!o.newMessage.trim())}}const ft=[{path:"",canActivate:[T.q],children:[{path:"",redirectTo:"consultations",pathMatch:"full"},{path:"consultations",component:H},{path:"consultation/:id",component:U},{path:"room/:roomId",component:(()=>{class i{constructor(e,n,o,s,r,d,g,C,_){this.route=e,this.router=n,this.videoConsultationService=o,this.webRTCService=s,this.chatService=r,this.presenceService=d,this.authService=g,this.notificationService=C,this.recordingService=_,this.consultation=null,this.roomId="",this.isVideoEnabled=!0,this.isAudioEnabled=!0,this.isScreenSharing=!1,this.isCallActive=!1,this.isConnecting=!1,this.isChatOpen=!1,this.isControlsVisible=!0,this.showParticipants=!1,this.isRecording=!1,this.recordingDuration=0,this.recordingConsent=!1,this.messages=[],this.newMessage="",this.error=null,this.connectionStatus="Connecting...",this.subscriptions=[],this.currentUser=this.authService.getCurrentUser()}ngOnInit(){this.route.params.subscribe(e=>{this.roomId=e.roomId,this.roomId&&this.initializeConsultation()}),this.resetControlsTimeout()}ngOnDestroy(){this.subscriptions.forEach(e=>e.unsubscribe()),this.webRTCService.leaveRoom(),this.isRecording&&this.recordingService.cleanup(),this.controlsTimeout&&clearTimeout(this.controlsTimeout)}initializeConsultation(){var e=this;return(0,m.A)(function*(){try{e.isConnecting=!0,e.connectionStatus="Loading consultation...";const n=e.videoConsultationService.getConsultationByRoomId(e.roomId).subscribe({next:o=>{e.consultation=o,e.continueInitialization()},error:o=>{console.error("Failed to get consultation:",o),e.error="Failed to load consultation details",e.isConnecting=!1}});e.subscriptions.push(n)}catch(n){console.error("Failed to initialize consultation:",n),e.error="Failed to join the consultation. Please try again.",e.isConnecting=!1}})()}continueInitialization(){var e=this;return(0,m.A)(function*(){try{if(!e.isAuthorizedUser())return void(e.error="You are not authorized to join this consultation");e.connectionStatus="Connecting to video call...",yield e.initializeWebRTC(),e.isConnecting=!1,e.isCallActive=!0,e.connectionStatus="Connected"}catch(n){console.error("Failed to continue initialization:",n),e.error="Failed to initialize video consultation",e.isConnecting=!1}})()}initializeWebRTC(){var e=this;return(0,m.A)(function*(){try{e.presenceService.updatePresence("BUSY","In video consultation");const n=e.chatService.stompClient;yield e.webRTCService.initializeWebRTC(e.roomId,e.currentUser.id,e.currentUser.role.toLowerCase(),n),yield e.webRTCService.initializeLocalMedia(),yield e.webRTCService.joinRoom(e.roomId,e.currentUser.id,e.currentUser.role.toLowerCase()),e.setupWebRTCEventListeners(),e.localVideo&&(e.localVideo.nativeElement.srcObject=e.webRTCService.getLocalStreamValue())}catch(n){throw console.error("WebRTC initialization failed:",n),e.presenceService.updatePresence("ONLINE"),new Error("Failed to initialize video call")}})()}setupWebRTCEventListeners(){this.webRTCService.onRemoteStream().subscribe(e=>{this.remoteVideo&&(this.remoteVideo.nativeElement.srcObject=e)}),this.webRTCService.onConnectionStateChange().subscribe(e=>{this.connectionStatus=e,("disconnected"===e||"failed"===e)&&this.handleConnectionError()}),this.webRTCService.onParticipantJoined().subscribe(e=>{this.notificationService.addNotification({type:"system",title:"Participant Joined",message:`${e.name} joined the consultation`,priority:"low"})}),this.webRTCService.onParticipantLeft().subscribe(e=>{this.notificationService.addNotification({type:"system",title:"Participant Left",message:`${e.name} left the consultation`,priority:"low"})})}isAuthorizedUser(){return!!this.consultation&&(this.consultation.doctor.id===this.currentUser.id||this.consultation.patient.id===this.currentUser.id)}handleConnectionError(){this.error="Connection lost. Attempting to reconnect..."}toggleVideo(){this.isVideoEnabled=!this.isVideoEnabled,this.webRTCService.toggleVideo(this.isVideoEnabled),this.resetControlsTimeout()}toggleAudio(){this.isAudioEnabled=!this.isAudioEnabled,this.webRTCService.toggleAudio(this.isAudioEnabled),this.resetControlsTimeout()}toggleScreenShare(){var e=this;return(0,m.A)(function*(){try{e.isScreenSharing?(yield e.webRTCService.stopScreenShare(),e.isScreenSharing=!1):(yield e.webRTCService.startScreenShare(),e.isScreenSharing=!0),e.resetControlsTimeout()}catch(n){console.error("Screen share toggle failed:",n),e.notificationService.addNotification({type:"system",title:"Screen Share Error",message:"Failed to toggle screen sharing",priority:"medium"})}})()}toggleChat(){this.isChatOpen=!this.isChatOpen,this.isChatOpen&&this.loadChatMessages()}toggleParticipants(){this.showParticipants=!this.showParticipants}onMouseMove(){this.isControlsVisible=!0,this.resetControlsTimeout()}resetControlsTimeout(){this.controlsTimeout&&clearTimeout(this.controlsTimeout),this.controlsTimeout=setTimeout(()=>{this.isControlsVisible=!1},5e3)}loadChatMessages(){if(!this.consultation?.appointment?.id)return;this.messages=[];const e=this.chatService.messages$.subscribe({next:n=>{this.messages.push(n)},error:n=>{console.error("Failed to load chat messages:",n)}});this.subscriptions.push(e)}sendMessage(){if(!this.newMessage.trim())return;const e={id:Date.now(),content:this.newMessage.trim(),sender:this.currentUser,timestamp:(new Date).toISOString(),status:"SENT"};this.messages.push(e),this.newMessage="",console.log("Video consultation message sent:",e)}endConsultation(){var e=this;return(0,m.A)(function*(){if("DOCTOR"===e.currentUser.role){if(confirm("Are you sure you want to end this consultation?"))try{if(e.isCallActive=!1,e.webRTCService.endSession(),e.presenceService.updatePresence("ONLINE"),e.consultation?.id){const o=e.videoConsultationService.endConsultation(e.consultation.id,"","","").subscribe({next:()=>{e.notificationService.addNotification({type:"system",title:"Consultation Ended",message:"The consultation has been ended successfully.",priority:"medium"}),e.router.navigate(["/telemedicine/consultations"])},error:s=>{console.error("Failed to end consultation:",s),e.notificationService.addNotification({type:"system",title:"Error",message:"Failed to end the consultation.",priority:"high"})}});e.subscriptions.push(o)}}catch(o){console.error("Failed to end consultation:",o)}}else e.leaveConsultation()})()}leaveConsultation(){confirm("Are you sure you want to leave this consultation?")&&(this.isCallActive=!1,this.webRTCService.leaveRoom(),this.presenceService.updatePresence("ONLINE"),this.router.navigate(["/telemedicine/consultations"]))}formatTime(e){return new Date(e).toLocaleTimeString()}toggleRecording(){var e=this;return(0,m.A)(function*(){e.isRecording?yield e.stopRecording():yield e.startRecording()})()}startRecording(){var e=this;return(0,m.A)(function*(){try{if(!e.recordingConsent){if(!confirm("Do you consent to recording this consultation? The recording will be saved for medical records."))return;e.recordingConsent=!0}const n=e.webRTCService.getLocalStreamValue();if(!n)throw new Error("No local stream available for recording");yield e.recordingService.startRecording(n),e.isRecording=!0,e.recordingService.getRecordingDuration().subscribe(o=>{e.recordingDuration=o}),e.notificationService.addNotification({type:"system",title:"Recording Started",message:"Consultation recording has started",priority:"medium"})}catch(n){console.error("Failed to start recording:",n),e.notificationService.addNotification({type:"system",title:"Recording Failed",message:"Failed to start recording. Please try again.",priority:"high"})}})()}stopRecording(){var e=this;return(0,m.A)(function*(){try{const n=yield e.recordingService.stopRecording();e.isRecording=!1,e.recordingDuration=0;const o=`consultation-${e.roomId}-${(new Date).toISOString().split("T")[0]}.webm`;e.recordingService.downloadRecording(n,o),e.notificationService.addNotification({type:"system",title:"Recording Saved",message:"Consultation recording has been saved",priority:"medium"})}catch(n){console.error("Failed to stop recording:",n),e.notificationService.addNotification({type:"system",title:"Recording Error",message:"Failed to save recording. Please try again.",priority:"high"})}})()}formatRecordingDuration(){return this.recordingService.formatDuration(this.recordingDuration)}static{this.\u0275fac=function(n){return new(n||i)(t.rXU(b.nX),t.rXU(b.Ix),t.rXU(O.i),t.rXU(K),t.rXU(Q.m),t.rXU(Z.B),t.rXU(x.u),t.rXU(R.J),t.rXU(q))}}static{this.\u0275cmp=t.VBU({type:i,selectors:[["app-consultation-room"]],viewQuery:function(n,o){if(1&n&&(t.GBs(tt,5),t.GBs(et,5)),2&n){let s;t.mGM(s=t.lsd())&&(o.localVideo=s.first),t.mGM(s=t.lsd())&&(o.remoteVideo=s.first)}},decls:5,vars:4,consts:[[1,"consultation-room",3,"mousemove"],["class","connecting-overlay",4,"ngIf"],["class","error-overlay",4,"ngIf"],["class","recording-indicator",4,"ngIf"],["class","video-container",4,"ngIf"],[1,"connecting-overlay"],[1,"connecting-content"],["role","status",1,"spinner-border","text-primary","mb-3"],[1,"visually-hidden"],[1,"text-muted"],[1,"error-overlay"],[1,"error-content"],[1,"fas","fa-exclamation-triangle","text-danger","mb-3"],[1,"mt-3"],[1,"btn","btn-primary","me-2",3,"click"],[1,"fas","fa-redo","me-1"],[1,"btn","btn-outline-secondary",3,"click"],[1,"fas","fa-arrow-left","me-1"],[1,"recording-indicator"],[1,"recording-dot"],[1,"video-container"],[1,"remote-video-container"],["autoplay","","playsinline","",1,"remote-video"],["remoteVideo",""],["class","video-placeholder remote-placeholder",4,"ngIf"],[1,"local-video-container"],["autoplay","","playsinline","","muted","",1,"local-video"],["localVideo",""],["class","video-placeholder local-placeholder",4,"ngIf"],[1,"video-status"],["class","status-indicator video-off",4,"ngIf"],["class","status-indicator audio-off",4,"ngIf"],[1,"top-bar"],[1,"consultation-info"],["class","mb-0",4,"ngIf"],[1,"top-actions"],[1,"btn","btn-outline-light","btn-sm","me-2",3,"click"],[1,"fas","fa-users"],[1,"btn","btn-outline-light","btn-sm",3,"click"],[1,"fas","fa-comments"],["class","badge bg-primary ms-1",4,"ngIf"],[1,"bottom-controls"],[1,"control-buttons"],["title","Toggle Microphone",1,"btn","control-btn",3,"click"],[1,"fas"],["title","Toggle Camera",1,"btn","control-btn",3,"click"],["title","Share Screen",1,"btn","control-btn",3,"click"],[1,"fas","fa-desktop"],["title","Toggle Chat",1,"btn","control-btn","btn-outline-light",3,"click"],["title","End Consultation",1,"btn","control-btn","btn-danger",3,"click"],[1,"fas","fa-phone-slash"],[1,"connection-status"],[1,"status-text"],[1,"status-indicator"],["class","participants-panel",3,"open",4,"ngIf"],[1,"chat-panel"],[1,"panel-header"],[1,"mb-0"],[1,"fas","fa-comments","me-2"],[1,"btn","btn-sm","btn-outline-light",3,"click"],[1,"fas","fa-times"],[1,"panel-content"],[1,"messages-container"],["class","message-item",3,"own-message",4,"ngFor","ngForOf"],["class","no-messages",4,"ngIf"],[1,"message-input"],[1,"input-group"],["type","text","placeholder","Type a message...",1,"form-control",3,"ngModel","ngModelChange","keyup.enter"],[1,"btn","btn-primary",3,"disabled","click"],[1,"fas","fa-paper-plane"],[1,"video-placeholder","remote-placeholder"],[1,"placeholder-content"],[1,"fas","fa-user-circle"],[1,"video-placeholder","local-placeholder"],[1,"fas","fa-user"],[1,"status-indicator","video-off"],[1,"fas","fa-video-slash"],[1,"status-indicator","audio-off"],[1,"fas","fa-microphone-slash"],[1,"fas","fa-video","me-2"],[1,"badge","bg-primary","ms-1"],[1,"participants-panel"],[1,"fas","fa-users","me-2"],[1,"participant-item"],[1,"participant-avatar","bg-primary"],[1,"fas","fa-user-md"],[1,"participant-info"],[1,"participant-name"],[1,"participant-role"],[1,"participant-status"],["title","Online",1,"fas","fa-circle","text-success"],[1,"participant-avatar","bg-info"],[1,"message-item"],[1,"message-content"],[1,"message-text"],[1,"message-time"],[1,"no-messages"],[1,"fas","fa-comments","text-muted"]],template:function(n,o){1&n&&(t.j41(0,"div",0),t.bIt("mousemove",function(){return o.onMouseMove()}),t.DNE(1,nt,9,1,"div",1),t.DNE(2,ot,14,1,"div",2),t.DNE(3,it,4,1,"div",3),t.DNE(4,gt,56,53,"div",4),t.k0s()),2&n&&(t.R7$(1),t.Y8G("ngIf",o.isConnecting),t.R7$(1),t.Y8G("ngIf",o.error),t.R7$(1),t.Y8G("ngIf",o.isRecording),t.R7$(1),t.Y8G("ngIf",o.isCallActive&&!o.error))},dependencies:[h.Sq,h.bT,S.me,S.BC,S.vS],styles:[".consultation-room[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;background:#000;overflow:hidden;z-index:1000}.recording-duration[_ngcontent-%COMP%]{font-size:.8rem;font-weight:700;color:#fff}.btn.btn-danger[_ngcontent-%COMP%]   .recording-duration[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_pulse 1s infinite}@keyframes _ngcontent-%COMP%_pulse{0%{opacity:1}50%{opacity:.5}to{opacity:1}}.recording-indicator[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;background:rgba(220,53,69,.9);color:#fff;padding:8px 12px;border-radius:20px;font-size:.9rem;font-weight:700;display:flex;align-items:center;gap:8px;z-index:1000}.recording-indicator[_ngcontent-%COMP%]   .recording-dot[_ngcontent-%COMP%]{width:8px;height:8px;background:#fff;border-radius:50%;animation:_ngcontent-%COMP%_pulse 1s infinite}.connecting-overlay[_ngcontent-%COMP%], .error-overlay[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,.9);display:flex;align-items:center;justify-content:center;z-index:1001}.connecting-content[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]{text-align:center;color:#fff}.connecting-content[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%]{width:3rem;height:3rem}.connecting-content[_ngcontent-%COMP%]   i.fa-exclamation-triangle[_ngcontent-%COMP%], .error-content[_ngcontent-%COMP%]   i.fa-exclamation-triangle[_ngcontent-%COMP%]{font-size:3rem}.video-container[_ngcontent-%COMP%]{position:relative;width:100%;height:100%}.remote-video-container[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;background:#1a1a1a}.remote-video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.local-video-container[_ngcontent-%COMP%]{position:absolute;top:20px;right:20px;width:240px;height:180px;background:#333;border-radius:12px;overflow:hidden;border:2px solid rgba(255,255,255,.2);z-index:100;transition:all .3s ease}.local-video-container.screen-sharing[_ngcontent-%COMP%]{width:180px;height:135px}.local-video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover}.video-placeholder[_ngcontent-%COMP%]{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:#2a2a2a;color:#888}.video-placeholder.remote-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]{text-align:center}.video-placeholder.remote-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:4rem;margin-bottom:1rem}.video-placeholder.remote-placeholder[_ngcontent-%COMP%]   .placeholder-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:1.2rem;margin:0}.video-placeholder.local-placeholder[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem}.video-status[_ngcontent-%COMP%]{position:absolute;bottom:8px;left:8px;display:flex;gap:4px}.status-indicator[_ngcontent-%COMP%]{background:rgba(0,0,0,.7);color:#fff;padding:4px 6px;border-radius:4px;font-size:.8rem}.status-indicator.video-off[_ngcontent-%COMP%], .status-indicator.audio-off[_ngcontent-%COMP%]{background:rgba(220,53,69,.9)}.top-bar[_ngcontent-%COMP%]{position:absolute;top:0;left:0;right:0;background:linear-gradient(to bottom,rgba(0,0,0,.7),transparent);padding:20px;display:flex;justify-content:space-between;align-items:center;color:#fff;opacity:0;transform:translateY(-20px);transition:all .3s ease;z-index:200}.top-bar.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.consultation-info[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin:0;font-weight:600}.consultation-info[_ngcontent-%COMP%]   small[_ngcontent-%COMP%]{opacity:.8}.top-actions[_ngcontent-%COMP%]{display:flex;gap:8px}.top-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{border-radius:8px}.top-actions[_ngcontent-%COMP%]   .btn.active[_ngcontent-%COMP%]{background:rgba(255,255,255,.2)}.bottom-controls[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;background:linear-gradient(to top,rgba(0,0,0,.7),transparent);padding:20px;display:flex;justify-content:space-between;align-items:center;opacity:0;transform:translateY(20px);transition:all .3s ease;z-index:200}.bottom-controls.visible[_ngcontent-%COMP%]{opacity:1;transform:translateY(0)}.control-buttons[_ngcontent-%COMP%]{display:flex;gap:12px;justify-content:center;flex:1}.control-btn[_ngcontent-%COMP%]{width:50px;height:50px;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.2rem;border:2px solid rgba(255,255,255,.3);transition:all .3s ease}.control-btn[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.control-btn.btn-success[_ngcontent-%COMP%]{background:#198754;border-color:#198754}.control-btn.btn-danger[_ngcontent-%COMP%]{background:#dc3545;border-color:#dc3545}.control-btn.btn-primary[_ngcontent-%COMP%]{background:#0d6efd;border-color:#0d6efd}.control-btn.btn-outline-light[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);color:#fff}.control-btn.btn-outline-light.active[_ngcontent-%COMP%]{background:rgba(255,255,255,.2)}.connection-status[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;color:#fff;font-size:.9rem}.connection-status[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%]{opacity:.8}.connection-status[_ngcontent-%COMP%]   .status-indicator[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;background:#6c757d}.connection-status[_ngcontent-%COMP%]   .status-indicator.connected[_ngcontent-%COMP%]{background:#198754}.connection-status[_ngcontent-%COMP%]   .status-indicator.connecting[_ngcontent-%COMP%]{background:#ffc107;animation:_ngcontent-%COMP%_pulse 1.5s ease-in-out infinite}.connection-status[_ngcontent-%COMP%]   .status-indicator.error[_ngcontent-%COMP%]{background:#dc3545}.participants-panel[_ngcontent-%COMP%], .chat-panel[_ngcontent-%COMP%]{position:absolute;top:0;right:-350px;width:350px;height:100%;background:rgba(0,0,0,.9);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-left:1px solid rgba(255,255,255,.1);transition:right .3s ease;z-index:300}.participants-panel.open[_ngcontent-%COMP%], .chat-panel.open[_ngcontent-%COMP%]{right:0}.panel-header[_ngcontent-%COMP%]{padding:20px;border-bottom:1px solid rgba(255,255,255,.1);display:flex;justify-content:space-between;align-items:center;color:#fff}.panel-header[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin:0;font-weight:600}.panel-content[_ngcontent-%COMP%]{padding:20px;height:calc(100% - 80px);overflow-y:auto}.participant-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;padding:12px 0;border-bottom:1px solid rgba(255,255,255,.1);color:#fff}.participant-item[_ngcontent-%COMP%]:last-child{border-bottom:none}.participant-avatar[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:1.2rem}.participant-info[_ngcontent-%COMP%]{flex:1}.participant-name[_ngcontent-%COMP%]{font-weight:600;margin-bottom:2px}.participant-role[_ngcontent-%COMP%]{font-size:.85rem;opacity:.7}.participant-status[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:.7rem}.messages-container[_ngcontent-%COMP%]{height:calc(100% - 60px);overflow-y:auto;margin-bottom:20px}.message-item[_ngcontent-%COMP%]{margin-bottom:16px}.message-item.own-message[_ngcontent-%COMP%]{text-align:right}.message-item.own-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:#0d6efd;margin-left:40px}.message-item[_ngcontent-%COMP%]:not(.own-message)   .message-content[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);margin-right:40px}.message-content[_ngcontent-%COMP%]{display:inline-block;padding:12px 16px;border-radius:12px;color:#fff;max-width:80%}.message-text[_ngcontent-%COMP%]{margin-bottom:4px}.message-time[_ngcontent-%COMP%]{font-size:.75rem;opacity:.7}.no-messages[_ngcontent-%COMP%]{text-align:center;padding:40px 20px;color:#888}.no-messages[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-size:2rem;margin-bottom:12px}.message-input[_ngcontent-%COMP%]{position:absolute;bottom:20px;left:20px;right:20px}.message-input[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]{background:rgba(255,255,255,.1);border:1px solid rgba(255,255,255,.2);color:#fff}.message-input[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]::placeholder{color:#ffffff80}.message-input[_ngcontent-%COMP%]   .input-group[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus{background:rgba(255,255,255,.15);border-color:#0d6efd;box-shadow:0 0 0 .2rem #0d6efd40;color:#fff}@keyframes _ngcontent-%COMP%_pulse{0%,to{opacity:1}50%{opacity:.5}}@media (max-width: 768px){.local-video-container[_ngcontent-%COMP%]{width:120px;height:90px;top:10px;right:10px}.participants-panel[_ngcontent-%COMP%], .chat-panel[_ngcontent-%COMP%]{width:100%;right:-100%}.control-buttons[_ngcontent-%COMP%]{gap:8px}.control-btn[_ngcontent-%COMP%]{width:45px;height:45px;font-size:1rem}.top-bar[_ngcontent-%COMP%], .bottom-controls[_ngcontent-%COMP%]{padding:15px}}"]})}}return i})()}]}];let bt=(()=>{class i{static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=t.$C({type:i})}static{this.\u0275inj=t.G2t({imports:[b.iI.forChild(ft),b.iI]})}}return i})();var Ct=u(3887);let _t=(()=>{class i{static{this.\u0275fac=function(n){return new(n||i)}}static{this.\u0275mod=t.$C({type:i})}static{this.\u0275inj=t.G2t({imports:[h.MD,S.YN,S.X1,b.iI,bt,Ct.G]})}}return i})()}}]);